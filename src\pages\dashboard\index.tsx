import { Label } from 'devextreme-react/chart';
import { formatter, formatterMillion } from './methods';

export { Dashboard } from './dashboard';
export { Row2Table as ProjectDeploymentStatusReportTable } from './row-2-table';

export const RowLabel = ({ formatMillion = false }: { formatMillion?: boolean }) => {
  return (
    <Label
      visible={true}
      customizeText={formatMillion ? formatterMillion : formatter}
      rotationAngle={90}
      overlappingBehavior="none"
    />
  );
};
