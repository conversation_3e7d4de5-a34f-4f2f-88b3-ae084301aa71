import { ErrorMessage } from '@/components/ui/error-message';
import { QUERIES, TABLES } from '@/constant';
import {
  defaultValuesFinancialSettlementReport,
  FinancialSettlementReport,
  FinancialSettlementReportInvestmentCost,
  IUserPermission,
} from '@/types';
import { useFormContext, useWatch } from 'react-hook-form';

import { CellContext, ColumnDef } from '@tanstack/react-table';

import { DataTable, EditableDropdownCell, EditableInputCell } from '@/components/data-table';
import { translationWithNamespace } from '@/lib/i18nUtils';
import { getRandomNumber } from '@/lib/number';
import { useMemo } from 'react';

const [defaultRow] =
  defaultValuesFinancialSettlementReport.financialSettlementReportInvestmentCosts;

type FinancialSettlementReportInvestmentCostEditableTableProps = {
  role?: IUserPermission;
  calculateForm?: () => void;
};

const t = translationWithNamespace('financialSettlementReport');

export const FinancialSettlementReportInvestmentCostEditableTable = ({
  role,
  calculateForm,
}: FinancialSettlementReportInvestmentCostEditableTableProps) => {
  const {
    setValue,
    control,
    formState: { errors },
  } = useFormContext<FinancialSettlementReport>();

  const [editableData] = useWatch({
    control,
    name: ['financialSettlementReportInvestmentCosts'],
  });

  const financialSettlementReportInvestmentCostEditableColumns: ColumnDef<FinancialSettlementReportInvestmentCost>[] =
    useMemo(
      () => [
        {
          id: 'costItemTypeId', // Nội dung chi phí
          accessorKey: 'costItemTypeId',
          header: t('fields.financialSettlementReportInvestmentCosts.costItemTypeId'),
          cell: (props: CellContext<FinancialSettlementReportInvestmentCost, unknown>) => (
            <EditableDropdownCell
              {...props}
              model="cost-item-type"
              queryKey={[QUERIES.COST_ITEM_TYPE]}
            />
          ),
        },

        {
          id: 'approvedFinalProjectBudget', // Tổng mức đầu tư của dự án (dự án thành phần, tiểu dự án độc lập) hoặc dự toán (công trình, hạng mục công trình) được phê duyệt hoặc điều chỉnh lần cuối
          accessorKey: 'approvedFinalProjectBudget',
          header: t('fields.financialSettlementReportInvestmentCosts.approvedFinalProjectBudget'),
          cell: props => <EditableInputCell {...props} type="number" isMoney />,
        },

        {
          id: 'proposedSettlementValue', // Giá trị đề nghị quyết toán
          accessorKey: 'proposedSettlementValue',
          header: t('fields.financialSettlementReportInvestmentCosts.proposedSettlementValue'),
          cell: props => <EditableInputCell {...props} type="number" isMoney />,
        },

        {
          id: 'proposedLiquidationAmount', // Giá trị tăng giảm
          accessorKey: 'proposedLiquidationAmount',
          header: t('fields.financialSettlementReportInvestmentCosts.proposedLiquidationAmount'),
          cell: props => <EditableInputCell {...props} type="number" isMoney />,
        },
      ],
      []
    );

  return (
    <div>
      <DataTable
        tableId={TABLES.FINANCIAL_SETTLEMENT_REPORT_INVESTMENT_COSTS}
        sortColumn="id"
        role={role}
        editableData={editableData}
        setEditableData={editedData => {
          setValue('financialSettlementReportInvestmentCosts', editedData);
          calculateForm?.();
        }}
        onAddButtonClick={table => {
          const newRow = { ...defaultRow, id: -getRandomNumber() };
          table.options.meta?.addNewRow(newRow);
        }}
        syncQueryParams={false}
        columns={financialSettlementReportInvestmentCostEditableColumns}
        customToolbar={() => {
          return (
            <>
              {errors.financialSettlementReportInvestmentCosts?.message && (
                <ErrorMessage message={errors.financialSettlementReportInvestmentCosts?.message} />
              )}
            </>
          );
        }}
      />
    </div>
  );
};
