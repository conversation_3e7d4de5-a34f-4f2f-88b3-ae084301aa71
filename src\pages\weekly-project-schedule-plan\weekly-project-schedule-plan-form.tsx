import { zod<PERSON><PERSON>olver } from '@hookform/resolvers/zod';
import { SyntheticEvent, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useParams } from 'react-router-dom';

import { DeleteConfirmDialog } from '@/components/confirm-dialog';
import { PageLayout } from '@/components/page-layout';
import { PeriodFilter } from '@/components/period-filter-form';
import { Form, FormCombobox, FormField, FormLabel } from '@/components/ui/form';
// import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { enterLabel, MUTATE, PATHS, PERMISSIONS, QUERIES, selectLabel } from '@/constant';
import { useAuth, useDataTable, useFormHandler, useFormOperation, usePermission } from '@/hooks';
import { useFormNavigate } from '@/hooks/use-form-navigate';
import { toDateType, toLocaleDate } from '@/lib/date';
import { createMutationSuccessFn } from '@/lib/i18nUtils';
import {
  createDeleteMutateFn,
  createPostMutateFn,
  createPutMutateFn,
  createQueryByIdFn,
} from '@/services';
import {
  WeeklyProjectSchedulePlan,
  weeklyProjectSchedulePlanSchema,
  defaultValuesWeeklyProjectSchedulePlan,
} from '@/types';
import { DateBox, TextBox } from 'devextreme-react';
import Button from 'devextreme-react/button';
import { Textarea } from '@/components/ui/textarea';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { LazyLoadedTable } from './lazy-loaded-table';

const onWeeklyProjectSchedulePlanMutationSuccess = createMutationSuccessFn(
  'weeklyProjectSchedulePlan'
);

export const WeeklyProjectSchedulePlanForm = () => {
  const { id: editId } = useParams();

  const { t } = useTranslation('weeklyProjectSchedulePlan');
  const [activeTab, setActiveTab] = useState<string>('detail');

  const role = usePermission(PERMISSIONS.WEEKLY_PROJECT_SCHEDULE_PLAN);
  const { user } = useAuth();

  const { goBackToList, goToUpdate, goToNew } = useFormNavigate(PATHS.WEEKLY_PROJECT_SCHEDULE_PLAN);

  const defaultValues = useMemo(
    () => ({
      ...defaultValuesWeeklyProjectSchedulePlan,
      userCreatedId: user?.userId,
    }),
    [user?.userId]
  );

  const { handleSubmit, loading, methods } = useFormHandler<WeeklyProjectSchedulePlan>({
    queryKey: [MUTATE.WEEKLY_PROJECT_SCHEDULE_PLAN, editId],
    mutateKey: [MUTATE.WEEKLY_PROJECT_SCHEDULE_PLAN],
    queryId: Number(editId) || 0,
    invalidateKey: [QUERIES.WEEKLY_PROJECT_SCHEDULE_PLAN],
    readFn: createQueryByIdFn<WeeklyProjectSchedulePlan>('weekly-project-schedule-plan'),
    createFn: createPostMutateFn<WeeklyProjectSchedulePlan>('weekly-project-schedule-plan'),
    updateFn: createPutMutateFn<WeeklyProjectSchedulePlan>('weekly-project-schedule-plan'),
    formatPayloadFn: data => ({
      ...data,
      storeId: data.storeId === null ? 0 : data.storeId,
      weeklyProjectSchedulePlanTime: toLocaleDate(data.weeklyProjectSchedulePlanTime!),
      budgetYear: toLocaleDate(data.budgetYear!),
    }),
    formatResponseFn: data => ({
      ...data,
      weeklyProjectSchedulePlanTime: toDateType(data.weeklyProjectSchedulePlanTime!),
      budgetYear: toDateType(data.budgetYear!),
    }),
    onCreateSuccess: data => {
      onWeeklyProjectSchedulePlanMutationSuccess(data);
      goToUpdate(data);
    },
    onUpdateSuccess: onWeeklyProjectSchedulePlanMutationSuccess,
    formOptions: {
      resolver: zodResolver(weeklyProjectSchedulePlanSchema),
      defaultValues,
    },
  });

  const {
    isDeleting,
    deleteTarget,
    selectTargetToDelete,
    toggleConfirmDeleteDialog,
    isConfirmDeleteDialogOpen,
  } = useDataTable<WeeklyProjectSchedulePlan, PeriodFilter>({
    deleteFn: createDeleteMutateFn('weekly-project-schedule-plan'),
    deleteKey: [MUTATE.WEEKLY_PROJECT_SCHEDULE_PLAN],
  });
  const { reset, onTimeChange } = useFormOperation<WeeklyProjectSchedulePlan>({
    model: 'weekly-project-schedule-plan',
    fieldTime: 'weeklyProjectSchedulePlanTime',
    createCodeKey: [QUERIES.WEEKLY_PROJECT_SCHEDULE_PLAN],
    formMethods: methods,
  });

  const onCreateNew = () => {
    goToNew();
    methods.reset(defaultValues);
    reset();
  };

  const onDelete = () => {
    selectTargetToDelete(methods.getValues());
  };

  return (
    <>
      <Form {...methods}>
        <form autoComplete="off">
          <PageLayout
            onSaveChange={e => {
              handleSubmit(e.event?.currentTarget as unknown as SyntheticEvent<HTMLElement>);
            }}
            header={editId !== 'new' ? t('page.form.edit') : t('page.form.addNew')}
            canSaveChange={!isNaN(Number(editId)) ? role?.isUpdate : role?.isCreate}
            isSaving={loading}
            onCancel={goBackToList}
            onDelete={editId !== 'new' ? onDelete : undefined}
            customElementLeft={
              <>
                <Button
                  text={t('content.createNew', { ns: 'common' })}
                  className="uppercase"
                  stylingMode="outlined"
                  type="default"
                  icon="plus"
                  onClick={onCreateNew}
                />
              </>
            }
          >
            <div className="grid max-w-screen-2xl grid-cols-1 gap-x-8 gap-y-4 md:grid-cols-24">
              <div className="col-span-1 flex flex-col gap-x-8 gap-y-4 md:col-span-10 2xl:col-span-6">
                <div className="flex flex-row items-center ">
                  <FormLabel
                    name="budgetYear"
                    htmlFor="budgetYear"
                    className="hidden w-[60px] md:block md:w-[85px]"
                  >
                    {t('fields.budgetYear')}
                  </FormLabel>
                  <FormField
                    label={t('fields.budgetYear')}
                    id="budgetYear"
                    name="budgetYear"
                    className="w-full min-w-0 flex-1"
                  >
                    <DateBox
                      placeholder={`${selectLabel} ${t('fields.budgetYear')}`}
                      displayFormat={'year'}
                      calendarOptions={{
                        maxZoomLevel: 'decade',
                        minZoomLevel: 'decade',
                      }}
                      pickerType="calendar"
                      focusStateEnabled={false}
                    />
                  </FormField>
                </div>
                <div className="flex flex-row items-center ">
                  <FormLabel
                    htmlFor="totalNumberProject"
                    className="hidden w-[60px] md:block md:w-[85px]"
                  >
                    {t('fields.totalNumberProject')}
                  </FormLabel>
                  <FormField
                    id="totalNumberProject"
                    name="totalNumberProject"
                    className="w-full flex-1"
                    label={t('fields.totalNumberProject')}
                  >
                    <TextBox
                      disabled
                      // placeholder={`${enterLabel} ${t('fields.totalNumberProject')}`}
                    />
                  </FormField>
                </div>
              </div>
              <div className="col-span-1 flex flex-col gap-x-8 gap-y-4 md:col-span-10 2xl:col-span-6">
                <div className="flex flex-row items-center ">
                  <FormLabel htmlFor="code" className="hidden w-[60px] md:block md:w-[85px]">
                    {t('fields.code')}
                  </FormLabel>
                  <FormField
                    id="code"
                    name="code"
                    className="w-full min-w-0 flex-1"
                    label={t('fields.code')}
                  >
                    <TextBox disabled placeholder={`${enterLabel} ${t('fields.code')}`} />
                  </FormField>
                </div>
                <div className="flex flex-row items-center ">
                  <FormLabel
                    name="weeklyProjectSchedulePlanTime"
                    htmlFor="weeklyProjectSchedulePlanTime"
                    className="hidden w-[60px] md:block md:w-[85px]"
                  >
                    {t('fields.weeklyProjectSchedulePlanTime')}
                  </FormLabel>
                  <FormField
                    id="weeklyProjectSchedulePlanTime"
                    name="weeklyProjectSchedulePlanTime"
                    className="w-full min-w-0 flex-1"
                    type="date"
                    onChange={e => {
                      onTimeChange(e.target.value);
                    }}
                    label={t('fields.weeklyProjectSchedulePlanTime')}
                  >
                    <DateBox
                      disabled
                      placeholder={`${selectLabel} ${t('fields.weeklyProjectSchedulePlanTime')}`}
                      pickerType="calendar"
                      focusStateEnabled={false}
                    />
                  </FormField>
                </div>
                <div className="flex flex-row items-center ">
                  <FormLabel
                    htmlFor="userCreatedId"
                    className="hidden w-[60px] md:block md:w-[85px]"
                  >
                    {t('fields.userCreatedId')}
                  </FormLabel>
                  <FormField
                    id="userCreatedId"
                    name="userCreatedId"
                    className="w-full min-w-0 flex-1"
                    label={t('fields.userCreatedId')}
                  >
                    <FormCombobox
                      placeholder={`${selectLabel} ${t('fields.userCreatedId')}`}
                      model="user"
                      queryKey={[QUERIES.USERS]}
                      disabled
                    />
                  </FormField>
                </div>
              </div>
              <div className="col-span-12 hidden h-2 2xl:block" />
              <div className="col-span-1 flex flex-col gap-x-8 gap-y-4 md:col-span-20 2xl:col-span-12">
                <div className="flex flex-row items-center ">
                  <FormLabel htmlFor="note" className="hidden w-[60px] md:block md:w-[85px]">
                    {t('fields.note')}
                  </FormLabel>
                  <FormField
                    id="note"
                    name="note"
                    className="w-full flex-1"
                    label={t('fields.note')}
                  >
                    <Textarea placeholder={`${enterLabel} ${t('fields.note')}`} />
                  </FormField>
                </div>
              </div>
            </div>

            <div className="mt-8">
              <Tabs
                defaultValue="detail"
                onValueChange={value => {
                  setActiveTab(value);
                  // Force re-render of the table when tab is selected
                  if (value === 'detail') {
                    // This is a no-op but will trigger a re-render
                    methods.setValue('totalNumberProject', methods.getValues('totalNumberProject'));
                  }
                }}
              >
                <div className="w-full">
                  <TabsList>
                    <TabsTrigger value="detail">{t('page.tabs.detail')}</TabsTrigger>
                  </TabsList>
                </div>
                <TabsContent value="detail" className="mt-4">
                  {activeTab === 'detail' && <LazyLoadedTable role={role} />}
                </TabsContent>
              </Tabs>
            </div>
            {/* <div style={{ display: "none" }}>{printoutElement}</div> */}
          </PageLayout>
        </form>
      </Form>
      <DeleteConfirmDialog
        model="weekly-project-schedule-plan"
        name={methods.getValues('note')!}
        open={isConfirmDeleteDialogOpen}
        toggle={toggleConfirmDeleteDialog}
        isDeleting={isDeleting}
        onConfirm={() => {
          deleteTarget();
          setTimeout(() => onCreateNew(), 0);
        }}
      />
    </>
  );
};
