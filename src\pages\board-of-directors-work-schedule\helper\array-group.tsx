import { Department, Position, User } from '@/types';

// Type cho chi tiết lịch làm việc đầu vào (raw input)
// Sử dụng departmentIdArray và memberIdArray là mảng số
export interface WorkScheduleDetailInput {
  chairpersonName: string | null; // <PERSON><PERSON> thể vẫn giữ lại nếu cần hiển thị ngay cả khi chairpersonId là null
  contentPreparerName: string | null;
  id: number;
  boardOfDirectorsWorkScheduleId: number;
  workDate: string;
  session: number;
  workTime: string;
  content: string | null;
  chairpersonId: number | null; // ID để phân giải thành đối tượng Member
  departmentIdArray: number[] | null; // Mảng các ID phòng ban
  memberIdArray: number[] | null; // Mảng các ID thành viên tham gia
  contentPreparerId: number | null;
  location: string | null;
  chairpersonOther: string | null;
  memberOther: string | null;
  departmentOther: string | null;
  // C<PERSON><PERSON> trường departmentIds, memberIds (string) từ dữ liệu mẫu gốc sẽ bị bỏ qua
  // nếu chúng ta thống nhất dùng departmentIdArray và memberIdArray
  departmentIds?: string | null; // Bỏ qua, dùng departmentIdArray
  memberIds?: string | null; // Bỏ qua, dùng memberIdArray
  [key: string]: any;
}

// Type cho chi tiết lịch làm việc đã được xử lý và làm sạch
interface ProcessedWorkScheduleDetail {
  id: number;
  boardOfDirectorsWorkScheduleId: number;
  workDate: string;
  session: number;
  workTime: string;
  content: string | null;
  chairpersonId: number | null;
  contentPreparerId: number | null;
  contentPreparerName: string | null;
  location: string | null;
  chairpersonOther: string | null;
  memberOther: string | null;
  departmentOther: string | null;

  // Các thực thể đã được phân giải và làm sạch
  chairpersonResolved: SlimMember | null; // Đối tượng chủ trì đã phân giải
  departmentsResolved: Department[]; // Mảng các đối tượng Department tham gia
  membersResolved: User[]; // Mảng các đối tượng Member tham gia

  // Giữ lại chairpersonName gốc nếu cần, hoặc có thể lấy từ chairpersonResolved.name
  chairpersonNameOriginal: string | null;
  [key: string]: any;
}

// Type cho nhóm cấp hai (theo session)
interface SessionGroup {
  session: number;
  details: ProcessedWorkScheduleDetail[];
  rowspan: number;
}

// Type cho nhóm cấp một (theo workDate)
interface DateGroup {
  workDate: string;
  sessions: SessionGroup[];
  rowspan: number;
}

interface SlimMember {
  id: number;
  name: string;
  positionName: string; // THÊM thuộc tính này
}

// Type cho kết quả nhóm cuối cùng
type GroupedWorkScheduleOutput = DateGroup[];

/**
 * Loại bỏ ký tự xuống dòng và các khoảng trắng thừa từ một chuỗi.
 */
function cleanString(str: string | null): string | null {
  if (str === null || str === undefined) return null;
  if (typeof str === 'string') {
    return str
      .replace(/(\r\n|\n|\r)/gm, '<br>')
      .replace(/\s+/g, ' ')
      .trim();
  }
  return str; // Trả về nguyên gốc nếu không phải chuỗi hoặc đã là null/undefined
}

/**
 * Làm sạch tất cả các thuộc tính chuỗi bên trong một đối tượng (generic).
 */
function cleanStringPropertiesInObject<T extends Record<string, any>>(obj: T | null): T | null {
  if (obj === null || obj === undefined) return null;
  const cleanedObj = { ...obj }; // Tạo bản sao để không thay đổi đối tượng gốc
  for (const key in cleanedObj) {
    if (Object.prototype.hasOwnProperty.call(cleanedObj, key)) {
      if (typeof cleanedObj[key] === 'string') {
        cleanedObj[key] = cleanString(cleanedObj[key] as string) as any;
      }
    }
  }
  return cleanedObj;
}

/**
 * Xử lý một chi tiết lịch làm việc: làm sạch chuỗi và phân giải IDs.
 */
function processDetailItem(
  detailRaw: WorkScheduleDetailInput,
  departmentMap: Map<number, Department>,
  memberMap: Map<number, User>,
  positionMap: Map<number, Position>
): ProcessedWorkScheduleDetail {
  // Tạo đối tượng processed với các thuộc tính cơ bản và làm sạch chuỗi
  const processed: Partial<ProcessedWorkScheduleDetail> = {
    id: detailRaw.id,
    boardOfDirectorsWorkScheduleId: detailRaw.boardOfDirectorsWorkScheduleId,
    workDate: cleanString(detailRaw.workDate)!, // workDate không nên null
    session: detailRaw.session,
    workTime: cleanString(detailRaw.workTime)!, // workTime không nên null
    content: cleanString(detailRaw.content),
    chairpersonId: detailRaw.chairpersonId,
    contentPreparerId: detailRaw.contentPreparerId,
    contentPreparerName: cleanString(detailRaw.contentPreparerName),
    location: cleanString(detailRaw.location),
    chairpersonOther: cleanString(detailRaw.chairpersonOther),
    memberOther: cleanString(detailRaw.memberOther),
    departmentOther: cleanString(detailRaw.departmentOther),
    chairpersonNameOriginal: cleanString(detailRaw.chairpersonName), // Giữ lại tên gốc
  };

  // Phân giải chairpersonId
  processed.chairpersonResolved = null;
  if (detailRaw.chairpersonId !== null) {
    const fullChairperson = memberMap.get(detailRaw.chairpersonId);
    if (fullChairperson) {
      let chairpersonPositionName = '';
      if (fullChairperson.positionId !== null) {
        const position = positionMap.get(fullChairperson.positionId ?? 0);
        if (position) {
          chairpersonPositionName = cleanString(position.name) || '';
        }
      }
      processed.chairpersonResolved = {
        id: fullChairperson.id,
        name: cleanString(fullChairperson?.name) || '',
        positionName: chairpersonPositionName, // Gán positionName
      };
    }
  }

  // Phân giải departmentIdArray
  processed.departmentsResolved = [];
  if (Array.isArray(detailRaw.departmentIdArray)) {
    detailRaw.departmentIdArray.forEach(id => {
      if (typeof id === 'number' && !isNaN(id) && id !== 0) {
        // Bỏ qua ID 0 nếu nó không có ý nghĩa
        const department = departmentMap.get(id);
        if (department) {
          processed.departmentsResolved!.push(cleanStringPropertiesInObject(department)!);
        }
      }
    });
  }

  // Phân giải memberIdArray
  processed.membersResolved = [];
  if (Array.isArray(detailRaw.memberIdArray) && processed.departmentsResolved.length === 0) {
    detailRaw.memberIdArray.forEach(id => {
      if (typeof id === 'number' && !isNaN(id) && id !== 0) {
        // Bỏ qua ID 0 nếu nó không có ý nghĩa
        const member = memberMap.get(id);
        if (member) {
          processed.membersResolved!.push(cleanStringPropertiesInObject(member)!);
        }
      }
    });
  }
  return processed as ProcessedWorkScheduleDetail;
}

/**
 * Nhóm chi tiết lịch làm việc theo workDate và session,
 * đồng thời phân giải IDs và tính toán rowspan.
 */
export function groupBoardOfDirectorsSchedule(
  detailsArray: any[],
  allDepartments: Department[],
  allMembers: User[],
  allPositions: Position[]
): GroupedWorkScheduleOutput {
  if (!detailsArray || detailsArray.length === 0) {
    return [];
  }

  const departmentMap = new Map<number, Department>();
  allDepartments.forEach(dept => departmentMap.set(dept.id, dept));

  const memberMap = new Map<number, User>();
  allMembers.forEach(member => memberMap.set(member.id, member));

  const positionMap = new Map<number, Position>();
  allPositions.forEach(pos => positionMap.set(pos.id, pos));

  const groupedByDate: Record<string, Record<number, ProcessedWorkScheduleDetail[]>> = {};

  detailsArray.forEach(detailRaw => {
    // eslint-disable-next-line @typescript-eslint/no-unsafe-argument
    const processedDetail = processDetailItem(detailRaw, departmentMap, memberMap, positionMap);
    const { workDate, session } = processedDetail;

    if (!workDate) {
      // Bỏ qua nếu workDate không hợp lệ sau khi làm sạch
      console.warn('Skipping detail item due to invalid workDate:', detailRaw);
      return;
    }

    if (!groupedByDate[workDate]) {
      groupedByDate[workDate] = {};
    }
    if (!groupedByDate[workDate][session]) {
      groupedByDate[workDate][session] = [];
    }
    groupedByDate[workDate][session].push(processedDetail);
  });

  const result: GroupedWorkScheduleOutput = Object.keys(groupedByDate)
    .sort((a, b) => new Date(a).getTime() - new Date(b).getTime())
    .map(date => {
      const sessionsForDate = groupedByDate[date];
      let dateTotalRowspan = 0;

      const sessionGroups: SessionGroup[] = Object.keys(sessionsForDate)
        .map(sessionStr => parseInt(sessionStr, 10))
        .sort((a, b) => a - b)
        .map(sessionKey => {
          const currentSessionDetails = sessionsForDate[sessionKey];
          const sessionRowspan = currentSessionDetails.length;
          dateTotalRowspan += sessionRowspan;

          return {
            session: sessionKey,
            details: currentSessionDetails, // details đã được xử lý trong processDetailItem
            rowspan: sessionRowspan,
          };
        });

      return {
        workDate: date,
        sessions: sessionGroups,
        rowspan: dateTotalRowspan,
      };
    });

  console.log('🚀 ~ result:', result);
  return result;
}
