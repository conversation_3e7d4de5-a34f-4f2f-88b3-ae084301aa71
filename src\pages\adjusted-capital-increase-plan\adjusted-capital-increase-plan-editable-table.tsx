import { ErrorMessage } from '@/components/ui/error-message';
import { PROFESSIONS, QUERIES, TABLES } from '@/constant';
import {
  BudgetItemCode,
  BudgetSourceCode,
  FundingProgramCode,
  IUserPermission,
  Project,
  SectorCode,
  TypeCode,
} from '@/types';
import { useFormContext, useWatch } from 'react-hook-form';

import { CellContext, ColumnDef } from '@tanstack/react-table';

import { BasicDialog } from '@/components/basic-dialog';
import {
  DataTable,
  DataTableRowActions,
  EditableDropdownCell,
  EditableInputCell,
} from '@/components/data-table';
import { ImportExcelConfigForm } from '@/components/import-excel-config-form';
import { useBoolean, useEntity } from '@/hooks';
import { getRandomNumber } from '@/lib/number';
import { useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import {
  AdjustedCapitalIncreasePlan,
  AdjustedCapitalIncreasePlanDetail,
  defaultValuesAdjustedCapitalIncreasePlan,
} from '@/types/adjusted-capital-increase-plan';
const [defaultRow] = defaultValuesAdjustedCapitalIncreasePlan.adjustedCapitalIncreasePlanDetails;

type AdjustedCapitalIncreasePlanEditableTableProps = {
  role?: IUserPermission;
  calculateForm?: () => void;
  budgetYear: string | null | undefined;
  approvalNumber: string | null | undefined;
  approvalDate: string | null | undefined;
};
export const AdjustedCapitalIncreasePlanEditableTable = ({
  role,
  calculateForm,
  budgetYear,
  approvalNumber,
  approvalDate,
}: AdjustedCapitalIncreasePlanEditableTableProps) => {
  // const isMobile = useMediaQuery('(max-width: 768px)');
  const {
    setValue,
    control,
    formState: { errors },
  } = useFormContext<AdjustedCapitalIncreasePlan>();

  const [editableData] = useWatch({
    control,
    name: ['adjustedCapitalIncreasePlanDetails'],
  });

  const { t: translator } = useTranslation('adjustedCapitalIncreasePlan');
  const t = useCallback(
    (fieldName: string) => {
      return translator(fieldName, {
        budgetYear: budgetYear,
        approvalNumber: approvalNumber || '',
        approvalDate: approvalDate,
        interpolation: { escapeValue: false },
      });
    },
    [translator, budgetYear, approvalNumber, approvalDate]
  );

  const { state: isImportFormOpen, toggle: toggleImportForm } = useBoolean(false);

  const { fetch: fetchProjects } = useEntity<Project>({
    model: 'project',
    queryKey: [QUERIES.PROJECT],
  });

  const { fetch: fetchBudgetSourceCodes } = useEntity<BudgetSourceCode>({
    model: 'budget-source-code',
    queryKey: [QUERIES.BUDGET_SOURCE_CODE],
  });

  const { fetch: fetchFundingProgramCodes } = useEntity<FundingProgramCode>({
    model: 'funding-program-code',
    queryKey: [QUERIES.FUNDING_PROGRAM_CODE],
  });

  const { fetch: fetchSectorCodes } = useEntity<SectorCode>({
    model: 'sector-code',
    queryKey: [QUERIES.SECTOR_CODE],
  });

  const { fetch: fetchTypeCodes } = useEntity<TypeCode>({
    model: 'type-code',
    queryKey: [QUERIES.TYPE_CODE],
  });

  const { fetch: fetchBudgetItemCodes } = useEntity<BudgetItemCode>({
    model: 'budget-item-code',
    queryKey: [QUERIES.BUDGET_ITEM_CODE],
  });

  const adjustedCapitalIncreasePlanEditableColumns: ColumnDef<AdjustedCapitalIncreasePlanDetail>[] =
    useMemo(
      () => [
        {
          id: 'projectId', // Dự án
          accessorKey: 'projectId',
          header: t('fields.adjustedCapitalIncreasePlanDetails.projectId'),
          cell: (props: CellContext<AdjustedCapitalIncreasePlanDetail, unknown>) => (
            <EditableDropdownCell
              {...props}
              model="project"
              queryKey={[QUERIES.PROJECT]}
              defaultText={props.row.original.projectName}
              onSelectItem={(
                selectedProject:
                  | (Project & {
                      programCode?: number;
                      sectorCode?: number;
                    })
                  | null
              ) => {
                if (!selectedProject) {
                  return;
                }
                // thay đổi giá trị của ô Mã chương và mã ngành KT khi chọn dự án
                const updateRowValues = {
                  ...props.row.original,
                  projectId: selectedProject.id,
                  programCode: selectedProject.programCode,
                  sectorCode: selectedProject.sectorCode,
                };
                props.table.options.meta?.updateRowValues(updateRowValues, props.row.index);
              }}
            />
          ),
        },

        {
          id: 'budgetSourceCodeId', // Mã nguồn NS
          accessorKey: 'budgetSourceCodeId',
          header: t('fields.adjustedCapitalIncreasePlanDetails.budgetSourceCodeId'),
          cell: (props: CellContext<AdjustedCapitalIncreasePlanDetail, unknown>) => (
            <EditableDropdownCell
              {...props}
              model="budget-source-code"
              queryKey={[QUERIES.BUDGET_SOURCE_CODE]}
              defaultText={props.row.original.budgetSourceCodeName}
            />
          ),
        },

        {
          id: 'fundingProgramCodeId', // Mã chương
          accessorKey: 'fundingProgramCodeId',
          header: t('fields.adjustedCapitalIncreasePlanDetails.fundingProgramCodeId'),
          cell: props => (
            <EditableDropdownCell
              {...props}
              model="funding-program-code"
              queryKey={[QUERIES.FUNDING_PROGRAM_CODE]}
              defaultText={props.row.original.fundingProgramCodeId?.toString()}
            />
          ),
        },

        // {
        //   id: 'sectorCodeId', // Mã ngành KT
        //   accessorKey: 'sectorCodeId',
        //   header: t('fields.capitalIncreasePlanDetails.sectorCodeId'),
        //   cell: props => (
        //     <EditableDropdownCell
        //       {...props}
        //       model="sector-code"
        //       queryKey={[QUERIES.SECTOR_CODE]}
        //       defaultText={props.row.original.sectorCodeId?.toString()}
        //     />
        //   ),
        // },

        {
          id: 'typeCodeId', // Loại
          accessorKey: 'typeCodeId',
          header: t('fields.adjustedCapitalIncreasePlanDetails.typeCodeId'),
          cell: props => (
            <EditableDropdownCell
              {...props}
              model="type-code"
              queryKey={[QUERIES.TYPE_CODE]}
              defaultText={props.row.original.typeCodeId?.toString()}
            />
          ),
        },

        {
          id: 'budgetItemCodeId', // Khoản
          accessorKey: 'budgetItemCodeId',
          header: t('fields.adjustedCapitalIncreasePlanDetails.budgetItemCodeId'),
          cell: props => (
            <EditableDropdownCell
              {...props}
              model="budget-item-code"
              queryKey={[QUERIES.BUDGET_ITEM_CODE]}
              defaultText={props.row.original.budgetItemCodeId?.toString()}
            />
          ),
        },

        // {
        //   id: 'yearlyPlan', // Số tiền
        //   accessorKey: 'yearlyPlan',
        //   header: t('fields.adjustedCapitalIncreasePlanDetails.yearlyPlan'),
        //   cell: props => (
        //     <EditableInputCell
        //       {...props}
        //       type="number"
        //       onValueChange={value => {
        //         props.table.options.meta?.updateData(
        //           props.row.index,
        //           props.column.id,
        //           Number(value)
        //         );
        //       }}
        //     />
        //   ),
        // },

        {
          id: 'assignedPlanYearly', // Số tiền
          accessorKey: 'assignedPlanYearly',
          header: t('fields.adjustedCapitalIncreasePlanDetails.assignedPlanYearly'),
          cell: props => (
            <EditableInputCell
              {...props}
              type="number"
              onValueChange={value => {
                const rowValue = props.row.original;
                const assignedPlanYearly = Number(value);
                const adjustmentProposal = rowValue.adjustmentProposal || 0;
                const totalAmount = assignedPlanYearly + adjustmentProposal;
                props.table.options.meta?.updateRowValues(
                  { ...rowValue, assignedPlanYearly, totalAmount },
                  props.row.index
                );
              }}
              readOnly
            />
          ),
        },
        {
          id: 'adjustmentProposal', // Số tiền
          accessorKey: 'adjustmentProposal',
          header: t('fields.adjustedCapitalIncreasePlanDetails.adjustmentProposal'),
          cell: props => (
            <EditableInputCell
              {...props}
              type="number"
              onValueChange={value => {
                if (isNaN(Number(value))) return;
                const rowValue = props.row.original;
                const assignedPlanYearly = rowValue.assignedPlanYearly || 0;
                const adjustmentProposal = Number(value);
                const totalAmount = adjustmentProposal + assignedPlanYearly;
                props.table.options.meta?.updateRowValues(
                  { ...rowValue, adjustmentProposal, totalAmount },
                  props.row.index
                );
              }}
              onChange={e => {
                console.log('e', e);
              }}
            />
          ),
        },
        {
          id: 'totalAmount', // Số tiền
          accessorKey: 'totalAmount',
          header: t('fields.adjustedCapitalIncreasePlanDetails.totalAmount'),
          cell: props => (
            <EditableInputCell
              {...props}
              type="number"
              onValueChange={value => {
                if (isNaN(Number(value))) return;
                const rowValue = props.row.original;
                const assignedPlanYearly = rowValue.assignedPlanYearly || 0;
                const totalAmount = Number(value);
                const adjustmentProposal = totalAmount - assignedPlanYearly;
                props.table.options.meta?.updateRowValues(
                  { ...rowValue, adjustmentProposal, totalAmount },
                  props.row.index
                );
              }}
            />
          ),
        },

        {
          id: 'removeRow',
          header: '',
          size: 10,

          cell: props => {
            return (
              <DataTableRowActions
                onDelete={() => {
                  props.table.options.meta?.removeRowByIndex(props.row.index);
                }}
                canDelete={role?.isCreate || role?.isUpdate}
              />
            );
          },
        },
      ],
      [role?.isCreate, role?.isUpdate, t]
    );

  const columnsForImportConfig = [...adjustedCapitalIncreasePlanEditableColumns].map(column => {
    return {
      field: column.id,
      header: column.header as string,
    };
  });

  const handleEditableDataChange = useCallback(
    (editedData: AdjustedCapitalIncreasePlanDetail[]) => {
      setValue('adjustedCapitalIncreasePlanDetails', editedData);
      calculateForm?.();
    },
    [calculateForm, setValue]
  );

  return (
    <div>
      <>
        <DataTable
          tableId={TABLES.ADJUSTED_CAPITAL_INCREASE_PLAN_DETAIL}
          sortColumn="id"
          role={role}
          editableData={editableData}
          setEditableData={handleEditableDataChange}
          onAddButtonClick={table => {
            const newRow = { ...defaultRow, id: -getRandomNumber() };
            table.options.meta?.addNewRow(newRow);
          }}
          syncQueryParams={false}
          columns={adjustedCapitalIncreasePlanEditableColumns}
          customToolbar={() => {
            return (
              <>
                {errors.adjustedCapitalIncreasePlanDetails?.message && (
                  <ErrorMessage message={errors.adjustedCapitalIncreasePlanDetails?.message} />
                )}
              </>
            );
          }}
        />
        <BasicDialog
          open={isImportFormOpen}
          title="Import Excel"
          toggle={toggleImportForm}
          className="max-w-[100vw] md:max-w-[90vw]"
        >
          <ImportExcelConfigForm<AdjustedCapitalIncreasePlanDetail>
            onApply={data => {
              setValue(
                'adjustedCapitalIncreasePlanDetails',
                data.map(item => ({
                  ...defaultRow,
                  ...item,
                }))
              );
              calculateForm?.();
              toggleImportForm();
            }}
            importModel="adjusted-capital-increase-plan"
            onClose={toggleImportForm}
            professionType={PROFESSIONS.ADJUSTED_CAPITAL_INCREASE_PLAN}
            professionColumns={columnsForImportConfig}
            onImported={() => {
              fetchProjects({});
              fetchBudgetSourceCodes({});
              fetchFundingProgramCodes({});
              fetchSectorCodes({});
              fetchTypeCodes({});
              fetchBudgetItemCodes({});
            }}
          />
        </BasicDialog>
      </>
    </div>
  );
};
