import { ProjectDetail } from '@/types';
import { CellContext } from '@tanstack/react-table';
import { useState } from 'react';
import { calculatePostTaxValues } from '@/pages/project';
import { EditableInputCell } from '@/components/data-table';
export const VatTaxCell = ({
  ...props
}: CellContext<ProjectDetail, unknown> & {
  isPmDirector?: boolean;
  isEditVat: boolean;
  updateSummary?: (
    costItemTypeId: number | null | undefined,
    row: ProjectDetail | null,
    data: ProjectDetail[]
  ) => void;
}) => {
  const [isUserInput, setIsUserInput] = useState(false);

  return (
    <EditableInputCell
      type="number"
      isMoney
      readOnly={!props.isEditVat}
      onFocus={() => setIsUserInput(true)}
      onBlur={() => setIsUserInput(false)}
      onValueChange={value => {
        if (!isUserInput) return; // Chỉ thực hiện tính toán khi người dùng đang nhập

        const vatTax = Number(value);
        const { original } = props.row;
        if (!original) return;

        const currentPreTaxValue = original.preTaxValue ?? 0;

        // Tính toán vat từ vatTax và preTaxValue
        const postTaxValue = calculatePostTaxValues(vatTax, currentPreTaxValue);

        const updatedRow = {
          ...original,
          ...postTaxValue,
        };

        props.table.options.meta?.updateRowValues(updatedRow, props.row.index);
        props?.updateSummary?.(
          original.costItemCostItemTypeId,
          updatedRow,
          props.table.options.data
        );
      }}
      {...props}
    />
  );
};
