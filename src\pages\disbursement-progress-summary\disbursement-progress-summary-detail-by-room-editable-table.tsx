import { ErrorMessage } from '@/components/ui/error-message';
import { TABLES } from '@/constant';
import {
  DisbursementProgressSummary,
  DisbursementProgressSummaryDetailByRoom,
  defaultValuesDisbursementProgressSummary,
  IUserPermission,
} from '@/types';
import { useFormContext, useWatch } from 'react-hook-form';

import { CellContext, ColumnDef } from '@tanstack/react-table';

import { DataTable, EditableInputCell } from '@/components/data-table';
import { getRandomNumber } from '@/lib/number';
import { useMemo } from 'react';
import { useTWithDefaultParams } from './utils';

const [defaultRow] =
  defaultValuesDisbursementProgressSummary.disbursementProgressSummaryDetailByRooms;

type DisbursementProgressSummaryEditableTableProps = {
  role?: IUserPermission;
  calculateForm?: () => void;
};

export const DisbursementProgressSummaryDetailByRoomEditableTable = ({
  role,
  calculateForm,
}: DisbursementProgressSummaryEditableTableProps) => {
  const {
    setValue,
    control,
    formState: { errors },
  } = useFormContext<DisbursementProgressSummary>();
  const [budgetYear, editableData] = useWatch({
    control,
    name: ['budgetYear', 'disbursementProgressSummaryDetailByRooms'],
  });

  const { t } = useTWithDefaultParams('disbursementProgressSummary', {
    year: budgetYear?.getFullYear() || 0,
  });

  const disbursementProgressSummaryEditableColumns: ColumnDef<DisbursementProgressSummaryDetailByRoom>[] =
    useMemo(() => {
      const renderInfo = (props: CellContext<DisbursementProgressSummaryDetailByRoom, unknown>) => {
        const { row, table } = props;
        const previousRow = table.getRowModel().rows[row.index - 1];
        const isFirstRowOfGroup =
          !previousRow || previousRow.original.roomId !== row.original.roomId;

        if (!isFirstRowOfGroup) {
          return <EditableInputCell {...props} disabled value={''} />;
        }

        if (props.column.id === 'plannedCapitalValue') {
          return <EditableInputCell {...props} disabled type="number" />;
        } else {
          return <EditableInputCell {...props} disabled />;
        }
      };
      const groupColumn: ColumnDef<DisbursementProgressSummaryDetailByRoom>[] = [
        {
          id: 'roomIdplannedCapitalValuecontent',
          header: 'Bảng tóm tắt tiến độ giải ngân theo phòng',
          columns: [
            {
              id: 'roomName', // Phòng
              accessorKey: 'roomName',
              header: '',
              cell: renderInfo,
            },

            {
              id: 'plannedCapitalValue', // Giá trị kế hoạch vốn
              accessorKey: 'plannedCapitalValue',
              header: t('fields.disbursementProgressSummaryDetailByRooms.totalPlan'),
              cell: renderInfo,
            },

            {
              id: 'content', // Nội dung
              accessorKey: 'content',
              header: '',
              cell: props => <EditableInputCell {...props} disabled />,
            },
          ],
        },
      ];
      const renderValue = (
        props: CellContext<DisbursementProgressSummaryDetailByRoom, unknown>
      ) => {
        const { row } = props;
        if (props.getValue() === null) {
          return <EditableInputCell {...props} type="text" disabled value={''} />;
        }
        if (row.original.content === 'TL') {
          return (
            <EditableInputCell
              {...props}
              type="number"
              disabled
              value={`${(props.getValue() as number).toLocaleString('vi-VN', { maximumFractionDigits: 0 })}%`}
            />
          );
        }
        return <EditableInputCell {...props} type="number" disabled />;
      };
      const otherColumns: ColumnDef<DisbursementProgressSummaryDetailByRoom>[] = [
        {
          id: 'month01', // Dự kiến lũy kế giải ngân từ đầu năm đến hết tháng 1/2025
          accessorKey: 'month01',
          header: t('fields.disbursementProgressSummaryDetailByRooms.month01'),
        },

        {
          id: 'month02', // Dự kiến lũy kế giải ngân từ đầu năm đến hết tháng 2/2025
          accessorKey: 'month02',
          header: t('fields.disbursementProgressSummaryDetailByRooms.month02'),
        },

        {
          id: 'month03', // Dự kiến lũy kế giải ngân từ đầu năm đến hết tháng 3/2025
          accessorKey: 'month03',
          header: t('fields.disbursementProgressSummaryDetailByRooms.month03'),
        },

        {
          id: 'q1PlannedDisbursement', // GIẢI NGÂN THEO KH SỐ 191/KH-QLDA Quý I/2025
          accessorKey: 'q1PlannedDisbursement',
          header: t('fields.disbursementProgressSummaryDetailByRooms.q1PlannedDisbursement'),
        },

        {
          id: 'month04', // Dự kiến lũy kế giải ngân từ đầu năm đến hết tháng 4/2025
          accessorKey: 'month04',
          header: t('fields.disbursementProgressSummaryDetailByRooms.month04'),
        },

        {
          id: 'month05', // Dự kiến lũy kế giải ngân từ đầu năm đến hết tháng 5/2025
          accessorKey: 'month05',
          header: t('fields.disbursementProgressSummaryDetailByRooms.month05'),
        },

        {
          id: 'month06', // Dự kiến lũy kế giải ngân từ đầu năm đến hết tháng 6/2025
          accessorKey: 'month06',
          header: t('fields.disbursementProgressSummaryDetailByRooms.month06'),
        },

        {
          id: 'q2PlannedDisbursement', // GIẢI NGÂN THEO KH SỐ 191/KH-QLDA Quý II/2025
          accessorKey: 'q2PlannedDisbursement',
          header: t('fields.disbursementProgressSummaryDetailByRooms.q2PlannedDisbursement'),
        },

        {
          id: 'month07', // Dự kiến lũy kế giải ngân từ đầu năm đến hết tháng 7/2025
          accessorKey: 'month07',
          header: t('fields.disbursementProgressSummaryDetailByRooms.month07'),
        },

        {
          id: 'month08', // Dự kiến lũy kế giải ngân từ đầu năm đến hết tháng 8/2025 UBND TP
          accessorKey: 'month08',
          header: t('fields.disbursementProgressSummaryDetailByRooms.month08'),
        },

        {
          id: 'month09', // Dự kiến lũy kế giải ngân từ đầu năm đến hết tháng 9/2025
          accessorKey: 'month09',
          header: t('fields.disbursementProgressSummaryDetailByRooms.month09'),
        },

        // {
        //   id: 'q3PlannedDisbursement', // GIẢI NGÂN THEO KH SỐ 191/KH-QLDA Quý III/2025
        //   accessorKey: 'q3PlannedDisbursement',
        //   header: t('fields.disbursementProgressSummaryDetailByRooms.q3PlannedDisbursement'),
        //   cell: props => <EditableInputCell {...props} type="number" />,
        // },

        {
          id: 'month10', // Dự kiến lũy kế giải ngân từ đầu năm đến hết tháng 10/2025
          accessorKey: 'month10',
          header: t('fields.disbursementProgressSummaryDetailByRooms.month10'),
        },

        {
          id: 'month11', // Dự kiến lũy kế giải ngân từ đầu năm đến hết tháng 11/2025 UBND TP
          accessorKey: 'month11',
          header: t('fields.disbursementProgressSummaryDetailByRooms.month11'),
        },

        {
          id: 'month12', // Dự kiến lũy kế giải ngân từ đầu năm đến hết tháng 12/2025 UBND TP
          accessorKey: 'month12',
          header: t('fields.disbursementProgressSummaryDetailByRooms.month12'),
        },

        // {
        //   id: 'q4PlannedDisbursement', // GIẢI NGÂN THEO KH SỐ 191/KH-QLDA Quý IV/2025
        //   accessorKey: 'q4PlannedDisbursement',
        //   header: t('fields.disbursementProgressSummaryDetailByRooms.q4PlannedDisbursement'),
        //   cell: props => <EditableInputCell {...props} type="number" />,
        // },
        // {
        //   id: 'removeRow',
        //   header: ' ',
        //   size: 10,
        //   cell: props => {
        //     return (
        //       <DataTableRowActions
        //         onDelete={() => {
        //           props.table.options.meta?.removeRowByIndex(props.row.index);
        //         }}
        //         canDelete={role?.isCreate || role?.isUpdate}
        //       />
        //     );
        //   },
        // },
      ];

      otherColumns.forEach(column => {
        if (!column.cell) {
          column.cell = renderValue;
        }
      });

      // const columns: ColumnDef<DisbursementProgressSummaryDetailByRoom>[] = [...groupColumn];
      // otherColumns.forEach((item, index) => {
      //   columns.push({
      //     id: `${index + 1}`,
      //     columns: [item],
      //   });
      // });

      const columns: ColumnDef<DisbursementProgressSummaryDetailByRoom>[] = [...groupColumn];
      otherColumns.forEach((item, index) => {
        const groupedColumn: ColumnDef<DisbursementProgressSummaryDetailByRoom> = {
          id: `${index + 1}`,
          columns: [item],
        };

        // Copy size properties from child column to parent group column
        if (item.size !== undefined) {
          groupedColumn.size = item.size;
        }
        if (item.maxSize !== undefined) {
          groupedColumn.maxSize = item.maxSize;
        }
        if (item.minSize !== undefined) {
          groupedColumn.minSize = item.minSize;
        }

        columns.push(groupedColumn);
      });
      return columns;
    }, [role?.isCreate, role?.isUpdate, t]);

  return (
    <div>
      <>
        <div className="col-span-1 flex justify-end xl:col-span-3 xl:justify-start">
          {/* <Button
              text={tcommon('action.getData')}
              className="w-fit"
              stylingMode="contained"
              type="default"
              icon="search"
              onClick={() => void handleGetData()}
              disabled={loadingData}
              //
            /> */}
        </div>
        <DataTable
          tableId={TABLES.DISBURSEMENT_PROGRESS_SUMMARY_DETAIL_BY_ROOM}
          sortColumn="id"
          grouping={['roomIdplannedCapitalValuecontent']}
          role={role}
          editableData={editableData}
          setEditableData={editedData => {
            setValue('disbursementProgressSummaryDetailByRooms', editedData);
            calculateForm?.();
          }}
          onAddButtonClick={table => {
            const newRow = { ...defaultRow, id: -getRandomNumber() };
            table.options.meta?.addNewRow(newRow);
          }}
          initialState={{
            pagination: {
              pageSize: editableData.length,
            },
          }}
          manualPagination
          syncQueryParams={false}
          columns={disbursementProgressSummaryEditableColumns}
          customToolbar={() => {
            return (
              <>
                {errors.disbursementProgressSummaryDetailByRooms?.message && (
                  <ErrorMessage
                    message={errors.disbursementProgressSummaryDetailByRooms?.message}
                  />
                )}
              </>
            );
          }}
          hideAutoNumberedColumn
        />
      </>
    </div>
  );
};
