import { BasicDialog } from '@/components/basic-dialog';
import { customizeNumberCell, DevexDataGrid } from '@/components/devex-data-grid';
import { PeriodFilter, PeriodFilterForm } from '@/components/period-filter-form';
import { FormField } from '@/components/ui/form';
import { QUERIES, TABLES } from '@/constant';
import { useAuth, useBoolean, useDataTable, useEntity } from '@/hooks';
import { callbackWithTimeout, cn, displayExpr } from '@/lib/utils';
import { createQueryByIdFn, createQueryPaginationFn, Model } from '@/services';
import { Contract, ContractTaskManagement } from '@/types';
import { useQuery } from '@tanstack/react-query';
import { SelectBox } from 'devextreme-react';
import Button from 'devextreme-react/button';
import { Column, DataGridTypes, Editing, Lookup, Selection } from 'devextreme-react/data-grid';
import { Check } from 'lucide-react';
import { useCallback, useRef } from 'react';
import { useFormContext } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

export const ContractTaskManagementSelectFromContractWindow = ({
  setHasSelectProject,
  model,
}: {
  setHasSelectProject: () => void;
  model: Model;
}) => {
  const { t } = useTranslation(['contract', 'contractTaskManagement']);
  const { state: open, toggle } = useBoolean();
  const { user, projects } = useAuth();
  const projectIds = projects.map(i => i.id).toString() || user?.projectIds;
  const { list: costItemStore } = useEntity({ queryKey: [QUERIES.COST_ITEM], model: 'cost-item' });
  const { list: staffStore } = useEntity({
    queryKey: [QUERIES.USERS],
    model: 'user',
  });
  const { list: contractTypeStore } = useEntity({
    queryKey: [QUERIES.CONTRACT_TYPE],
    model: 'contract-type',
  });
  const { list: tenderPackageStore } = useEntity({
    queryKey: [QUERIES.TENDER_PACKAGE],
    model: 'tender-package',
  });

  const selectedRowRef = useRef<{ instance: Contract | null }>({ instance: null });

  const {
    queryListParams,
    queryListMethods,
    // Query
  } = useDataTable<Contract, PeriodFilter>({
    queryRangeName: 'contractTime',
    invalidateKey: [QUERIES.CONTRACT],
    initialQuery: {
      filterColumn: [
        {
          column: 'ProjectId',
          expression: 'IN',
          keySearch: `(${projectIds || 0})`,
        },
      ],
    },
  });

  const { data, refetch } = useQuery({
    queryKey: [QUERIES.CONTRACT],
    queryFn: () => {
      return createQueryPaginationFn<Contract>('contract')({
        pageIndex: 1,
        pageSize: -1,
        sortColumn: 'Id',
        sortOrder: 1,
        isPage: false,
        filterColumn: [],
        ...queryListParams,
      });
    },
  });

  const { items } = data || { items: [] };

  const { getValues, reset } = useFormContext<ContractTaskManagement>();

  const onSelectionChanged = useCallback(
    ({ selectedRowsData }: DataGridTypes.SelectionChangedEvent) => {
      const [data] = selectedRowsData as Contract[];
      selectedRowRef.current.instance = data;
    },
    []
  );

  const { refetch: fetchSelectedContract } = useQuery({
    queryKey: [QUERIES.CONTRACT, selectedRowRef.current.instance?.id],
    queryFn: () => {
      return createQueryByIdFn<Contract>('contract')(selectedRowRef.current.instance?.id || 0);
    },
    enabled: false,
  });

  const onApply = async () => {
    const selectedRow = selectedRowRef.current.instance;
    if (!selectedRow) return;

    const commonData = {
      contractId: selectedRow.id,
      projectId: selectedRow.projectId || 0,
      contractCode: selectedRow.code,
      contractNumber: selectedRow.contractNumber,
    };

    try {
      if (model === 'contract-task-management') {
        const response = await fetchSelectedContract();
        reset({
          ...getValues(),
          ...commonData,
          itemsRecordManagement: response.data?.itemsRecordManagement,
        });
      } else {
        reset({ ...getValues(), ...commonData });
      }
    } catch (err) {
      console.log(err);
    } finally {
      setHasSelectProject();
      toggle();
    }
  };

  return (
    <>
      <Button
        icon="plus"
        text={t('page.form.buttonContract', { ns: 'contractTaskManagement' })}
        onClick={toggle}
        type="default"
        stylingMode="contained"
      />
      <BasicDialog
        open={open}
        toggle={toggle}
        // title nhớ lấy từ file đa ngôn ngữ
        title={t('page.selectFromContractWindow', { ns: 'contractTaskManagement' })}
      >
        <div className="max-w-fit overflow-scroll">
          <div className={cn('h-full opacity-100')}>
            <PeriodFilterForm
              defaultSearchValues={{
                range: [queryListParams.fromDate!, queryListParams.toDate!],
              }}
              onSearch={values => {
                if (values.range) {
                  const [from, to] = values.range;
                  queryListMethods.addOrReplaceFilterDateColumn('contractTime', from!, to!);
                }

                queryListMethods.addOrReplaceFilterColumn({
                  column: 'projectId',
                  expression: 'IN',
                  keySearch: `(${values.projectId ? String(values.projectId) : projectIds || 0})`,
                });

                callbackWithTimeout(refetch);
              }}
            >
              <FormField id="projectId" className="min-w-0 flex-1" name={'projectId'}>
                <SelectBox
                  items={projects}
                  searchExpr={['name', 'code']}
                  valueExpr="id"
                  onFocusIn={e => {
                    const input = e.element.querySelector(
                      'input.dx-texteditor-input'
                    ) as HTMLInputElement;
                    if (input) input.select();
                  }}
                  searchEnabled
                  searchMode="contains"
                  displayExpr={displayExpr(['name'])}
                  showClearButton
                  focusStateEnabled={false}
                />
              </FormField>
            </PeriodFilterForm>
            <div className="grid grid-cols-24">
              <div className="col-span-24">
                <DevexDataGrid
                  id={TABLES.CONTRACT}
                  dataSource={items}
                  onRefresh={() => {
                    callbackWithTimeout(refetch);
                  }}
                  onSelectionChanged={onSelectionChanged}
                >
                  <Editing allowUpdating={false} allowDeleting={false} useIcons />
                  <Selection mode="single" allowSelectAll={false} />
                  <Column dataField="projectId" caption={t('fields.projectId')}>
                    <Lookup
                      dataSource={projects}
                      displayExpr={displayExpr(['name'])}
                      valueExpr={'id'}
                    />
                  </Column>
                  <Column dataField="userCreatedId" caption={t('fields.userCreatedId')}>
                    <Lookup
                      dataSource={staffStore}
                      displayExpr={displayExpr(['name'])}
                      valueExpr={'id'}
                    />
                  </Column>
                  <Column dataField="contractTypeId" caption={t('fields.contractTypeId')}>
                    <Lookup
                      dataSource={contractTypeStore}
                      displayExpr={displayExpr(['name'])}
                      valueExpr={'id'}
                    />
                  </Column>
                  <Column dataField="tenderPackageId" caption={t('fields.tenderPackageId')}>
                    <Lookup
                      dataSource={tenderPackageStore}
                      displayExpr={displayExpr(['tenderPackageName'])}
                      valueExpr={'id'}
                    />
                  </Column>
                  <Column dataField="costItemId" caption={t('fields.costItemId')}>
                    <Lookup
                      dataSource={costItemStore}
                      displayExpr={displayExpr(['name'])}
                      valueExpr={'id'}
                    />
                  </Column>
                  <Column dataField="contractNumber" caption={t('fields.contractNumber')} />
                  <Column
                    dataField="contractTime"
                    caption={t('fields.contractTime')}
                    dataType="date"
                  />
                  <Column dataField="contractName" caption={t('fields.contractName')} />
                  <Column
                    dataField="contractPenaltyRate"
                    caption={t('fields.contractPenaltyRate')}
                  />
                  <Column
                    dataField="totalAmount"
                    caption={t('fields.totalAmount')}
                    dataType="number"
                    alignment="right"
                    customizeText={customizeNumberCell()}
                  />
                  <Column
                    dataField="unexpectedCostPct"
                    caption={t('fields.unexpectedCostPct')}
                    dataType="number"
                    alignment="right"
                    customizeText={customizeNumberCell()}
                  />
                  <Column
                    dataField="contractAmount"
                    caption={t('fields.contractAmount')}
                    dataType="number"
                    alignment="right"
                    customizeText={customizeNumberCell()}
                  />
                </DevexDataGrid>
              </div>
            </div>
          </div>
          <div className="col-span-24">
            <div className="flex justify-end">
              <Button
                className={cn(
                  'items-center border-solid  border-gray-400/60 bg-white px-1.5 hover:bg-gray-200'
                )}
                onClick={() => void onApply()}
              >
                <Check size={16} className="text-sky-500" strokeWidth={3} />
                <span className="ml-1">{t('page.form.okLabel')}</span>
              </Button>
            </div>
          </div>
        </div>
      </BasicDialog>
    </>
  );
};
