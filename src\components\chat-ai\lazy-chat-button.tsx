import { lazy, Suspense, useState } from 'react';
import { Button } from 'devextreme-react/button';

// Sử dụng dynamic import để tải component một cách lazy
const LazyChatDialog = lazy(() =>
  import('./chat-dialog').then(module => ({
    default: module.default,
  }))
);

export function LazyChatButton() {
  const [isOpen, setIsOpen] = useState(false);

  const toggleChat = () => {
    setIsOpen(!isOpen);
  };

  return (
    <div className="fixed bottom-9 right-0 z-50 md:right-6">
      <Suspense fallback={null}>
        {isOpen && (
          <div className="absolute bottom-16 right-0 h-[600px] w-[600px] overflow-hidden rounded-lg bg-white shadow-xl">
            <LazyChatDialog onClose={() => setIsOpen(false)} />
          </div>
        )}
      </Suspense>
      <Button
        type="default"
        stylingMode="contained"
        icon="comment"
        onClick={toggleChat}
        className="!h-10 !w-10 !rounded-full !bg-info-500 !shadow-lg hover:!bg-info-600"
      />
    </div>
  );
}
