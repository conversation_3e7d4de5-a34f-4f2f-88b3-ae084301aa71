import { ErrorMessage } from '@/components/ui/error-message';
import { QUERIES, TABLES } from '@/constant';
import {
  FinancialSettlementReport,
  FinancialSettlementReportProposedSettlementInvestmentCost,
  IUserPermission,
} from '@/types';
import { useFormContext, useWatch } from 'react-hook-form';

import { CellContext, ColumnDef, GroupingState } from '@tanstack/react-table';

import {
  DataTable,
  EditableDropdownCell,
  EditableInputCell,
  NumberCell,
} from '@/components/data-table';
import { translationWithNamespace } from '@/lib/i18nUtils';
import { useMemo, useState } from 'react';

type FinancialSettlementReportProposedSettlementInvestmentCostEditableTableProps = {
  role?: IUserPermission;
  calculateForm?: () => void;
};

const t = translationWithNamespace('financialSettlementReport');

export const FinancialSettlementReportProposedSettlementInvestmentCostEditableTable = ({
  role,
  calculateForm,
}: FinancialSettlementReportProposedSettlementInvestmentCostEditableTableProps) => {
  const {
    setValue,
    control,
    formState: { errors },
  } = useFormContext<FinancialSettlementReport>();

  const [editableData] = useWatch({
    control,
    name: ['financialSettlementReportProposedSettlementInvestmentCosts'],
  });

  const [grouping, setGrouping] = useState<GroupingState>(['costItemTypeId']);

  const financialSettlementReportProposedSettlementInvestmentCostEditableColumns: ColumnDef<FinancialSettlementReportProposedSettlementInvestmentCost>[] =
    useMemo(
      () => [
        {
          id: 'costItemTypeId', //Loại nội dung chi phí
          accessorKey: 'costItemTypeId',
          header: t(
            'fields.financialSettlementReportProposedSettlementInvestmentCosts.costItemTypeId'
          ),
          cell: (
            props: CellContext<FinancialSettlementReportProposedSettlementInvestmentCost, unknown>
          ) => (
            <EditableDropdownCell
              {...props}
              model="cost-item-type"
              queryKey={[QUERIES.COST_ITEM_TYPE]}
              disabled={true}
            />
          ),
        },
        {
          id: 'costItemId', // Nội dung chi phí
          accessorKey: 'costItemId',
          header: t('fields.financialSettlementReportProposedSettlementInvestmentCosts.costItemId'),
          cell: (
            props: CellContext<FinancialSettlementReportProposedSettlementInvestmentCost, unknown>
          ) => (
            <EditableDropdownCell
              {...props}
              model="cost-item"
              queryKey={[QUERIES.COST_ITEM]}
              disabled={true}
            />
          ),
          aggregatedCell: () => {
            return null;
          },
        },
        {
          id: 'approvedFinalProjectBudget', // Tổng mức đầu tư của dự án (dự án thành phần, tiểu dự án độc lập) hoặc dự toán (công trình, hạng mục công trình) được phê duyệt hoặc điều chỉnh lần cuối
          accessorKey: 'approvedFinalProjectBudget',
          header: t(
            'fields.financialSettlementReportProposedSettlementInvestmentCosts.approvedFinalProjectBudget'
          ),
          cell: props => <EditableInputCell {...props} type="number" disabled={true} />,
          aggregatedCell: NumberCell,
        },

        {
          id: 'approvedFinalEstimate', // Dự toán (Tổng dự toán) được phê duyệt hoặc điều chỉnh lần cuối
          accessorKey: 'approvedFinalEstimate',
          header: t(
            'fields.financialSettlementReportProposedSettlementInvestmentCosts.approvedFinalEstimate'
          ),
          cell: props => <EditableInputCell {...props} type="number" disabled={true} />,
          aggregatedCell: NumberCell,
        },

        {
          id: 'proposedSettlementValue', // Giá trị đề nghị quyết toán
          accessorKey: 'proposedSettlementValue',
          header: t(
            'fields.financialSettlementReportProposedSettlementInvestmentCosts.proposedSettlementValue'
          ),
          cell: props => <EditableInputCell {...props} type="number" />,
          aggregatedCell: NumberCell,
        },

        {
          id: 'increaseDecreaseReason', // Nguyên nhân tăng, giảm
          accessorKey: 'increaseDecreaseReason',
          header: t(
            'fields.financialSettlementReportProposedSettlementInvestmentCosts.increaseDecreaseReason'
          ),
          cell: props => <EditableInputCell {...props} />,
        },
      ],
      []
    );

  return (
    <div>
      <DataTable
        tableId={TABLES.FINANCIAL_SETTLEMENT_REPORT_PROPOSED_SETTLEMENT_INVESTMENT_COST}
        sortColumn="id"
        role={role}
        editableData={editableData}
        setEditableData={editedData => {
          setValue('financialSettlementReportProposedSettlementInvestmentCosts', editedData);
          calculateForm?.();
        }}
        manualGrouping={false}
        // grouping={['costItemTypeId']}
        grouping={grouping}
        setGrouping={setGrouping}
        syncQueryParams={false}
        columns={financialSettlementReportProposedSettlementInvestmentCostEditableColumns}
        customToolbar={() => {
          return (
            <>
              {errors.financialSettlementReportProposedSettlementInvestmentCosts?.message && (
                <ErrorMessage
                  message={
                    errors.financialSettlementReportProposedSettlementInvestmentCosts?.message
                  }
                />
              )}
            </>
          );
        }}
      />
    </div>
  );
};
