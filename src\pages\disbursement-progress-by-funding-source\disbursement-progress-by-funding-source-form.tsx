import { zodResolver } from '@hookform/resolvers/zod';
import { SyntheticEvent, useMemo, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { useParams } from 'react-router-dom';

import { DeleteConfirmDialog } from '@/components/confirm-dialog';
import { PageLayout } from '@/components/page-layout';
import { PeriodFilter } from '@/components/period-filter-form';
import { Form, FormCombobox, FormField, FormLabel } from '@/components/ui/form';
// import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { enterLabel, MUTATE, QUERIES, selectLabel } from '@/constant';
import { useAuth, useDataTable, useFormHandler, usePermission } from '@/hooks';
import { useFormNavigate } from '@/hooks/use-form-navigate';
import { toDateType, toLocaleDate } from '@/lib/date';
import { createMutationSuccessFn } from '@/lib/i18nUtils';
import {
  createDeleteMutateFn,
  createPostMutateFn,
  createPutMutateFn,
  createQueryByIdFn,
} from '@/services';
import {
  DisbursementProgressByFundingSource,
  disbursementProgressByFundingSourceSchema,
  defaultValuesDisbursementProgressByFundingSource,
  DisbursementProgressByFundingSourceDetail,
} from '@/types';
import { DateBox } from 'devextreme-react';
import Button from 'devextreme-react/button';
import { Textarea } from '@/components/ui/textarea';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { getRandomNumber } from '@/lib/number';
import { DisbursementProgressByFundingSourceEditableTableDevextreme } from './disbursement-progress-by-funding-source-editable-table-devextreme';
import { DataGridRef } from 'devextreme-react/cjs/data-grid';

const onDisbursementProgressByFundingSourceMutationSuccess = createMutationSuccessFn(
  'disbursementProgressByFundingSource'
);

export const DisbursementProgressByFundingSourceForm = ({
  path,
  permission,
}: {
  path: string;
  permission: number;
}) => {
  const { id: editId } = useParams();

  const { t } = useTranslation('disbursementProgressByFundingSource');

  const role = usePermission(permission);
  const { user } = useAuth();

  const { goBackToList, goToUpdate, goToNew } = useFormNavigate(path);

  const defaultValues = useMemo(
    () => ({
      ...defaultValuesDisbursementProgressByFundingSource,
      userCreatedId: user?.userId,
    }),
    [user?.userId]
  );

  const { handleSubmit, loading, methods } = useFormHandler<DisbursementProgressByFundingSource>({
    queryKey: [MUTATE.DISBURSEMENT_PROGRESS_BY_FUNDING_SOURCE, editId],
    mutateKey: [MUTATE.DISBURSEMENT_PROGRESS_BY_FUNDING_SOURCE],
    queryId: Number(editId) || 0,
    invalidateKey: [QUERIES.DISBURSEMENT_PROGRESS_BY_FUNDING_SOURCE],
    readFn: createQueryByIdFn<DisbursementProgressByFundingSource>(
      'disbursement-progress-by-funding-source'
    ),
    createFn: createPostMutateFn<DisbursementProgressByFundingSource>(
      'disbursement-progress-by-funding-source'
    ),
    updateFn: createPutMutateFn<DisbursementProgressByFundingSource>(
      'disbursement-progress-by-funding-source'
    ),
    formatPayloadFn: data => ({
      ...data,
      storeId: data.storeId === null ? 0 : data.storeId,
      fiscalYear: toLocaleDate(data.fiscalYear),
      disbursementProgressByFundingSourceDetails:
        data.disbursementProgressByFundingSourceDetails.map(item => ({
          ...item,
          id: item.id > 0 ? item.id : -getRandomNumber(),
          completionAcceptanceDate: toLocaleDate(item.completionAcceptanceDate || null),
        })),
    }),
    formatResponseFn: data => ({
      ...data,
      fiscalYear: toDateType(data.fiscalYear),
      disbursementProgressByFundingSourceDetails:
        data.disbursementProgressByFundingSourceDetails.map(item => ({
          ...item,
          completionAcceptanceDate: toDateType(item.completionAcceptanceDate || null),
        })),
    }),
    onCreateSuccess: data => {
      onDisbursementProgressByFundingSourceMutationSuccess(data);
      goToUpdate(data);
    },
    onUpdateSuccess: onDisbursementProgressByFundingSourceMutationSuccess,
    formOptions: {
      resolver: zodResolver(disbursementProgressByFundingSourceSchema),
      defaultValues,
    },
  });

  const {
    isDeleting,
    deleteTarget,
    selectTargetToDelete,
    toggleConfirmDeleteDialog,
    isConfirmDeleteDialogOpen,
  } = useDataTable<DisbursementProgressByFundingSource, PeriodFilter>({
    deleteFn: createDeleteMutateFn('disbursement-progress-by-funding-source'),
    deleteKey: [MUTATE.DISBURSEMENT_PROGRESS_BY_FUNDING_SOURCE],
  });

  const onCreateNew = () => {
    goToNew();
    methods.reset(defaultValues);
  };

  const onDelete = () => {
    selectTargetToDelete(methods.getValues());
  };

  const dataGridRef = useRef<DataGridRef | null>(null);

  return (
    <>
      <Form {...methods}>
        <form autoComplete="off">
          <PageLayout
            onSaveChange={e => {
              const dataGridInstance = dataGridRef?.current?.instance();
              dataGridInstance
                ?.getDataSource()
                .store()
                .load()
                .then(data => {
                  methods.setValue(
                    'disbursementProgressByFundingSourceDetails',
                    (data || []) as DisbursementProgressByFundingSourceDetail[]
                  );
                  handleSubmit(e.event?.currentTarget as unknown as SyntheticEvent<HTMLElement>);
                })
                .catch(error => console.error('error:', error));
            }}
            header={editId !== 'new' ? t('page.form.edit') : t('page.form.addNew')}
            canSaveChange={!isNaN(Number(editId)) ? role?.isUpdate : role?.isCreate}
            isSaving={loading}
            onCancel={goBackToList}
            onDelete={editId !== 'new' ? onDelete : undefined}
            customElementLeft={
              <>
                <Button
                  text={t('content.createNew', { ns: 'common' })}
                  className="uppercase"
                  stylingMode="outlined"
                  type="default"
                  icon="plus"
                  onClick={onCreateNew}
                />
              </>
            }
          >
            <div className="grid grid-cols-1  gap-x-8 gap-y-4 lg:grid-cols-24">
              <div className="col-span-1 flex flex-col gap-x-8 gap-y-4 lg:col-span-8 2xl:col-span-6">
                <div className="flex flex-row items-center ">
                  <FormLabel
                    name="fiscalYear"
                    htmlFor="fiscalYear"
                    className="hidden w-[120px]  md:block "
                  >
                    {t('fields.fiscalYear')}
                  </FormLabel>
                  <FormField
                    label={t('fields.fiscalYear')}
                    id="fiscalYear"
                    name="fiscalYear"
                    className="w-full flex-1"
                    type="date"
                  >
                    <DateBox
                      placeholder={`${selectLabel} ${t('fields.fiscalYear')}`}
                      calendarOptions={{
                        maxZoomLevel: 'decade',
                        minZoomLevel: 'decade',
                      }}
                      displayFormat={'year'}
                      pickerType="calendar"
                      focusStateEnabled={false}
                    />
                  </FormField>
                </div>
              </div>
              <div className="col-span-1 flex flex-col gap-x-8 gap-y-4 lg:col-span-14 2xl:col-span-10">
                <div className="flex flex-row items-center ">
                  <FormLabel htmlFor="budgetFundId" className="hidden w-[120px]  md:block ">
                    {t('fields.budgetFundId')}
                  </FormLabel>
                  <FormField
                    id="budgetFundId"
                    name="budgetFundId"
                    className="w-full flex-1"
                    label={t('fields.budgetFundId')}
                  >
                    <FormCombobox
                      placeholder={`${selectLabel} ${t('fields.budgetFundId')}`}
                      model="budget-fund"
                      queryKey={[QUERIES.BUDGET_FUND]}
                    />
                  </FormField>
                </div>
              </div>
              <div className="col-span-1  grid grid-cols-1 flex-col gap-x-8 gap-y-4 lg:col-span-24 lg:grid-cols-24">
                <div className="col-span-1 flex flex-col gap-x-8 gap-y-4 lg:col-span-22  2xl:col-span-16">
                  <div className="flex flex-row items-center ">
                    <FormLabel htmlFor="note" className="hidden w-[120px] md:block ">
                      {t('fields.note')}
                    </FormLabel>
                    <FormField
                      id="note"
                      name="note"
                      className="w-full flex-1"
                      label={t('fields.note')}
                    >
                      <Textarea placeholder={`${enterLabel} ${t('fields.note')}`} />
                    </FormField>
                  </div>
                </div>
              </div>
            </div>

            <div className="mt-8">
              <Tabs defaultValue="detail">
                <div className="w-full">
                  <TabsList>
                    <TabsTrigger value="detail">{t('page.tabs.detail')}</TabsTrigger>
                  </TabsList>
                </div>
                <TabsContent value="detail" className="mt-4">
                  <DisbursementProgressByFundingSourceEditableTableDevextreme
                    role={role}
                    setInstance={ref => (dataGridRef.current = ref)}
                  />
                  {/* <DisbursementProgressByFundingSourceEditableTable role={role} /> */}
                </TabsContent>
              </Tabs>
            </div>
            {/* <div style={{ display: "none" }}>{printoutElement}</div> */}
          </PageLayout>
        </form>
      </Form>
      <DeleteConfirmDialog
        model="disbursementProgressByFundingSource"
        name={methods.getValues('note')!}
        open={isConfirmDeleteDialogOpen}
        toggle={toggleConfirmDeleteDialog}
        isDeleting={isDeleting}
        onConfirm={() => {
          deleteTarget();
          setTimeout(() => onCreateNew(), 0);
        }}
      />
    </>
  );
};
