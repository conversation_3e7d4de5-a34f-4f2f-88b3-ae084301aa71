// Type cho chi tiết lịch làm việc đầu vào (raw input)
// Sử dụng departmentIdArray và memberIdArray là mảng số
export interface TrainingTrackingDetailInput {
  id: number;
  trainingTrackingId: number;
  memberIdArray: number[] | null; // Mảng các ID thành viên tham gia
  roomName: string | null;
  trainingInstitutionId: number | null;
  quantityMember: number | null;
  classStartDate: string | null;
  memberIds?: string | null; // Bỏ qua, dùng memberIdArray
  [key: string]: any;
}

/**
 * Loại bỏ ký tự xuống dòng và các khoảng trắng thừa từ một chuỗi.
 */
function cleanString(str: string | null): string | null {
  if (str === null || str === undefined) return null;
  if (typeof str === 'string') {
    return str
      .replace(/(\r\n|\n|\r)/gm, '<br>')
      .replace(/\s+/g, ' ')
      .trim();
  }
  return str; // Tr<PERSON> về nguyên gốc nếu không phải chuỗi hoặc đã là null/undefined
}

export function groupTrainingTrackingDetails(
  detailsArray: any[],
  allMembers: any[],
  trainingInstitutionList: any[]
): any[] {
  if (!detailsArray || detailsArray.length === 0) {
    return [];
  }

  const memberMap = new Map<number, any>();
  allMembers.forEach(member => {
    if (member && typeof member.id === 'number') {
      memberMap.set(member.id as number, member);
    }
  });

  const institutionMap = new Map<number, any>();
  if (Array.isArray(trainingInstitutionList)) {
    trainingInstitutionList.forEach(inst => {
      if (inst && typeof inst.id === 'number') {
        institutionMap.set(inst.id as number, inst);
      }
    });
  }

  return detailsArray.map(detailRaw => {
    // Tạo đối tượng kết quả với các thuộc tính cơ bản
    const result: any = {
      ...detailRaw,
      trainingInstitutionName: '',
      memberNames: '',
    };

    // Mapping trainingInstitutionId sang trainingInstitutionName
    if (
      typeof result.trainingInstitutionId === 'number' &&
      institutionMap.has(result.trainingInstitutionId as number)
    ) {
      const inst = institutionMap.get(result.trainingInstitutionId as number);
      result.trainingInstitutionName = cleanString((inst?.name || '') as string);
    }

    // Phân giải memberIdArray thành memberNames với format mỗi người một dòng
    if (Array.isArray(detailRaw.memberIdArray)) {
      const memberIdArray = detailRaw.memberIdArray as number[];
      const memberNames = memberIdArray
        .map((id: number) => {
          if (typeof id === 'number' && !isNaN(id) && id !== 0) {
            const member = memberMap.get(id);
            const name = member
              ? cleanString((member?.fullName || member?.name || '') as string)
              : '';
            return name ? `- ${name}` : '';
          }
          return null;
        })
        .filter((name: string | null) => name !== null && name !== '')
        .join('\n');

      result.memberNames = memberNames;
    }

    // Định dạng classStartDate
    if (result.classStartDate) {
      const date = new Date(result.classStartDate as string | number | Date);
      if (!isNaN(date.getTime())) {
        result.classStartDate = date.toLocaleDateString('vi-VN');
      }
    }

    // Làm sạch các trường chuỗi
    Object.keys(result as Record<string, unknown>).forEach(key => {
      if (typeof result[key] === 'string') {
        result[key] = cleanString(result[key]);
      }
    });
    return result as TrainingTrackingDetailInput;
  });
}
