export const boardOfDirectorsWorkSchedulePrintTemplate = `<style>
/* <PERSON>ăn chỉnh phần tiêu đề trên cùng */
.header-container { display: flex; justify-content: space-between; margin-bottom: 20px; }
.header-left { width: fit-content; }
.header-right { width: fit-content; }
.header-left h3,
.header-right h3 { margin: 0; }
.header-left { text-transform: uppercase; }
.header-right { text-transform: uppercase; }

/* Tiêu đề ở giữa trang */
.main-title { text-align: center; margin-bottom: 30px; }
.main-title h2 { margin: 0; text-transform: uppercase; font-weight: bold; }
.main-title h4 { margin: 5px 0 0 0; font-weight: bold; }

/* Phần chữ ký */
.signature-section { width: 100%; display: flex; justify-content: space-between; text-align: center; }
.signature-box { width: 30%; }

/* Canh giữa chữ ký và tên */
.signature-box p { margin: 80px 0 0 0; /* <PERSON><PERSON><PERSON> khoảng trống để ký tên trước khi ghi họ tên */ }

.date-section { width: 100%; display: flex; justify-content: space-between; text-align: center; }
.date-box { width: 30%; }
.date-place { text-align: right; font-style: italic; }
.summary-place { text-align: left; font-weight: bold; }
hr.header-line { border: 0; height: 0.005rem; background: #333; width: 180px }
</style>
<div class="print-content" style="font-family: &quot;Times New Roman&quot;; serif;">
  <!-- PHẦN HEADER (TRÁI & PHẢI) -->
  <div class="header-container">
    <div class="header-left">
      <div style="width: 300px; text-align: center;">
        <h3 style="font-weight: normal;">UBND HUYỆN HÓC MÔN</h3>
        <h3 style="font-weight: bold;">BAN QUẢN LÝ DỰ ÁN</h3>
        <h3 style="font-weight: bold;">ĐẦU TƯ XÂY DỰNG KHU VỰC</h3>
        <hr style="margin-left:auto; margin-right: auto;"class="header-line">
      </div>
    </div>
    <div class="header-right">
      <div style="width: 400px; text-align: center; margin-left: auto; margin-right: 0">
        <h3 style="font-weight: bold;">CỘNG HÒA XÃ HỘI CHỦ NGHĨA VIỆT NAM</h3>
        <h3 style="font-weight: bold; font-size: 12px">Độc lập - Tự do - Hạnh phúc</h3>
        <hr style="margin-left:auto; margin-right: auto;"class="header-line">
      </div>
    </div>
  </div>
  <!-- TIÊU ĐỀ CHÍNH -->
  <div class="main-title">
    <h2>LỊCH LÀM VIỆC CỦA BAN GIÁM ĐỐC</h2>
    <h4>Tuần {{ context.weekNumber }} ({{ context.fromDate }} - {{ context.toDate }})</h4>
  </div>
  <table border="1" style="width: 100%; margin-right: auto; margin-left: auto; border-collapse: collapse; border: 1px solid black;">
  <thead>
      <tr>
        <th style="width: 7%">Ngày</th>
        <th colspan="2" style="width: 10%">Thời gian</th>
        <th style="width: 30%">Nội dung</th>
        <th style="width: 15%">Người chủ trì</th>
        <th style="width: 23%">Thành phần tham dự</th>
        <th style="width: 15%">Địa điểm</th>
      </tr>
  </thead>
  {# Render Table Body #}
  {% if context.boardOfDirectorsWorkScheduleDetails.length > 0 %}
  <tbody>
    {# Render cột ngày #}
    {% for root in context.boardOfDirectorsWorkScheduleDetails %}
      <tr>
        <td {% if root.rowspan > 1 %} rowspan="{{ root.rowspan }}" {% endif %} style="text-align: center;">{{ root.workDate | formatWorkDate }}</td>
        {# Render cột Buổi - Thời gian #}
        {# Get buổi đầu tiên trong sessions #}
        {% if root.sessions.length > 0 %}
        {% set firstSession = root.sessions | first %}
        <td {% if firstSession.rowspan > 1 %} rowspan="{{ firstSession.rowspan }}" {% endif %}>{{ context.sessionName[firstSession.session] }}</td>
          {# Render cột: Giờ - Thời gian, Nội dung, Người chủ trì, Thành phần tham dự, Địa điểm #}
          {# Get phần tử đầu tiên trong details #}
          {% if firstSession.details.length > 0 %}
          {% set firstElement = firstSession.details | first %}
          <td>{{ firstElement.workTime | formatWorkTime }}</td>
          <td>{{ firstElement.content }}</td>
          <td>
            {% if firstElement.chairpersonNameOriginal %}
              {% set firstElementChairperson = ["", firstElement.chairpersonNameOriginal] %}
              {{ firstElementChairperson | join(" - ") }} <br>
            {%endif%}
            {% if firstElement.chairpersonOther %}{{ firstElement.chairpersonOther }}{% endif %}
          </td>
          <td>

            {% if firstElement.departmentsResolved.length > 0 %}
              {% for dept in firstElement.departmentsResolved %}
                - {{ dept.name }}<br>
              {% endfor %}
            {% endif %}
            {% if firstElement.departmentOther %}{{ firstElement.departmentOther }} <br>{% endif %}
             {% if firstElement.membersResolved.length > 0 %}
              {% for dept in firstElement.membersResolved %}
                - {{ dept.name }}<br>
              {% endfor %}
            {% endif %}
            {% if firstElement.memberOther %}{{ firstElement.memberOther }}{% endif %}
          </td>
          <td>{{ firstElement.location }}</td>
          {% endif %}
        {% endif %}
      </tr>

      {% for detail in firstSession.details %}
        {# Bỏ qua phần tử đầu tiên #}
        {% if loop.index > 1 %}
          <tr>
            <td>{{ detail.workTime | formatWorkTime }}</td>
            <td>{{ detail.content }}</td>
            <td>
              {% if detail.chairpersonNameOriginal %}
                {% set detailChairperson = ["", detail.chairpersonNameOriginal] %}
                {{ detailChairperson | join(" - ") }} <br>
              {%endif%}
              {% if detail.chairpersonOther %}{{ detail.chairpersonOther }}{% endif %}
            </td>
            <td>
              {% if detail.membersResolved.length > 0 %}{{ "- " }}{{ detail.membersResolved | join(",", "name") }}<br>{% endif %}
              {% if detail.memberOther %}{{ detail.memberOther }}{% endif %}
            </td>
            <td>{{ detail.location }}</td>
          </tr>
        {% endif %}
      {% endfor %}

      {% if root.sessions.length > 1 %}
        {% for row in root.sessions %}
          {% if loop.index > 1 %}
            <tr>
              <td {% if row.rowspan > 1 %} rowspan="{{ row.rowspan }}" {% endif %}>{{ context.sessionName[row.session] }}</td>
              {% set nextElement = row.details | first %}
              <td>{{ nextElement.workTime | formatWorkTime }}</td>
              <td>{{ nextElement.content }}</td>
              <td>
                {% if nextElement.chairpersonNameOriginal %}
                  {% set nextElementChairperson = ["", nextElement.chairpersonNameOriginal] %}
                  {{ nextElementChairperson | join(" - ") }} <br>
                {%endif%}
                {% if nextElement.chairpersonOther %}{{ nextElement.chairpersonOther }}{% endif %}
              </td>
              <td>
                {% if nextElement.membersResolved.length > 0 %}{{ "- " }}{{ nextElement.membersResolved | join(",", "name") }}<br>{% endif %}
                {% if nextElement.memberOther %}{{ nextElement.memberOther }}{% endif %}
              </td>
              <td>{{ nextElement.location }}</td>
            </tr>
            {% for detail in row.details %}
              {% if loop.index > 1 %}
                <tr>
                  <td>{{ detail.workTime | formatWorkTime }}</td>
                  <td>{{ detail.content }}</td>
                  <td>
                    {% if detail.chairpersonNameOriginal %}
                      {% set detailChairperson = ["", detail.chairpersonNameOriginal] %}
                      {{ detailChairperson | join(" - ") }} <br>
                    {%endif%}
                    {% if detail.chairpersonOther %}{{  detail.chairpersonOther }}{% endif %}
                  </td>
                  <td>
                    {% if detail.membersResolved.length > 0 %}{{ "- " }}{{ detail.membersResolved | join(",", "name") }}<br>{% endif %}
                    {% if detail.memberOther %}{{ detail.memberOther }}{% endif %}
                  </td>
                  <td>{{ detail.location }}</td>
                </tr>
              {% endif %}
            {% endfor %}
          {% endif %}
        {% endfor %}
      {% endif %}

    {% endfor %}
  </tbody>
  {% else %}
   <tbody>
     <tr>
       <td colspan="7" style="text-align: center;">Không có dữ liệu</td>
     </tr>
   </tbody>
  {% endif %}
  </table>
  <!-- Dòng tổng cộng -->
  <div class="summary-place">
    {% if context.note.length > 0 %}
      <p>Ghi chú:</p>
      {% for note in context.notes %}
        <p style="margin-bottom: 0px; text-indent:18.35pt;">{{ note }}</p>
      {% endfor %}
    {% endif %}
  </div>
  <div class="signature-section" style="padding-top: 15px">
    <div class="signature-box">
      <strong></strong>
      <p></p>
    </div>
    <div class="signature-box">
      <strong></strong>
      <p></p>
    </div>
    <div class="signature-box">
      <strong>BAN QUẢN LÝ DỰ ÁN ĐẦU TƯ XÂY DỰNG KHU VỰC</strong>
      <p></p>
    </div>
  </div>
</div>`;
