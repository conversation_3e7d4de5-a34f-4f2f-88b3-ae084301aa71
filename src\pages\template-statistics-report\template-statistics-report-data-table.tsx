import { DeleteConfirmDialog } from '@/components/confirm-dialog';
import { DevexDataGrid } from '@/components/devex-data-grid';
import { PageLayout } from '@/components/page-layout';
import { removeAccents } from '@/lib/text';
import { PeriodFilter, PeriodFilterForm } from '@/components/period-filter-form';
import { MUTATE, PATHS, PERMISSIONS, QUERIES, TABLES } from '@/constant';
import { useDataTable, useEntity, usePermission } from '@/hooks';
import { createExportingEvent } from '@/lib/file';
import { callbackWithTimeout, displayExpr } from '@/lib/utils';
import { createDeleteMutateFn, createQueryPaginationFn } from '@/services';
import { translationWithNamespace } from '@/lib/i18nUtils';
import { ReportTemplate, TemplateStatisticsReportFilter, User } from '@/types';
import { useQuery } from '@tanstack/react-query';
import { But<PERSON>, Column, Editing, Export } from 'devextreme-react/data-grid';
import { ColumnButtonClickEvent } from 'devextreme/ui/data_grid';
import { snakeCase } from 'lodash';
import { useNavigate } from 'react-router-dom';
import { Lookup } from 'devextreme-react/data-grid';

const t = translationWithNamespace('templateStatisticsReport');
const path = PATHS.TEMPLATE_STATISTICS_REPORT;
const exportFileName = snakeCase(removeAccents(t('model')));

const onExporting = createExportingEvent(`${exportFileName}.xlsx`, 'Main');

export const TemplateStatisticReportDataTable = () => {
  const navigate = useNavigate();
  const role = usePermission(PERMISSIONS.TEMPLATE_STATISTICS_REPORT);

  const getTargetAlias = (target: TemplateStatisticsReportFilter | undefined) => {
    if (!target) {
      return '';
    }
    return target.numberOfCode || '';
  };

  const {
    selectedTarget,

    isConfirmDeleteDialogOpen,
    toggleConfirmDeleteDialog,
    selectTargetToDelete,
    deleteTarget,
    isDeleting,

    queryListParams,
    queryListMethods,
    // Query
  } = useDataTable<TemplateStatisticsReportFilter, PeriodFilter>({
    queryRangeName: 'templateStatisticsReportTime',
    getTargetAlias,
    deleteFn: createDeleteMutateFn<TemplateStatisticsReportFilter>('template-statistics-report'),
    deleteKey: [MUTATE.DELETE_TEMPLATE_STATISTICS_REPORT],
    invalidateKey: [QUERIES.TEMPLATE_STATISTICS_REPORT],
  });

  const { data, refetch } = useQuery({
    queryKey: [QUERIES.TEMPLATE_STATISTICS_REPORT],
    queryFn: () => {
      return createQueryPaginationFn<TemplateStatisticsReportFilter>('template-statistics-report')({
        pageIndex: 1,
        pageSize: -1,
        sortColumn: 'templateStatisticsReportTime',
        sortOrder: 1,
        isPage: false,
        filterColumn: [],
        ...queryListParams,
      });
    },
  });

  const { items } = data || { items: [] };

  const onEditClick = (e: ColumnButtonClickEvent<TemplateStatisticsReportFilter>) => {
    if (e.row?.data) {
      navigate(`${path}/` + e.row.data?.id, { state: path });
    }
  };

  const onAddClick = () => {
    navigate(`${path}/new`, { state: path });
  };

  const onDeleteClick = (e: ColumnButtonClickEvent<TemplateStatisticsReportFilter>) => {
    if (e.row?.data) {
      selectTargetToDelete(e.row.data);
    }
  };

  const { isUpdate, isDelete } = role || {};

  const { list: reportTemplates } = useEntity<ReportTemplate>({
    queryKey: [QUERIES.REPORT_TEMPLATE],
    model: 'report-template',
  });
  const { list: users } = useEntity<User>({
    queryKey: [QUERIES.USERS],
    model: 'user',
  });

  return (
    <PageLayout header={t('page.header')}>
      <PeriodFilterForm
        defaultSearchValues={{
          range: [queryListParams.fromDate!, queryListParams.toDate!],
        }}
        onSearch={values => {
          const { range } = values;

          if (range) {
            const [from, to] = values.range;
            queryListMethods.addOrReplaceFilterDateColumn(
              'templateStatisticsReportTime',
              from!,
              to!
            );
          }

          callbackWithTimeout(refetch);
        }}
      />
      <DevexDataGrid
        id={TABLES.TEMPLATE_STATISTICS_REPORT}
        dataSource={items}
        onAddNewClick={onAddClick}
        onRefresh={() => {
          callbackWithTimeout(refetch);
        }}
        onExporting={onExporting}
      >
        <Export enabled={true} />
        <Editing allowUpdating={isUpdate} allowDeleting={isDelete} useIcons />

        {/* thao tác */}
        <Column type="buttons">
          <Button name="edit" onClick={onEditClick} />
          <Button name="delete" onClick={onDeleteClick} />
        </Column>

        {/* Mã phiếu */}
        <Column dataField="numberOfCode" caption={t('fields.numberOfCode')} alignment="left" />

        <Column dataField="reportTemplateId" caption={t('fields.reportTemplateId')}>
          <Lookup
            dataSource={reportTemplates}
            displayExpr={displayExpr(['name'])}
            valueExpr={'id'}
          />
        </Column>

        <Column
          dataField="year"
          caption={t('fields.year')}
          dataType="date"
          alignment="left"
          format={'yyyy'}
        />

        <Column dataField="name" caption={t('fields.name')} />

        <Column dataField="content" caption={t('fields.content')} />

        <Column dataField="userCreatedId" caption={t('fields.userCreatedId')}>
          <Lookup dataSource={users} displayExpr={displayExpr(['name'])} valueExpr={'id'} />
        </Column>

        {/* Ngày lập */}
        <Column
          dataField="templateStatisticsTime"
          caption={t('fields.templateStatisticsTime')}
          dataType="date"
          alignment="left"
        />
      </DevexDataGrid>
      <DeleteConfirmDialog
        isDeleting={isDeleting}
        open={isConfirmDeleteDialogOpen}
        toggle={toggleConfirmDeleteDialog}
        onConfirm={() => {
          deleteTarget();
        }}
        name={getTargetAlias(selectedTarget)}
        model="templateStatisticsReport"
      />
    </PageLayout>
  );
};
