import { zod<PERSON>esolver } from '@hookform/resolvers/zod';
import { SyntheticEvent, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useParams } from 'react-router-dom';

import { DeleteConfirmDialog } from '@/components/confirm-dialog';
import { PageLayout } from '@/components/page-layout';
import { PeriodFilter } from '@/components/period-filter-form';
import { Form, FormCombobox, FormField, FormLabel } from '@/components/ui/form';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { enterLabel, MUTATE, QUERIES, selectLabel } from '@/constant';
import { useAuth, useDataTable, useFormHandler, usePermission } from '@/hooks';
import { useFormNavigate } from '@/hooks/use-form-navigate';
import { toDateType, toLocaleDate } from '@/lib/date';
import { createMutationSuccessFn } from '@/lib/i18nUtils';
import { getValidItems } from '@/lib/utils';
import {
  createDeleteMutateFn,
  createPostMutateFn,
  createPutMutateFn,
  createQueryByIdFn,
} from '@/services';
import {
  DisbursementProgressSummary,
  disbursementProgressSummarySchema,
  defaultValuesDisbursementProgressSummary,
} from '@/types';
import { DateBox, TextBox } from 'devextreme-react';
import Button from 'devextreme-react/button';
import { DisbursementProgressSummaryDetailEditableTable } from './disbursement-progress-summary-detail-editable-table';
import { DisbursementProgressSummaryDetailByRoomEditableTable } from './disbursement-progress-summary-detail-by-room-editable-table';
import { GetDisbursementProgressSummaryReport } from './utils';
import { getRandomNumber } from '@/lib/number';

const onDisbursementProgressSummaryMutationSuccess = createMutationSuccessFn(
  'disbursementProgressSummary'
);

export const DisbursementProgressSummaryForm = ({
  path,
  permission,
}: {
  path: string;
  permission: number;
}) => {
  const { id: editId } = useParams();

  const { t } = useTranslation('disbursementProgressSummary');

  const role = usePermission(permission);
  const { user } = useAuth();

  const { goBackToList, goToUpdate, goToNew } = useFormNavigate(path);

  const defaultValues = useMemo(
    () => ({
      ...defaultValuesDisbursementProgressSummary,
      userCreatedId: user?.userId,
    }),
    [user?.userId]
  );

  const { handleSubmit, loading, methods } = useFormHandler<DisbursementProgressSummary>({
    queryKey: [MUTATE.DISBURSEMENT_PROGRESS_SUMMARY, editId],
    mutateKey: [MUTATE.DISBURSEMENT_PROGRESS_SUMMARY],
    queryId: Number(editId) || 0,
    invalidateKey: [QUERIES.DISBURSEMENT_PROGRESS_SUMMARY],
    readFn: createQueryByIdFn<DisbursementProgressSummary>('disbursement-progress-summary'),
    createFn: createPostMutateFn<DisbursementProgressSummary>('disbursement-progress-summary'),
    updateFn: createPutMutateFn<DisbursementProgressSummary>('disbursement-progress-summary'),
    formatPayloadFn: data => ({
      ...data,
      budgetYear: toLocaleDate(data.budgetYear || null),
      disbursementProgressSummaryTime: toLocaleDate(data.disbursementProgressSummaryTime || null),
      disbursementProgressSummaryDetails: getValidItems(
        data.disbursementProgressSummaryDetails,
        item => item.content !== null || item.content !== undefined
      ),
      disbursementProgressSummaryDetailByRooms: getValidItems(
        data.disbursementProgressSummaryDetailByRooms,
        item => item.roomId !== null || item.roomId !== undefined
      ),
    }),
    formatResponseFn: data => ({
      ...data,
      budgetYear: toDateType(data.budgetYear || null),
      disbursementProgressSummaryTime: toDateType(data.disbursementProgressSummaryTime || null),
    }),
    onCreateSuccess: data => {
      onDisbursementProgressSummaryMutationSuccess(data);
      goToUpdate(data);
    },
    onUpdateSuccess: onDisbursementProgressSummaryMutationSuccess,
    formOptions: {
      resolver: zodResolver(disbursementProgressSummarySchema),
      defaultValues,
    },
  });

  const {
    isDeleting,
    deleteTarget,
    selectTargetToDelete,
    toggleConfirmDeleteDialog,
    isConfirmDeleteDialogOpen,
  } = useDataTable<DisbursementProgressSummary, PeriodFilter>({
    deleteFn: createDeleteMutateFn('disbursement-progress-summary'),
    deleteKey: [MUTATE.DISBURSEMENT_PROGRESS_SUMMARY],
  });

  const onCreateNew = () => {
    goToNew();
    methods.reset(defaultValues);
    // reset();
  };

  const onDelete = () => {
    selectTargetToDelete(methods.getValues());
  };

  const { getReport: mutateDetails, isLoading: loadingData } = GetDisbursementProgressSummaryReport(
    {
      onSuccess: data => {
        methods.setValue(
          'disbursementProgressSummaryDetails',
          data.listDisbursementProgressSummaryReportDto.map(item => ({
            ...item,
            id: item.id || -getRandomNumber(),
            disbursementProgressSummaryId:
              item.disbursementProgressSummaryId || isNaN(Number(editId)) ? 0 : Number(editId),
          }))
        );
        methods.setValue(
          'disbursementProgressSummaryDetailByRooms',
          data.listDisbursementProgressSummaryByRoomReportDto.map(item => ({
            ...item,
            id: item.id || -getRandomNumber(),
            disbursementProgressSummaryId:
              item.disbursementProgressSummaryId || isNaN(Number(editId)) ? 0 : Number(editId),
          }))
        );
      },
    }
  );

  const handleGetData = () => {
    const [year] = methods.getValues(['budgetYear']);
    if (year) {
      mutateDetails({
        year: year,
        disbursementProgressSummaryId: isNaN(Number(editId)) ? 0 : Number(editId),
      });
    }
  };

  return (
    <>
      <Form {...methods}>
        <form autoComplete="off">
          <PageLayout
            onSaveChange={e => {
              handleSubmit(e.event?.currentTarget as unknown as SyntheticEvent<HTMLElement>);
            }}
            header={editId !== 'new' ? t('page.form.edit') : t('page.form.addNew')}
            canSaveChange={!isNaN(Number(editId)) ? role?.isUpdate : role?.isCreate}
            isSaving={loading}
            onCancel={goBackToList}
            onDelete={editId !== 'new' ? onDelete : undefined}
            customElementLeft={
              <>
                <Button
                  text={t('content.createNew', { ns: 'common' })}
                  className="uppercase"
                  stylingMode="outlined"
                  type="default"
                  icon="plus"
                  onClick={onCreateNew}
                />
              </>
            }
            // customElementRight={
            //   <>
            //     <Button
            //       type="default"
            //       stylingMode="contained"
            //       icon="print"
            //       text={t('content.printA4', { ns: 'common' })}
            //       disabled={!role?.isPrint || !data}
            //       onClick={() => {
            //         // callbackWithTimeout(refetch);
            //         // setTimeout(() => {
            //         //   handlePrint();
            //         // }, 100);
            //       }}
            //     />
            //   </>
            // }
          >
            <div className="grid max-w-full grid-cols-1 gap-x-8  gap-y-4 xl:max-w-screen-2xl xl:grid-cols-24">
              {/* Cột 1 */}
              <div className="col-span-1 flex flex-col gap-x-8 gap-y-4 xl:col-span-12">
                {/* Năm ngân sách */}
                <div className="flex items-center">
                  <FormLabel name="budgetYear" htmlFor="budgetYear" className="w-[100px]">
                    {t('fields.budgetYear')}
                  </FormLabel>
                  <FormField
                    id="budgetYear"
                    name="budgetYear"
                    className="flex-1 md:w-[250px]"
                    type="date"
                  >
                    <DateBox
                      placeholder={`${selectLabel} ${t('fields.budgetYear')}`}
                      calendarOptions={{
                        maxZoomLevel: 'decade',
                        minZoomLevel: 'decade',
                      }}
                      displayFormat={'year'}
                      pickerType="calendar"
                      focusStateEnabled={false}
                    />
                  </FormField>
                </div>

                {/* Ghi chú */}
                <div className="flex items-center">
                  <FormLabel name="note" htmlFor="note" className="w-[100px]">
                    {t('fields.note')}
                  </FormLabel>
                  <FormField id="note" name="note" className="flex-1 md:w-[250px]">
                    <TextBox placeholder={`${enterLabel} ${t('fields.note')}`} />
                  </FormField>
                </div>

                {/* Ngày lập */}
                <div className="flex items-center">
                  <FormLabel
                    name="disbursementProgressSummaryTime"
                    htmlFor="disbursementProgressSummaryTime"
                    className="w-[100px]"
                  >
                    {t('fields.disbursementProgressSummaryTime')}
                  </FormLabel>
                  <FormField
                    id="disbursementProgressSummaryTime"
                    name="disbursementProgressSummaryTime"
                    className="flex-1 md:w-[250px]"
                    type="date"
                  >
                    <DateBox
                      placeholder={`${selectLabel} ${t('fields.disbursementProgressSummaryTime')}`}
                      pickerType="calendar"
                      focusStateEnabled={false}
                    />
                  </FormField>
                </div>

                {/* Người lập */}
                <div className="flex items-center">
                  <FormLabel name="userCreatedId" htmlFor="userCreatedId" className="w-[100px]">
                    {t('fields.userCreatedId')}
                  </FormLabel>
                  <FormField
                    id="userCreatedId"
                    name="userCreatedId"
                    className="flex-1 md:w-[250px]"
                  >
                    <FormCombobox
                      placeholder={`${selectLabel} ${t('fields.userCreatedId')}`}
                      model="user"
                      queryKey={[QUERIES.USERS]}
                      disabled
                    />
                  </FormField>
                </div>
              </div>
            </div>
            <div className="mt-8">
              <Tabs defaultValue="detail">
                <div className="w-full">
                  <TabsList>
                    <TabsTrigger value="detail">{t('page.form.tabs.detail')}</TabsTrigger>
                    <TabsTrigger value="detailByRoom">
                      {t('page.form.tabs.detailByRoom')}
                    </TabsTrigger>
                  </TabsList>
                  <div className="col-span-1 flex justify-end xl:col-span-3 xl:justify-start">
                    <Button
                      text={t('action.getData', { ns: 'common' })}
                      className="w-fit"
                      stylingMode="contained"
                      type="default"
                      icon="search"
                      onClick={() => void handleGetData()}
                      disabled={loadingData}
                    />
                  </div>
                </div>
                <TabsContent value="detail" className="mt-4">
                  <DisbursementProgressSummaryDetailEditableTable role={role} />
                </TabsContent>
                <TabsContent value="detailByRoom" className="mt-4">
                  <DisbursementProgressSummaryDetailByRoomEditableTable role={role} />
                </TabsContent>
              </Tabs>
            </div>
            {/* <div style={{ display: "none" }}>{printoutElement}</div> */}
          </PageLayout>
        </form>
      </Form>
      <DeleteConfirmDialog
        model="disbursement-progress-summary"
        name={methods.getValues('note')!}
        open={isConfirmDeleteDialogOpen}
        toggle={toggleConfirmDeleteDialog}
        isDeleting={isDeleting}
        onConfirm={() => {
          deleteTarget();
          setTimeout(() => onCreateNew(), 0);
        }}
      />
    </>
  );
};
