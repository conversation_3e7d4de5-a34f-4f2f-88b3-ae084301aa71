import { Button } from '@/components/ui/button';
import { Command, CommandEmpty, CommandInput, CommandList } from '@/components/ui/command';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { searchLabel, selectLabel } from '@/constant';
import { useBoolean } from '@/hooks';
import useMobileDetect from '@/hooks/use-mobile-detect';
import i18next from '@/i18n';
import { interpolateStringTemplate, removeAccents } from '@/lib/text';
import { cn, hash } from '@/lib/utils';
import { ListComponentResponse } from '@/types';
import { Command as CommandPrimitive } from 'cmdk';
import { ChevronDown, X } from 'lucide-react';
import { ChangeEvent, UIEventHandler, useEffect, useRef, useState } from 'react';
import { Badge } from './ui/badge';
import { ScrollArea } from './ui/scroll-area';
import { Checkbox } from './ui/checkbox';

type ComboboxProps<TRecord> = {
  label?: string;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
  multiple?: boolean;
  isTable?: boolean;
  //
  options: TRecord[];
  showFields?: (keyof TRecord)[];
  defaultText?: string | null;
  formatLabel?: string;
  //
  value?: string | number | (string | number)[];
  valueField?: keyof TRecord;
  isWrap?: boolean;
  //
  onChange?: (selected: string | number | (string | number)[] | null) => void;
  onSelectItem?: (values: TRecord | null) => void;
  onSelectedItems?: (values: TRecord[]) => void;
  onScroll?: UIEventHandler<HTMLDivElement> | undefined;
  onSearch?: (search: string | undefined) => void;
  onClickOutSide?: (openValue: boolean) => void;
};

export const Combobox = <TRecord extends ListComponentResponse>({
  disabled,
  label = '',
  placeholder = `${selectLabel.toLocaleLowerCase()} ${label.toLocaleLowerCase()}`,
  className,
  multiple,
  isTable,
  //
  options = [],
  showFields = ['name'],
  defaultText,
  formatLabel,
  //
  value,
  valueField = 'id',
  isWrap = false,
  //
  onChange,
  onSelectItem,
  onSelectedItems,
  onScroll,
  onSearch,
  onClickOutSide,
  ...props
}: ComboboxProps<TRecord>) => {
  const isMobilePhone = useMobileDetect();

  const { state: open, setFalse: close, toggle } = useBoolean();

  const mapper = hash(options, String(valueField));

  const [selected, setSelected] = useState<number[]>([]);

  useEffect(() => {
    if (value && value !== '' && multiple && Array.isArray(value) && selected.length === 0) {
      // Lọc ra các giá trị hợp lệ (không phải chuỗi rỗng và không phải 0)
      const validValues = (value as never[]).filter(v => v !== '' && v !== 0);
      if (validValues.length > 0) {
        setSelected(validValues.map(Number));
      } else {
        setSelected([]);
      }
    }
  }, [multiple, selected.length, value]);

  const refPrevSelectedItem = useRef<{ selectedItem: TRecord | null }>({ selectedItem: null });

  const getLabel = (
    item: TRecord & Record<string, unknown>,
    fields: (keyof TRecord)[] = showFields
  ): string => {
    if (formatLabel) {
      return interpolateStringTemplate(formatLabel, item);
    }

    return fields
      .filter(field => item[field])
      .map(field => item[field])
      .join(', ');
  };

  const getSelectedLabel = (): string => {
    if (
      (multiple && selected?.length === 0) ||
      (!multiple && (value === null || value === undefined || value === '' || value === 0))
    ) {
      return placeholder;
    }

    const selectedItem = mapper?.[value as number | string];
    if (!selectedItem) {
      if (refPrevSelectedItem && refPrevSelectedItem.current.selectedItem)
        return getLabel(refPrevSelectedItem.current.selectedItem);

      return (defaultText ?? (value || 0)).toString();
    } else {
      refPrevSelectedItem.current.selectedItem = selectedItem;
    }
    return getLabel(selectedItem);
  };

  const filter = (value: string, search: string): number => {
    if (!mapper || !mapper[value]) {
      return 0;
    }

    const text = getLabel(mapper[value], [...showFields, 'code', 'name']).toLocaleLowerCase();
    const label = removeAccents(text);
    const searchTerm = removeAccents(search.toLocaleLowerCase());

    return label.includes(searchTerm) ? 1 : 0;
  };

  const handleUnselect = (item: number | string) => {
    const newSelected = selected.filter(i => i !== item);
    setSelected(newSelected);

    // Cập nhật giá trị cho component cha
    if (onChange) {
      onChange(newSelected);
    }

    // Cập nhật danh sách các item đã chọn
    if (mapper && onSelectedItems) {
      onSelectedItems(newSelected.map(i => mapper[i]));
    }
  };

  const handleSelect = (item: TRecord) => {
    if (!item) return;

    const singleValue = item[valueField] as never;
    if (multiple) {
      let selectedIds;

      if (selected?.includes(singleValue)) {
        selectedIds = selected.filter(i => i !== singleValue);
      } else {
        selectedIds = [...(selected || []), singleValue];
      }

      setSelected(selectedIds);
    } else {
      onChange?.(singleValue);
      onSelectItem?.(item);
    }

    if (!multiple) {
      close();
    }
  };

  const [search, setSearch] = useState<string>();
  const [debounce, setDebounce] = useState<string>();
  const [selectAll, setSelectAll] = useState<boolean>(false);

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      setDebounce(search);
    }, 500);
    return () => clearTimeout(timeoutId);
  }, [search]);

  useEffect(() => {
    onSearch?.(debounce);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [debounce]);

  // Kiểm tra trạng thái "Chọn tất cả" khi danh sách selected thay đổi
  useEffect(() => {
    if (options.length > 0 && selected.length === options.length) {
      setSelectAll(true);
    } else {
      setSelectAll(false);
    }
  }, [selected, options]);

  // Xử lý khi nhấn vào "Chọn tất cả"
  const handleSelectAll = () => {
    if (selectAll) {
      // Bỏ chọn tất cả
      setSelected([]);
      setSelectAll(false);
      onChange?.([]);
      onSelectedItems?.([]);
    } else {
      // Chọn tất cả
      const allIds = options.map(item => Number(item[valueField]));
      setSelected(allIds);
      setSelectAll(true);
      onChange?.(allIds);
      if (mapper) onSelectedItems?.(options);
    }
  };

  return (
    <Popover
      open={open}
      onOpenChange={openValue => {
        toggle();
        if (multiple && open) {
          // onSelectedItems?.(selected.map(i => mapper?.[i] ));
          if (mapper) onSelectedItems?.(selected.map(i => mapper[i]));
          onChange?.(selected);
        }
        !openValue && onClickOutSide && onClickOutSide(openValue);
      }}
      modal={true}
      {...props}
    >
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={cn(
            'relative flex w-full items-center justify-between pl-3 pr-1 text-left font-normal',
            isTable ? 'h-auto min-h-[2rem] py-1' : 'h-8',
            'rounded-none border-0 border-b border-[#0000006b] hover:border-b-[rgba(0,_0,_0,_0.87)]',
            (value === 0 ||
              value === '' ||
              value === null ||
              value === undefined ||
              (Array.isArray(value) && value.length === 0)) &&
            'text-muted-foreground',
            open && 'border-b-2 !border-[#379ae6]',
            className
          )}
          disabled={disabled}
          {...props}
        >
          <div className={cn('mr-1 min-w-0 flex-1 overflow-hidden', isTable && 'h-auto min-h-fit')}>
            {multiple ? (
              selected?.length > 0 ? (
                <div className={cn('flex flex-row gap-1 overflow-hidden', isWrap ? 'flex-wrap' : 'flex-nowrap')}>
                  {/* <div className="flex flex-row flex-wrap gap-1 overflow-hidden"> */}
                  {selected?.map(item => {
                    const entity = mapper?.[item];
                    return (
                      <div key={item} className="flex-shrink-0">
                        <Badge variant="secondary" className="whitespace-nowrap font-normal">
                          <button
                            className="-ml-1 mr-1 rounded-full outline-none ring-offset-background focus:ring-2 focus:ring-ring focus:ring-offset-2"
                            onKeyDown={e => {
                              if (e.key === 'Enter') {
                                handleUnselect(item);
                              }
                            }}
                            onMouseDown={e => {
                              e.preventDefault();
                              e.stopPropagation();
                            }}
                            onClick={e => {
                              e.preventDefault();
                              e.stopPropagation();
                              handleUnselect(item);
                            }}
                          >
                            <X className="h-3 w-3 text-muted-foreground hover:text-foreground" />
                          </button>
                          {entity
                            ? getLabel(entity)
                            : String(item) !== '' && Number(item) !== 0
                              ? item
                              : placeholder}
                        </Badge>
                      </div>
                    );
                  })}
                </div>
              ) : (
                <span className="text-muted-foreground">{placeholder}</span>
              )
            ) : (
              <span
                className={cn(
                  isTable
                    ? 'block h-auto min-h-fit min-w-0 flex-1 overflow-visible whitespace-normal break-words'
                    : 'block min-w-0 flex-1 overflow-hidden text-ellipsis'
                )}
                style={
                  isTable
                    ? { height: 'auto', minHeight: 'fit-content', lineHeight: 1.5 }
                    : { display: '-webkit-box', WebkitLineClamp: 1, WebkitBoxOrient: 'vertical' }
                }
              >
                {getSelectedLabel()}
              </span>
            )}
          </div>
          <div className="flex flex-shrink-0 items-center space-x-1">
            {!multiple && value ? (
              <X
                size={16}
                className="cursor-pointer opacity-60"
                onClick={e => {
                  e.stopPropagation();
                  if (onChange) {
                    onChange(null);
                  }
                  onSelectItem?.(null);
                }}
              />
            ) : null}
            {multiple && selected.length > 0 && (
              <Badge
                variant="secondary"
                className="flex-shrink-0"
                style={{ fontSize: '10px', padding: '2px 4px' }}
              >
                {selected.length}
              </Badge>
            )}
            <ChevronDown size={18} className="opacity-60" />
          </div>
        </Button>
      </PopoverTrigger>
      <PopoverContent
        className="z-[1001] w-full max-w-[calc(100vw-32px)] p-0"
        align="start"
        onOpenAutoFocus={e => {
          if (isMobilePhone) {
            e.preventDefault();
          }
        }}
      >
        <Command filter={filter}>
          <CommandInput
            placeholder={`${searchLabel}...`}
            className="top-0"
            onChangeCapture={(e: ChangeEvent<HTMLInputElement>) => setSearch(e.target.value)}
          />
          <CommandEmpty>{i18next.t('notFound')}</CommandEmpty>
          {multiple && (
            <div
              className="flex cursor-pointer items-center px-3 py-2 hover:bg-zinc-100"
              onClick={handleSelectAll}
            >
              <Checkbox checked={selectAll} />
              <span className="ml-2">Chọn tất cả</span>
            </div>
          )}
          <ScrollArea type="always" className="max-h-[35vh] overflow-auto" onScroll={onScroll}>
            <CommandList>
              {options?.map((item: TRecord) => {
                const isChecked = multiple
                  ? selected?.includes(item[valueField] as never)
                  : Number(item[valueField]) === Number(value);

                return (
                  <CommandPrimitive.Item
                    value={String(item[valueField])}
                    key={item.id}
                    onSelect={() => handleSelect(item)}
                    className={cn(
                      'font px-[11px] py-[10px] text-[13px] transition-all hover:cursor-pointer hover:bg-zinc-300/30 active:bg-[#ccc]',
                      isChecked && 'bg-zinc-300/60 hover:bg-[#00000024]'
                    )}
                    style={{
                      fontFamily:
                        "'Roboto, RobotoFallback, 'Noto Kufi Arabic', Helvetica, Arial, sans-serif'",
                    }}
                  >
                    {getLabel(item)}
                  </CommandPrimitive.Item>
                );
              })}
            </CommandList>
          </ScrollArea>
        </Command>
      </PopoverContent>
    </Popover>
  );
};
