import { BasicDialog } from '@/components/basic-dialog';
import { DevexDataGrid } from '@/components/devex-data-grid';
import { QUERIES, TABLES } from '@/constant';
import { callbackWithTimeout, cn } from '@/lib/utils';
import { createQueryReport } from '@/services';
import { useQuery } from '@tanstack/react-query';
import { Column, Editing, Selection } from 'devextreme-react/data-grid';
import { useTranslation } from 'react-i18next';

export interface ProjectDebtStatusStatisticsReportDetail {
  id: number;
  ordinalNumber: number;
  code: string;
  dateBill: Date;
  contractorId: number;
  contractorCode: string;
  contractorName: string;
  costItemId: number;
  costItemCode: string;
  costItemName: string;
  proposedSettlementValue: number;
  disbursedCapital: number;
}

export const FinancialSettlementReportProjectDebtStatusStatisticsReportDetails = ({
  open,
  toggle,
  contractorId,
  costItemId,
}: {
  open: boolean;
  toggle: () => void;
  contractorId: number;
  costItemId: number;
}) => {
  const { t } = useTranslation('projectDebtStatusStatisticsReport');

  const { data, refetch } = useQuery({
    queryKey: [QUERIES.PROJECT_DEBT_STATUS_STATISTICS_REPORT, contractorId, costItemId],
    queryFn: () => {
      return createQueryReport<ProjectDebtStatusStatisticsReportDetail>(
        'project-debt-status-statistics-report',
        true
      )({
        pageIndex: 1,
        pageSize: -1,
        sortColumn: 'Id',
        sortOrder: 1,
        isPage: false,
        filterColumn: [],
        objParam: {
          contractorId: contractorId.toString(),
          costItemId: costItemId.toString(),
        },
      });
    },
    enabled: !!contractorId && !!costItemId,
  });

  const { items } = data || { items: [] };

  return (
    <BasicDialog open={open} toggle={() => toggle()} title={t('page.tabs.detail')}>
      <div className="max-w-fit overflow-scroll">
        <div className={cn('h-full opacity-100')}>
          <div className="grid grid-cols-24">
            <div className="col-span-24">
              <DevexDataGrid
                id={TABLES.CONTRACT}
                dataSource={items}
                onRefresh={() => {
                  callbackWithTimeout(refetch);
                }}
                keyExpr={'ordinalNumber'}
              >
                <Editing allowUpdating={false} allowDeleting={false} useIcons />
                <Selection mode="single" />
                <Column dataField="code" caption={t('fields.code')} />
                <Column
                  dataField="dateBill"
                  caption={t('fields.dateBill')}
                  dataType="date"
                  format="dd/MM/yyyy"
                />
                <Column dataField="contractorName" caption={t('fields.contractorName')} />
                <Column dataField="costItemName" caption={t('fields.costItemName')} />
                <Column
                  dataField="proposedSettlementValue"
                  caption={t('fields.proposedSettlementValue')}
                  dataType="number"
                  format="#,##0"
                  alignment="right"
                />
                <Column
                  dataField="disbursedCapital"
                  caption={t('fields.disbursedCapital')}
                  dataType="number"
                  format="#,##0"
                  alignment="right"
                />
              </DevexDataGrid>
            </div>
          </div>
        </div>
      </div>
    </BasicDialog>
  );
};
