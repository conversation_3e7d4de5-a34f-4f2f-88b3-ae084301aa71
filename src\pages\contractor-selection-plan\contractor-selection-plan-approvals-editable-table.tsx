import { ErrorMessage } from '@/components/ui/error-message';
import { AGENCY_TYPE, QUERIES, TABLES } from '@/constant';
import {
  Agency,
  ContractorSelectionPlan,
  ContractorSelectionPlanApproval,
  defaultValuesContractorSelectionPlanApproval,
  IUserPermission,
} from '@/types';
import { useFormContext, useWatch } from 'react-hook-form';

import { CellContext, ColumnDef } from '@tanstack/react-table';

import {
  DataTable,
  DataTableRowActions,
  EditableDatePickerCell,
  EditableDropdownCell,
  EditableInputCell,
  EditableTextArea,
} from '@/components/data-table';
import { translationWithNamespace } from '@/lib/i18nUtils';
import { getRandomNumber } from '@/lib/number';

const defaultRow = defaultValuesContractorSelectionPlanApproval;

type ContractorSelectionPlanEditableTableProps = {
  role?: IUserPermission;
  calculateForm?: () => void;
};

export const ContractorSelectionPlanApprovalsEditableTable = ({
  role,
  calculateForm,
}: ContractorSelectionPlanEditableTableProps) => {
  const t = translationWithNamespace('contractorSelectionPlan');
  // const isMobile = useMediaQuery('(max-width: 768px)');
  const {
    setValue,
    control,
    formState: { errors },
  } = useFormContext<ContractorSelectionPlan>();

  const [editableData] = useWatch({
    control,
    name: ['contractorSelectionPlanApprovals'],
  });

  const contractorSelectionPlanEditableColumns: ColumnDef<ContractorSelectionPlanApproval>[] = [
    {
      id: 'approvalDate',
      accessorKey: 'approvalDate',
      header: t('fields.contractorSelectionPlanApprovals.approvalDate'),
      cell: (props: CellContext<ContractorSelectionPlanApproval, unknown>) => (
        <EditableDatePickerCell {...props} />
      ),
    },
    {
      id: 'approvalNumber',
      accessorKey: 'approvalNumber',
      header: t('fields.contractorSelectionPlanApprovals.approvalNumber'),
      cell: props => (
        <EditableInputCell
          {...props}
          type="text"
          placeholder={props.row.original.agencyName || ''}
        />
      ),
    },
    {
      id: 'agencyId',
      accessorKey: 'agencyId',
      header: t('fields.contractorSelectionPlanApprovals.agencyId'),
      cell: props => (
        <EditableDropdownCell<ContractorSelectionPlanApproval, Agency>
          {...props}
          model="agency"
          queryKey={[QUERIES.AGENCY]}
          filter={item => item.agencyType === AGENCY_TYPE.ISSUE}
        />
      ),
    },
    {
      id: 'approvalContent',
      accessorKey: 'approvalContent',
      header: t('fields.contractorSelectionPlanApprovals.approvalContent'),
      cell: props => <EditableTextArea {...props} />,
    },
    {
      id: 'removeRow',
      header: ' ',
      size: 10,
      cell: props => {
        return (
          <DataTableRowActions
            onDelete={() => {
              props.table.options.meta?.removeRowByIndex(props.row.index);
            }}
            canDelete={role?.isCreate || role?.isUpdate}
          />
        );
      },
    },
  ];

  return (
    <div>
      <DataTable
        tableId={TABLES.CONTRACTOR_SELECTION_PLAN_DETAIL}
        sortColumn="id"
        role={role}
        editableData={editableData || []}
        setEditableData={editedData => {
          setValue('contractorSelectionPlanApprovals', editedData);
          calculateForm?.();
        }}
        onAddButtonClick={table => {
          const newRow = {
            ...defaultRow,
            tenderPackageName: '',
            id: -getRandomNumber(),
          };
          table.options.meta?.addNewRow(newRow);
        }}
        columns={contractorSelectionPlanEditableColumns}
        syncQueryParams={false}
        customToolbar={() => {
          return (
            <>
              {errors.contractorSelectionPlanDetails?.message && (
                <ErrorMessage message={errors.contractorSelectionPlanDetails?.message} />
              )}
            </>
          );
        }}
      />
    </div>
  );
};
