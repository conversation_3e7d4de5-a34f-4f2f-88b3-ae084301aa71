import { ErrorMessage } from '@/components/ui/error-message';
import { QUERIES, TABLES } from '@/constant';
import {
  BudgetFund,
  FinancialSettlementReport,
  FinancialSettlementReportDataReconciliationTable,
  IUserPermission,
} from '@/types';
import { useFormContext, useWatch } from 'react-hook-form';

import { CellContext, ColumnDef } from '@tanstack/react-table';

import {
  DataTable,
  EditableDropdownCell,
  EditableInputCell,
  NumberCell,
} from '@/components/data-table';
import { translationWithNamespace } from '@/lib/i18nUtils';
import { useMemo } from 'react';

type FinancialSettlementReportDataReconciliationEditableTableProps = {
  role?: IUserPermission;
  calculateForm?: () => void;
};

const t = translationWithNamespace('financialSettlementReport');

export const FinancialSettlementReportDataReconciliationEditableTable = ({
  role,
  calculateForm,
}: FinancialSettlementReportDataReconciliationEditableTableProps) => {
  const {
    setValue,
    control,
    formState: { errors },
  } = useFormContext<FinancialSettlementReport>();

  const [editableData] = useWatch({
    control,
    name: ['financialSettlementReportDataReconciliationTables'],
  });

  const financialSettlementReportDataReconciliationEditableColumns: ColumnDef<FinancialSettlementReportDataReconciliationTable>[] =
    useMemo(
      () => [
        {
          id: 'budgetYear', // Năm ngân sách
          accessorKey: 'budgetYear',
          header: t('fields.financialSettlementReportDataReconciliationTables.budgetYear'),
          cell: (props: CellContext<FinancialSettlementReportDataReconciliationTable, unknown>) => (
            <EditableInputCell {...props} readOnly />
          ),
        },
        {
          id: 'budgetFundId', // Nội dung
          accessorKey: 'budgetFundId',
          header: t('fields.financialSettlementReportDataReconciliationTables.budgetFundId'),
          cell: (props: CellContext<FinancialSettlementReportDataReconciliationTable, unknown>) => (
            <EditableDropdownCell
              {...props}
              disabled
              model="budget-fund"
              queryKey={[QUERIES.BUDGET_FUND]}
              onSelectItem={(selectedProject: BudgetFund | null) => {
                if (!selectedProject) {
                  return;
                }
              }}
            />
          ),
          aggregatedCell: () => {
            return null;
          },
        },
        //group
        {
          id: 'member1',
          header: t('fields.member1'),
          columns: [
            {
              id: 'poPlannedCapital', // Số liệu của chủ đầu tư - vốn kế hoạch
              accessorKey: 'poPlannedCapital',
              header: t(
                'fields.financialSettlementReportDataReconciliationTables.poPlannedCapital'
              ),
              cell: (
                props: CellContext<FinancialSettlementReportDataReconciliationTable, unknown>
              ) => {
                return <EditableInputCell {...props} type="number" isMoney readOnly />;
              },
              aggregatedCell: NumberCell,
            },
            //Số vốn đã giải ngân
            {
              id: 'member1.1',
              header: t('fields.member1.1'),
              columns: [
                // - Tổng số
                {
                  id: 'poDisbursedTotal', // Số liệu của chủ đầu tư - Số vốn đã giải ngân - Tổng số
                  accessorKey: 'poDisbursedTotal',
                  header: t(
                    'fields.financialSettlementReportDataReconciliationTables.poDisbursedTotal'
                  ),
                  cell: (
                    props: CellContext<FinancialSettlementReportDataReconciliationTable, unknown>
                  ) => <EditableInputCell {...props} type="number" isMoney readOnly />,
                  aggregatedCell: NumberCell,
                },

                {
                  id: 'poDisbursedCompletedWorkload', // Số liệu của chủ đầu tư - Số vốn đã giải ngân - Thanh toán khối lượng hoàn thành
                  accessorKey: 'poDisbursedCompletedWorkload',
                  header: t(
                    'fields.financialSettlementReportDataReconciliationTables.poDisbursedCompletedWorkload'
                  ),
                  cell: (
                    props: CellContext<FinancialSettlementReportDataReconciliationTable, unknown>
                  ) => <EditableInputCell {...props} type="number" isMoney readOnly />,
                  aggregatedCell: NumberCell,
                },

                {
                  id: 'poDisbursedAdvance', // Số liệu của chủ đầu tư - Số vốn đã giải ngân - Tạm ứng
                  accessorKey: 'poDisbursedAdvance',
                  header: t(
                    'fields.financialSettlementReportDataReconciliationTables.poDisbursedAdvance'
                  ),
                  cell: (
                    props: CellContext<FinancialSettlementReportDataReconciliationTable, unknown>
                  ) => <EditableInputCell {...props} type="number" isMoney readOnly />,
                  aggregatedCell: NumberCell,
                },
              ],
            },
          ],
        },
        //group 2
        {
          id: 'member2',
          header: t('fields.member2'),
          columns: [
            //  vốn kế hoạch
            {
              id: 'amountPlannedCapital', // Thành tiền - Kế hoạch vốn
              accessorKey: 'amountPlannedCapital',
              header: t(
                'fields.financialSettlementReportDataReconciliationTables.amountPlannedCapital'
              ),
              cell: (
                props: CellContext<FinancialSettlementReportDataReconciliationTable, unknown>
              ) => <EditableInputCell {...props} type="number" readOnly isMoney />,
              aggregatedCell: NumberCell,
            },
            {
              id: 'member2.1',
              header: t('fields.member2.1'),
              columns: [
                // - Tổng số
                {
                  id: 'amountAllocatedTotal', // Thành tiền - Số vốn đã cấp, cho vay, thanh toán - Tổng số
                  accessorKey: 'amountAllocatedTotal',
                  header: t(
                    'fields.financialSettlementReportDataReconciliationTables.amountAllocatedTotal'
                  ),
                  cell: (
                    props: CellContext<FinancialSettlementReportDataReconciliationTable, unknown>
                  ) => <EditableInputCell {...props} type="number" readOnly isMoney />,
                  aggregatedCell: NumberCell,
                },

                {
                  id: 'amountAllocatedCompletedWorkload', // Thành tiền - Số vốn đã cấp, cho vay, thanh toán - Thanh toán KLHT
                  accessorKey: 'amountAllocatedCompletedWorkload',
                  header: t(
                    'fields.financialSettlementReportDataReconciliationTables.amountAllocatedCompletedWorkload'
                  ),
                  cell: (
                    props: CellContext<FinancialSettlementReportDataReconciliationTable, unknown>
                  ) => (
                    <EditableInputCell
                      {...props}
                      type="number"
                      isMoney
                      onValueChange={value => {
                        const currentAmountAllocatedCompletedWorkload = Number(value);
                        const { original } = props.row;
                        if (!original) return;
                        const newAmountAllocatedTotal =
                          currentAmountAllocatedCompletedWorkload +
                          (original.amountAllocatedAdvance ?? 0);

                        const newDifference =
                          newAmountAllocatedTotal - (original.poDisbursedTotal ?? 0);

                        props.table.options.meta?.updateRowValues(
                          {
                            ...original,
                            amountAllocatedCompletedWorkload:
                              currentAmountAllocatedCompletedWorkload,
                            amountAllocatedTotal: newAmountAllocatedTotal,
                            difference: newDifference,
                          },
                          props.row.index
                        );
                      }}
                    />
                  ),
                  aggregatedCell: NumberCell,
                },

                {
                  id: 'amountAllocatedAdvance', // Thành tiền - Số vốn đã cấp, cho vay, thanh toán - Tạm ứng
                  accessorKey: 'amountAllocatedAdvance',
                  header: t(
                    'fields.financialSettlementReportDataReconciliationTables.amountAllocatedAdvance'
                  ),
                  cell: (
                    props: CellContext<FinancialSettlementReportDataReconciliationTable, unknown>
                  ) => (
                    <EditableInputCell
                      {...props}
                      type="number"
                      isMoney
                      onValueChange={value => {
                        const currentAmountAllocatedAdvance = Number(value);
                        const { original } = props.row;
                        if (!original) return;

                        const newAmountAllocatedTotal =
                          currentAmountAllocatedAdvance +
                          (original.amountAllocatedCompletedWorkload ?? 0);

                        const newDifference =
                          newAmountAllocatedTotal - (original.poDisbursedTotal ?? 0);
                        props.table.options.meta?.updateRowValues(
                          {
                            ...original,
                            amountAllocatedAdvance: currentAmountAllocatedAdvance,
                            amountAllocatedTotal: newAmountAllocatedTotal,
                            difference: newDifference,
                          },
                          props.row.index
                        );
                      }}
                    />
                  ),
                  aggregatedCell: NumberCell,
                },
              ],
            },
          ],
        },
        //group 3
        {
          id: 'member3',
          header: '',
          columns: [
            //Chênh lệch
            {
              id: 'difference', // Chênh lệch
              accessorKey: 'difference',
              header: t('fields.financialSettlementReportDataReconciliationTables.difference'),
              cell: (
                props: CellContext<FinancialSettlementReportDataReconciliationTable, unknown>
              ) => <EditableInputCell {...props} type="number" readOnly />,
              aggregatedCell: NumberCell,
            },

            {
              id: 'notes', // Ghi chú
              accessorKey: 'notes',
              header: t('fields.financialSettlementReportDataReconciliationTables.notes'),
              cell: (
                props: CellContext<FinancialSettlementReportDataReconciliationTable, unknown>
              ) => <EditableInputCell {...props} />,
              aggregatedCell: NumberCell,
            },
          ],
        },
      ],
      []
    );

  return (
    <div>
      <DataTable
        tableId={TABLES.FINANCIAL_SETTLEMENT_REPORT_DATA_RECONCILIATION}
        sortColumn="id"
        role={role}
        editableData={editableData}
        setEditableData={editedData => {
          setValue('financialSettlementReportDataReconciliationTables', editedData);
          calculateForm?.();
        }}
        // onAddButtonClick={table => {
        //   const newRow = { ...defaultRow, id: -getRandomNumber() };
        //   table.options.meta?.addNewRow(newRow);
        // }}
        manualGrouping={false}
        grouping={['budgetYear']}
        syncQueryParams={false}
        columns={financialSettlementReportDataReconciliationEditableColumns}
        customToolbar={() => {
          return (
            <>
              {errors.financialSettlementReportDataReconciliationTables?.message && (
                <ErrorMessage
                  message={errors.financialSettlementReportDataReconciliationTables?.message}
                />
              )}
            </>
          );
        }}
      />
    </div>
  );
};
