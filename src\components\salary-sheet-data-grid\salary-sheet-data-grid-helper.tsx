import { safeRenderString } from '@/lib/print';
import { decimalToVietnameseWords } from '@/lib/utils';
import { salarySheetPrintTemplate } from './template/salary-sheet-print-template';
import { PrintProps } from './salary-sheet-data-grid';

const formatter = new Intl.NumberFormat('de-DE', {
  minimumFractionDigits: 0,
  maximumFractionDigits: 2,
});

/**
 * Describes the structure of an object defining a column's desired
 * visibility and position in the final render order.
 */
export interface VisibleColumnOrderDefinition {
  dataField: string;
  caption?: string; // Optional, primarily for readability/debugging
  alignment?: string; // Optional, used for display styling if needed elsewhere
}

/**
 * Describes the hierarchical structure of a header column definition.
 * This is used as input and is recursively processed.
 * Properties like 'order' and '_calculatedColspan' are added during processing.
 */
export interface HeaderColumnNode {
  caption: string;
  dataField?: string; // Typically present on leaf nodes, optional on groups
  name?: string; // Optional identifier
  visible?: boolean; // Initial visibility hint, can be overridden by logic
  totalSummaryValue: string | number | null; // For footer totals
  alignment?: string; // For display styling

  // Recursive definition for group headers
  columns?: HeaderColumnNode[];

  // --- Properties added during processing ---
  /** The calculated display order (1-based index). Added by assignOrderFilterAndSort. */
  order?: number;
  /** The calculated colspan based on visible descendant leaf nodes. Added by calculateColspan. */
  _calculatedColspan: number;
  index?: number;
}

/**
 * Describes the structure of the raw cell data within a row from the input 'rows' array.
 */
interface RawCellColumnData {
  dataField: string; // Links cell to a header column definition
  text: string; // The value to display
  alignment?: string;
  caption?: string; // Often reflects the header caption, may not be used directly for render
  displayValue?: any; // Original underlying value, if needed
}

/**
 * Wrapper object often found in raw row data structures.
 */
interface RawCellWrapper {
  column: RawCellColumnData;
}

/**
 * Describes the structure of a single raw data row from the input 'rows' array.
 */
interface RawRowData {
  cells: RawCellWrapper[];
}

// --- Interfaces for Processed/Renderable Data ---
/**
 * Describes the data needed to render a single cell (<th>) in the table header (<thead>).
 */
interface RenderableHeaderCell {
  caption: string;
  colspan: number;
  rowspan: number;
}

/**
 * Describes a leaf column that is visible and will be rendered in the final table.
 * Used for ordering body cells and rendering the footer.
 */
export interface VisibleLeafColumn {
  dataField: string;
  caption: string; // Can be useful for debugging or tooltips
  totalSummaryValue?: string | number | null;
}

/**
 * Describes the data needed to render a single cell (<td>) in the table body (<tbody>).
 */
interface RenderableBodyCell {
  text: string;
  alignment?: string;
}

/**
 * Describes a processed row ready for rendering in the table body (<tbody>),
 * containing only the cells for visible columns in the correct order.
 */
interface ProcessedRow {
  visibleCells: RenderableBodyCell[];
}

/**
 * Describes the combined result of preparing the header structure and
 * identifying the visible leaf columns for rendering.
 */
interface PrepareTableRenderDataResult {
  headerRows: RenderableHeaderCell[][]; // Array of rows, each row is an array of cells
  maxDepth: number;
  visibleLeafColumns: VisibleLeafColumn[];
}

/**
 * Recursively calculates the colspan for a node based on its visible leaf descendants
 * within the already ordered/filtered structure.
 * Adds/updates the _calculatedColspan property on the node (mutation).
 * @param columnNode The current column definition object (must be from the ordered structure).
 * @returns The calculated colspan for this node.
 */
function calculateColspan(columnNode: HeaderColumnNode): number {
  // If the node made it this far, it's either a visible leaf OR a group
  // containing visible leaves. Its own initial 'visible' flag isn't the main factor now.
  // The primary check is presence of children after filtering/ordering.
  // However, we still need _calculatedColspan = 0 for groups that END UP empty.

  if (!columnNode.columns || columnNode.columns.length === 0) {
    // It's a leaf that survived filtering
    // Check its explicit 'visible' flag if assignOrderFilterAndSort didn't force it
    if (columnNode.visible === false) {
      // Double check
      columnNode._calculatedColspan = 0;
      return 0;
    }
    columnNode._calculatedColspan = 1;
    return 1;
  } else {
    // It's a group that survived filtering (meaning it had visible children)
    let colspan = 0;
    for (const child of columnNode.columns) {
      colspan += calculateColspan(child); // Recursively calculate for children
    }
    // If, after recursive calls, all children have colspan 0, this group gets 0 too
    columnNode._calculatedColspan = colspan;
    if (colspan === 0) {
      columnNode.visible = false; // Update visible flag if it turned out empty
    }
    return colspan;
  }
}

/**
 * Calculates the maximum depth required for the header rows based on the
 * visible and rendering columns (uses _calculatedColspan).
 * @param columns Array of column nodes (must be from the ordered structure with colspans calculated).
 * @param currentDepth Internal recursion parameter.
 * @returns The maximum depth needed for header rows.
 */
function getVisibleMaxDepth(columns: HeaderColumnNode[], currentDepth = 1): number {
  let maxDepth = currentDepth;
  let levelHasRenderableNodes = false; // Check if nodes *render* (have colspan)

  for (const column of columns) {
    // Check if the node *renders* based on its calculated colspan
    if (column._calculatedColspan > 0) {
      levelHasRenderableNodes = true;
      if (column.columns && column.columns.length > 0) {
        // Recurse if it's a group with rendering children
        const childDepth = getVisibleMaxDepth(column.columns, currentDepth + 1);
        if (childDepth > maxDepth) {
          maxDepth = childDepth;
        }
      }
      // else: it's a rendering leaf, contributes 'currentDepth' to potential max
    }
  }
  // Return the depth only if this level actually had renderable content
  return levelHasRenderableNodes ? maxDepth : currentDepth - 1;
}

/**
 * Recursively extracts the visible leaf columns in rendering order from the
 * ordered and processed header structure.
 * @param orderedCols Array of column definition objects (ordered, filtered, with colspans calculated).
 * @returns A flat array of visible leaf column definitions, ready for body/footer use.
 */
function extractVisibleLeafs(orderedCols: HeaderColumnNode[]): VisibleLeafColumn[] {
  let leafColumns: VisibleLeafColumn[] = [];
  for (const column of orderedCols) {
    // Use the visibility determined *after* filtering/ordering
    if (!column.visible || column._calculatedColspan === 0) {
      // Use colspan result for effective visibility
      continue;
    }

    if (column.columns && column.columns.length > 0) {
      // Check if the group actually renders children
      if (column._calculatedColspan > 0) {
        const childLeafs = extractVisibleLeafs(column.columns);
        leafColumns = leafColumns.concat(childLeafs);
      }
    } else if (column.dataField) {
      // It's a leaf node confirmed to be visible and renderable
      leafColumns.push({
        dataField: column.dataField,
        caption: column.caption,
        totalSummaryValue:
          Number(column.totalSummaryValue) > 0
            ? formatter.format(Number(column.totalSummaryValue))
            : column.totalSummaryValue,
      });
    }
  }
  return leafColumns;
}

/**
 * Prepares header structure AND extracts visible leaf columns from ORDERED columns.
 * @param {object[]} orderedAndFilteredColumns - The result from assignOrderFilterAndSort.
 * @returns {object} { headerRows, maxDepth, visibleLeafColumns }
 */
export function prepareTableRenderData(
  orderedAndFilteredColumns: HeaderColumnNode[]
): PrepareTableRenderDataResult {
  // Input array is already ordered and filtered.

  // 1. Calculate colspans on the final structure
  let totalColspan = 0;
  orderedAndFilteredColumns.forEach(col => {
    totalColspan += calculateColspan(col); // Modifies nodes in place
  });

  // If nothing renders, return empty
  if (totalColspan === 0) {
    // Even if orderedAndFilteredColumns had items, if none render, return empty.
    // This handles cases where visibleColumns might list items that are marked visible:false
    // in the original data and that override wasn't removed in assignOrderFilterAndSort.
    return { headerRows: [], maxDepth: 0, visibleLeafColumns: [] };
  }

  // 2. Calculate max depth based on the rendering structure
  let maxDepth = getVisibleMaxDepth(orderedAndFilteredColumns);
  maxDepth = maxDepth < 1 ? 1 : maxDepth;

  // 3. Build the header rows structure (iterates in the sorted order)
  const headerRows: RenderableHeaderCell[][] = Array.from({ length: maxDepth }, () => []);
  function buildRowsRecursive(columns: HeaderColumnNode[], currentLevel: number): void {
    if (currentLevel >= maxDepth) return;
    for (const column of columns) {
      // Only process nodes that WILL render (have _calculatedColspan > 0)
      if (!column._calculatedColspan || column._calculatedColspan === 0) continue;

      // Check if it *acts* like a group (has rendering children)
      const isEffectivelyGroup =
        column.columns &&
        column.columns.length > 0 &&
        column.columns.some(c => c._calculatedColspan > 0);

      const cellData: RenderableHeaderCell = {
        caption: column.caption,
        colspan: column._calculatedColspan,
        rowspan: isEffectivelyGroup ? 1 : maxDepth - currentLevel,
      };
      headerRows[currentLevel].push(cellData); // Added in correct order
      if (isEffectivelyGroup) {
        buildRowsRecursive(column.columns ?? [], currentLevel + 1);
      }
    }
  }

  buildRowsRecursive(orderedAndFilteredColumns, 0);

  // 4. Extract the final list of visible leaf columns (will be in sorted order)
  const visibleLeafColumns = extractVisibleLeafs(orderedAndFilteredColumns);

  return { headerRows, maxDepth, visibleLeafColumns };
}

/**
 * Prepares the raw row data for rendering in the table body (<tbody>).
 * Filters and orders the cells within each row to match the final visibleLeafColumns.
 * @param dataRows The raw input rows data array.
 * @param visibleLeafColumns The final, ordered list of leaf columns that will be rendered.
 * @returns An array of processed rows, each containing an ordered array of 'visibleCells'.
 */
export function prepareRowData(
  dataRows: RawRowData[],
  visibleLeafColumns: VisibleLeafColumn[]
): ProcessedRow[] {
  if (
    !dataRows ||
    dataRows.length === 0 ||
    !visibleLeafColumns ||
    visibleLeafColumns.length === 0
  ) {
    // Added check for visibleLeafColumns
    return [];
  }
  const processedRows = [];
  const visibleDataFields = new Set(visibleLeafColumns.map(c => c.dataField)); // Faster check

  for (const row of dataRows) {
    if (!row || !row.cells) continue;
    const cellMap = new Map();
    for (const cellWrapper of row.cells) {
      // Only map cells that are actually needed
      if (
        cellWrapper.column &&
        cellWrapper.column.dataField &&
        visibleDataFields.has(cellWrapper.column.dataField)
      ) {
        cellMap.set(cellWrapper.column.dataField, cellWrapper.column);
      }
    }

    const visibleCells = [];
    // Iterate through the *ordered* visibleLeafColumns list
    for (const leafColumn of visibleLeafColumns) {
      const matchingCell = cellMap.get(leafColumn.dataField);
      if (matchingCell) {
        visibleCells.push({
          text: matchingCell.text || '',
          alignment: matchingCell.alignment || 'left',
        });
      } else {
        visibleCells.push({ text: '', alignment: 'left' }); // Placeholder
      }
    }
    processedRows.push({ visibleCells: visibleCells });
  }
  return processedRows;
}

/**
 * Creates a Map for quick lookup of a column's desired order based on its dataField.
 * @param {Array} visibleCols - The flat array defining the visible columns and their order.
 * @returns {Map<string, number>} A Map where key is dataField and value is the 0-based index (order).
 */
export function createOrderLookup(
  visibleCols: VisibleColumnOrderDefinition[]
): Map<string, number> {
  const lookup = new Map<string, number>();
  visibleCols.forEach((col, index) => {
    if (col.dataField) {
      lookup.set(col.dataField, index); // Store 0-based index
    }
  });
  return lookup;
}

/**
 * Recursively assigns order, filters, and sorts columns based on the visibleColumns definition.
 * Modifies the 'order' and 'visible' properties of nodes and returns a new filtered/sorted array.
 * @param nodes The current array of column nodes to process.
 * @param orderLookup The Map created from visibleColumns.
 * @returns A new array containing the processed, filtered, and sorted nodes.
 */
export function assignOrderFilterAndSort(
  nodes: HeaderColumnNode[],
  orderLookup: Map<string, number>
): HeaderColumnNode[] {
  const processedNodes: HeaderColumnNode[] = [];

  for (const node of nodes) {
    const newNode: HeaderColumnNode = { ...node }; // Clone the node to avoid modifying the original
    let assignedOrder: number = Infinity; // Default order for comparison, higher means comes later or filtered out

    if (newNode.columns) {
      // --- It's a Group Node ---
      // Recursively process children first
      const processedChildren: HeaderColumnNode[] = assignOrderFilterAndSort(
        newNode.columns,
        orderLookup
      );

      if (processedChildren.length > 0) {
        // Keep the group ONLY if it has visible children
        newNode.columns = processedChildren;
        // Group's order is the minimum order of its visible children
        assignedOrder = processedChildren[0].order!; // Children are already sorted by order
        newNode.order = assignedOrder; // Assign the calculated order (using 1-based for clarity)
        processedNodes.push(newNode);
      }
      // Else: Group has no visible children, discard it (don't add to processedNodes)
    } else if (newNode.dataField) {
      // --- It's a Leaf Node ---
      if (orderLookup.has(newNode.dataField)) {
        // Leaf is defined in visibleColumns
        assignedOrder = orderLookup.get(newNode.dataField)! + 1; // Get 0-based index and make it 1-based
        newNode.order = assignedOrder;
        processedNodes.push(newNode);
      }
      // Else: Leaf is not in visibleColumns, discard it (don't add to processedNodes)
    }
    // Else: Node without dataField or columns? Could ignore or log warning. Handled implicitly.
  }

  // Sort the processed nodes at the *current level* by their assigned order
  processedNodes.sort((a, b) => a.order! - b.order!);

  return processedNodes;
}

// Add the visible property to the columns variable by using recursion to traverse all the columns
// and check if that column exists in the list of currently displayed columns.
export const getVisibleColumns = (
  columns: HeaderColumnNode[],
  visibleColumns: VisibleColumnOrderDefinition[],
  instance: any
): HeaderColumnNode[] => {
  return columns.map(column => {
    if (visibleColumns.length === 0) return column;
    const isVisible = visibleColumns.some(col => col.dataField === column.dataField);
    const newColumn: HeaderColumnNode = {
      ...column,
      visible: isVisible,
      // eslint-disable-next-line @typescript-eslint/no-unsafe-call
      totalSummaryValue: instance?.getTotalSummaryValue(column.dataField ?? '') ?? '',
      index: visibleColumns.findIndex(col => col.dataField === column.dataField),
    };
    if (column.columns) {
      newColumn.columns = getVisibleColumns(column.columns, visibleColumns || [], instance);
      newColumn.visible = newColumn.columns.some(col => col.visible);
    }

    return newColumn;
  });
};

export const convertColumnsToVisibleColumnOrderDefinitions = (
  columns: any[]
): VisibleColumnOrderDefinition[] => {
  return columns.map(column => ({
    dataField: column.dataField,
    caption: column.caption,
    visible: column.visible,
    width: column.width,
    alignment: column.alignment,
    allowSorting: column.allowSorting,
    allowFiltering: column.allowFiltering,
  }));
};

export const convertToHeaderColumnNodes = (columns: any[]): HeaderColumnNode[] => {
  return columns.map(column => ({
    name: column.dataField || column.name || '',
    caption: column.caption || '',
    dataType: column.dataType || 'string',
    visible: column.visible !== false,
    // eslint-disable-next-line @typescript-eslint/no-unsafe-argument
    children: column.columns,
    totalSummaryValue: null, // Default value for totalSummaryValue
    _calculatedColspan: 0, // Default value for _calculatedColspan
  }));
};

export const getNetSalaryTotalSummayValue = (
  visibleLeafColumns: VisibleLeafColumn[],
  columnName: string = 'netSalary',
  instance: any
): string => {
  if (visibleLeafColumns.length === 0) return decimalToVietnameseWords(0);
  // eslint-disable-next-line @typescript-eslint/no-unsafe-call
  const summaryValue = instance?.getTotalSummaryValue(columnName);
  return decimalToVietnameseWords(Number(summaryValue));
};

const getApplicableMonthYear = (salarySheet: any) => {
  if (!salarySheet) return { applicableMonth: '', applicableYear: '' };

  const applicableMonth: any = salarySheet.applicableMonth
    ? new Date(salarySheet.applicableMonth as string | number | Date).getMonth() + 1
    : '';
  const applicableYear: any = salarySheet.applicableYear
    ? new Date(salarySheet.applicableYear as string | number | Date).getFullYear()
    : '';

  return { applicableMonth, applicableYear };
};

export const preprocessPrint = (instance: any, printProps: PrintProps): any => {
  const { applicableMonth, applicableYear } = getApplicableMonthYear(printProps.salarySheet);
  if (!instance) {
    return safeRenderString(salarySheetPrintTemplate, {
      headerRows: [],
      visibleRows: [],
      visibleLeafColumns: [],
      maxDepth: [],
      headerTitle: printProps.headerTitle,
      summaryText: '',
      applicableMonth: '',
      applicableYear: '',
    });
  }
  // eslint-disable-next-line @typescript-eslint/no-unsafe-call
  const visibleRows = instance?.getVisibleRows();
  // Get visible columns
  // eslint-disable-next-line @typescript-eslint/no-unsafe-call
  const visibleColumns = instance?.getVisibleColumns();
  // Add visible, totalSummartValue property into rawColumns
  // eslint-disable-next-line @typescript-eslint/no-unsafe-call
  const rawColumn = instance?.option('columns');
  // eslint-disable-next-line @typescript-eslint/no-unsafe-argument
  const columns = getVisibleColumns(rawColumn, visibleColumns, instance);
  // order column by visible index
  // eslint-disable-next-line @typescript-eslint/no-unsafe-argument
  const columnOrders = createOrderLookup(visibleColumns);
  const sortedColumns = assignOrderFilterAndSort(columns, columnOrders);

  // Create table header, colspan, summary row
  const { headerRows, maxDepth, visibleLeafColumns } = prepareTableRenderData(sortedColumns);

  const template = safeRenderString(salarySheetPrintTemplate, {
    headerRows: headerRows,
    processedRows: visibleRows || [],
    visibleLeafColumns: visibleLeafColumns,
    maxDepth: maxDepth,
    headerTitle: printProps.headerTitle,
    summaryText: getNetSalaryTotalSummayValue(visibleLeafColumns, printProps.summaryCell, instance),
    applicableMonth: applicableMonth,
    applicableYear: applicableYear,
  });

  if (template.error) {
    console.error('Error rendering string:', template.error);
    return '';
  }

  return template;
};
