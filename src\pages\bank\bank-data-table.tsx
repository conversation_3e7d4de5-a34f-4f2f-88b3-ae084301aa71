import { BasicDialog } from '@/components/basic-dialog';
import { DeleteConfirmDialog } from '@/components/confirm-dialog';
import { DevexDataGrid } from '@/components/devex-data-grid';
import { DxActiveStatus } from '@/components/dx-active-status';
import { PageLayout } from '@/components/page-layout';
import { PeriodFilter } from '@/components/period-filter-form';
import { MUTATE, PERMISSIONS, QUERIES, TABLES } from '@/constant';
import { useDataTable, usePermission } from '@/hooks';
import { createExportingEvent } from '@/lib/file';
import { callbackWithTimeout } from '@/lib/utils';
import { createDeleteMutateFn, createQueryPaginationFn } from '@/services';
import { useQuery } from '@tanstack/react-query';
import { Button, Column, Export } from 'devextreme-react/data-grid';
import { ColumnButtonClickEvent } from 'devextreme/ui/data_grid';
import { BankForm } from './bank-form';
import { Bank } from '@/types';
import { translationWithNamespace } from '@/lib/i18nUtils';
import { snakeCase } from 'lodash';
import { removeAccents } from '@/lib/text';

//export excel
const t = translationWithNamespace('bank');

const exportFileName = snakeCase(removeAccents(t('model')));
const onExporting = createExportingEvent(`${exportFileName}.xlsx`, 'Main');

export const BankDataTable = () => {
  //lấy quyền
  const role = usePermission(PERMISSIONS.BANK);

  const { data, refetch } = useQuery({
    queryKey: [QUERIES.BANK],
    queryFn: () =>
      createQueryPaginationFn('bank')({
        pageIndex: 1,
        pageSize: -1,
        sortColumn: 'Id',
        sortOrder: 1,
        isPage: false,
        filterColumn: [],
      }),
  });

  const { items } = data || {};

  const getTargetAlias = (target: Bank | undefined) => {
    if (!target) {
      return '';
    }
    return `${target?.name}`;
  };

  const {
    // state
    selectedTarget,

    // delete feature
    isConfirmDeleteDialogOpen,
    toggleConfirmDeleteDialog,
    selectTargetToDelete,
    deleteTarget,
    isDeleting,

    // edit feature
    isEditDialogOpen,
    toggleEditDialog,
    selectTargetToEdit,
  } = useDataTable<Bank, PeriodFilter>({
    getTargetAlias,
    deleteFn: createDeleteMutateFn<Bank>('bank'),
    deleteKey: [MUTATE.DELETE_BANK],
    invalidateKey: [QUERIES.BANK],
  });

  const selectedId = selectedTarget?.id || 0;
  const onEditClick = (e: ColumnButtonClickEvent<Bank>) => {
    if (e.row?.data) {
      selectTargetToEdit(e.row.data);
    }
  };

  const onAddClick = () => {
    selectTargetToEdit({ id: 0 } as Bank);
  };

  const onDeleteClick = (e: ColumnButtonClickEvent<Bank>) => {
    if (e.row?.data) {
      selectTargetToDelete(e.row.data);
    }
  };

  return (
    <PageLayout header={t('page.header')}>
      <DevexDataGrid
        id={TABLES.BANK}
        dataSource={items}
        onAddNewClick={onAddClick}
        onRefresh={() => callbackWithTimeout(refetch)}
        onExporting={onExporting}
        hoverStateEnabled
        focusedRowEnabled={true}
      >
        <Export enabled={true} />

        {/* thao tác */}
        <Column type="buttons">
          <Button name="edit" onClick={onEditClick} />
          <Button name="delete" onClick={onDeleteClick} />
        </Column>

        {/* Trường dữ liệu */}
        <Column dataField="code" caption={t('fields.code')} />
        <Column dataField="name" caption={t('fields.name')} />
        <Column dataField="note" caption={t('fields.note')} />
        <Column
          dataField="isActive"
          caption={t('fields.isActive')}
          dataType="boolean"
          cellRender={DxActiveStatus}
        />
      </DevexDataGrid>
      <BasicDialog
        className="w-full md:w-auto"
        open={isEditDialogOpen}
        toggle={toggleEditDialog}
        title={selectedId ? t('page.form.edit') : t('page.form.addNew')}
      >
        <BankForm role={role} editId={selectedId} />
      </BasicDialog>
      <DeleteConfirmDialog
        isDeleting={isDeleting}
        onConfirm={deleteTarget}
        open={isConfirmDeleteDialogOpen}
        toggle={toggleConfirmDeleteDialog}
        model="bank"
        name={getTargetAlias(selectedTarget)}
      />
    </PageLayout>
  );
};
