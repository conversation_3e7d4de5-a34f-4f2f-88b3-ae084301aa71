{"model": "<PERSON>h<PERSON>ng kê dự án", "page": {"header": "Thống kê DA", "budgetYear": "<PERSON><PERSON><PERSON> ng<PERSON> s<PERSON>ch"}, "fields": {"name": "<PERSON><PERSON>nh hình triển khai dự án", "total": "Tổng", "sum": "<PERSON><PERSON><PERSON> cộng", "ordinalNumber": "Thống kê DA", "budgetFundId": "<PERSON>d ng<PERSON><PERSON><PERSON> v<PERSON>n", "budgetFundCode": "<PERSON><PERSON> nguồn vốn", "budgetFundName": "<PERSON><PERSON><PERSON> ngu<PERSON>n vốn", "parentId": "<PERSON><PERSON><PERSON><PERSON> nguồn vốn", "numProjectsCompletedConstruction": "Số DA đã thi công hoàn thành ", "numProjectsUnderConstruction": "Số DA đang triển khai thi công xây dựng ", "numProjectsProcurementInProgress": "Số DA đang triển khai LCNT thi công xây dựng ", "numProjectsDesignInProgress": "Số DA đang lập thiết kế ", "totalProjectsOneStep": "Tổng số DA (1 bước) ", "totalProjectsPreparingTechnicalReport": " + Tổng số DA đang lập thiết kế (B<PERSON>o c<PERSON>o kinh tế kỹ thuật) ", "totalProjectsSubmittedForTechnicalAppraisal": " + Tổng số DA đã trình Cơ quan chuyên môn thẩm định BCKTKT ", "totalProjectsApprovedTechnicalReport": " + Tổng số DA được phê duyệt DA (Báo c<PERSON>o kinh tế kỹ thuật) ", "totalProjectsTwoSteps": "Tổng số DA (2 bước) ", "totalProjectsPreparingFeasibilityStudy": " + Tổng số DA đang lập <PERSON><PERSON><PERSON> c<PERSON><PERSON> cứ<PERSON> kh<PERSON> thi ", "totalProjectsSubmittedForFeasibilityAppraisal": " + Tổng số DA đã trình <PERSON>ơ quan chuyên môn thẩm định <PERSON> cáo NCKT ", "totalProjectsApprovedFeasibilityStudy": " + Tổng số DA được phê duyệt DA ", "totalProjectsPreparingDetailedDesign": " + Tổng số DA đang lập thiết kế XD triển khai sau thiết kế cơ sở ", "totalProjectsSubmittedForStep2Appraisal": " + Tổng số DA đã trình Cơ quan chuyên môn thẩm định B2 ", "totalProjectsApprovedDetailedDesign": " + Tổng số DA được duyệt thiết kế XD triển khai sau thiết kế cơ sở "}}