import { DeleteConfirmDialog } from '@/components/confirm-dialog';
import { DevexDataGrid } from '@/components/devex-data-grid';
import { PageLayout } from '@/components/page-layout';
import { removeAccents } from '@/lib/text';
import { PeriodFilter, PeriodFilterForm } from '@/components/period-filter-form';
import { MUTATE, PATHS, PERMISSIONS, QUERIES, TABLES } from '@/constant';
import { useDataTable, useEntity, usePermission } from '@/hooks';
import { createExportingEvent } from '@/lib/file';
import { callbackWithTimeout, displayExpr } from '@/lib/utils';
import { createDeleteMutateFn, createQueryPaginationFn } from '@/services';
import { translationWithNamespace } from '@/lib/i18nUtils';
import { DirectiveImplementation } from '@/types';
import { useQuery } from '@tanstack/react-query';
import { Button, Column, Editing, Export, Lookup } from 'devextreme-react/data-grid';
import { ColumnButtonClickEvent } from 'devextreme/ui/data_grid';
import { snakeCase } from 'lodash';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';

const t = translationWithNamespace('directiveImplementation');
const path = PATHS.DIRECTIVE_IMPLEMENTATION;
const exportFileName = snakeCase(removeAccents(t('model')));

const onExporting = createExportingEvent(`${exportFileName}.xlsx`, 'Main');

export const DirectiveImplementationDataTable = () => {
  const navigate = useNavigate();
  const { t } = useTranslation('directiveImplementation');

  const role = usePermission(PERMISSIONS.DIRECTIVE_IMPLEMENTATION);

  const { list: users } = useEntity({ queryKey: [QUERIES.USERS], model: 'user' });

  const getTargetAlias = (target: DirectiveImplementation | undefined) => {
    if (!target) {
      return '';
    }
    return target.name!;
  };

  const {
    selectedTarget,

    isConfirmDeleteDialogOpen,
    toggleConfirmDeleteDialog,
    selectTargetToDelete,
    deleteTarget,
    isDeleting,

    queryListParams,
    queryListMethods,
    // Query
  } = useDataTable<DirectiveImplementation, PeriodFilter>({
    queryRangeName: 'directiveImplementationTime',
    getTargetAlias,
    deleteFn: createDeleteMutateFn<DirectiveImplementation>('directive-implementation'),
    deleteKey: [MUTATE.DELETE_DIRECTIVE_IMPLEMENTATION],
    invalidateKey: [QUERIES.DIRECTIVE_IMPLEMENTATION],
  });

  const { data, refetch } = useQuery({
    queryKey: [QUERIES.DIRECTIVE_IMPLEMENTATION],
    queryFn: () => {
      return createQueryPaginationFn<DirectiveImplementation>('directive-implementation')({
        pageIndex: 1,
        pageSize: -1,
        sortColumn: 'DirectiveImplementationTime',
        sortOrder: 1,
        isPage: false,
        filterColumn: [],
        ...queryListParams,
      });
    },
  });

  const { items } = data || { items: [] };

  const onEditClick = (e: ColumnButtonClickEvent<DirectiveImplementation>) => {
    if (e.row?.data) {
      navigate(`${path}/` + e.row.data?.id, { state: path });
    }
  };

  const onAddClick = () => {
    navigate(`${path}/new`, { state: path });
  };

  const onDeleteClick = (e: ColumnButtonClickEvent<DirectiveImplementation>) => {
    if (e.row?.data) {
      selectTargetToDelete(e.row.data);
    }
  };

  const { isUpdate, isDelete } = role || {};

  return (
    <PageLayout header={t('page.header')}>
      <PeriodFilterForm
        defaultSearchValues={{
          range: [queryListParams.fromDate!, queryListParams.toDate!],
        }}
        onSearch={values => {
          const { range } = values;

          if (range) {
            const [from, to] = values.range;
            queryListMethods.addOrReplaceFilterDateColumn(
              'directiveImplementationTime',
              from!,
              to!
            );
          }

          callbackWithTimeout(refetch);
        }}
      />
      <DevexDataGrid
        id={TABLES.DIRECTIVE_IMPLEMENTATION}
        dataSource={items}
        onAddNewClick={onAddClick}
        onRefresh={() => {
          callbackWithTimeout(refetch);
        }}
        onExporting={onExporting}
      >
        <Export enabled={true} />
        <Editing allowUpdating={isUpdate} allowDeleting={isDelete} useIcons />
        <Column type="buttons">
          <Button name="edit" onClick={onEditClick} />
          <Button name="delete" onClick={onDeleteClick} />
        </Column>
        <Column
          dataField="directiveImplementationReportTime"
          caption={t('fields.directiveImplementationReportTime')}
          dataType="date"
          alignment="left"
        />
        <Column dataField="userCreatedId" caption={t('fields.userCreatedId')}>
          <Lookup dataSource={users} displayExpr={displayExpr(['name'])} valueExpr={'id'} />
        </Column>
        <Column dataField="name" caption={t('fields.name')} alignment="left" />
        <Column dataField="note" caption={t('fields.note')} alignment="left" />
      </DevexDataGrid>
      <DeleteConfirmDialog
        isDeleting={isDeleting}
        open={isConfirmDeleteDialogOpen}
        toggle={toggleConfirmDeleteDialog}
        onConfirm={() => {
          deleteTarget();
        }}
        name={getTargetAlias(selectedTarget)}
        model="directiveImplementation"
      />
    </PageLayout>
  );
};
