/* eslint-disable @typescript-eslint/no-unsafe-member-access */

import { DeleteConfirmDialog } from '@/components/confirm-dialog';
import { DevexDataGrid } from '@/components/devex-data-grid';
import { PageLayout } from '@/components/page-layout';
import { removeAccents } from '@/lib/text';
import { PeriodFilter, PeriodFilterForm } from '@/components/period-filter-form';
import { MUTATE, PATHS, PERMISSIONS, QUERIES, TABLES } from '@/constant';
import { useAuth, useDataTable, useEntity, usePermission } from '@/hooks';
import { createExportingEvent } from '@/lib/file';
import { callbackWithTimeout, displayExpr } from '@/lib/utils';
import { createDeleteMutateFn, createQueryPaginationFn } from '@/services';
import { translationWithNamespace } from '@/lib/i18nUtils';
import { WeeklyProjectSchedulePlan } from '@/types';
import { useQuery } from '@tanstack/react-query';
import { Button, Column, Editing, Export, Lookup } from 'devextreme-react/data-grid';
import { ColumnButtonClickEvent } from 'devextreme/ui/data_grid';
import { snakeCase } from 'lodash';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';

const t = translationWithNamespace('weeklyProjectSchedulePlan');
const path = PATHS.WEEKLY_PROJECT_SCHEDULE_PLAN;
const exportFileName = snakeCase(removeAccents(t('model')));

const onExporting = createExportingEvent(`${exportFileName}.xlsx`, 'Main');

export const WeeklyProjectSchedulePlanDataTable = () => {
  const navigate = useNavigate();
  const { t } = useTranslation('weeklyProjectSchedulePlan');
  const { user } = useAuth();

  const role = usePermission(PERMISSIONS.WEEKLY_PROJECT_SCHEDULE_PLAN);

  const { list: users } = useEntity({ queryKey: [QUERIES.USERS], model: 'user' });

  const getTargetAlias = (target: WeeklyProjectSchedulePlan | undefined) => {
    if (!target) {
      return '';
    }
    return target.note!;
  };

  const {
    selectedTarget,

    isConfirmDeleteDialogOpen,
    toggleConfirmDeleteDialog,
    selectTargetToDelete,
    deleteTarget,
    isDeleting,

    queryListParams,
    queryListMethods,
    // Query
  } = useDataTable<WeeklyProjectSchedulePlan, PeriodFilter>({
    queryRangeName: 'weeklyProjectSchedulePlanTime',
    getTargetAlias,
    deleteFn: createDeleteMutateFn<WeeklyProjectSchedulePlan>('weekly-project-schedule-plan'),
    deleteKey: [MUTATE.DELETE_WEEKLY_PROJECT_SCHEDULE_PLAN],
    invalidateKey: [QUERIES.WEEKLY_PROJECT_SCHEDULE_PLAN],
    initialQuery: {
      filterColumn: [
        {
          column: 'UserCreatedId',
          expression: '=',
          keySearch: user?.userId.toString() || '',
        },
      ],
    },
  });

  const { data, refetch } = useQuery({
    queryKey: [QUERIES.WEEKLY_PROJECT_SCHEDULE_PLAN],
    queryFn: () => {
      return createQueryPaginationFn<WeeklyProjectSchedulePlan>('weekly-project-schedule-plan')({
        pageIndex: 1,
        pageSize: -1,
        sortColumn: 'WeeklyProjectSchedulePlanTime',
        sortOrder: 1,
        isPage: false,
        filterColumn: [],
        ...queryListParams,
      });
    },
  });

  const { items } = data || { items: [] };

  const onEditClick = (e: ColumnButtonClickEvent<WeeklyProjectSchedulePlan>) => {
    if (e.row?.data) {
      navigate(`${path}/` + e.row.data?.id, { state: path });
    }
  };

  const onAddClick = () => {
    navigate(`${path}/new`, { state: path });
  };

  const onDeleteClick = (e: ColumnButtonClickEvent<WeeklyProjectSchedulePlan>) => {
    if (e.row?.data) {
      selectTargetToDelete(e.row.data);
    }
  };

  const { isUpdate, isDelete } = role || {};

  return (
    <PageLayout header={t('page.header')}>
      <PeriodFilterForm
        defaultSearchValues={{
          range: [queryListParams.fromDate!, queryListParams.toDate!],
        }}
        onSearch={values => {
          const { range } = values;

          if (range) {
            const [from, to] = values.range;
            queryListMethods.addOrReplaceFilterDateColumn(
              'weeklyProjectSchedulePlanTime',
              from!,
              to!
            );
          }

          callbackWithTimeout(refetch);
        }}
      />
      <DevexDataGrid
        id={TABLES.WEEKLY_PROJECT_SCHEDULE_PLAN}
        dataSource={items}
        onAddNewClick={onAddClick}
        onRefresh={() => {
          callbackWithTimeout(refetch);
        }}
        onExporting={onExporting}
      >
        <Export enabled={true} />
        <Editing allowUpdating={isUpdate} allowDeleting={isDelete} useIcons />
        <Column type="buttons">
          <Button name="edit" onClick={onEditClick} />
          <Button name="delete" onClick={onDeleteClick} />
        </Column>
        <Column
          dataType="date"
          dataField="weeklyProjectSchedulePlanTime"
          caption={t('fields.weeklyProjectSchedulePlanTime')}
        />
        <Column
          dataField="budgetYear"
          caption={t('fields.budgetYear')}
          dataType="date"
          format={'year'}
        />
        <Column dataField="totalNumberProject" caption={t('fields.totalNumberProject')} />

        <Column dataField="note" caption={t('fields.note')} />
        <Column dataField="userCreatedId" caption={t('fields.userCreatedId')}>
          <Lookup dataSource={users} displayExpr={displayExpr(['name'])} valueExpr={'id'} />
        </Column>
      </DevexDataGrid>
      <DeleteConfirmDialog
        isDeleting={isDeleting}
        open={isConfirmDeleteDialogOpen}
        toggle={toggleConfirmDeleteDialog}
        onConfirm={() => {
          deleteTarget();
        }}
        name={getTargetAlias(selectedTarget)}
        model="weeklyProjectSchedulePlan"
      />
    </PageLayout>
  );
};
