import { ErrorMessage } from '@/components/ui/error-message';
import { QUERIES, TABLES } from '@/constant';
import {
  defaultValuesTrainingTracking,
  IUserPermission,
  TrainingInstitution,
  TrainingTracking,
  TrainingTrackingDetail,
  User,
} from '@/types';
import { useFormContext, useWatch } from 'react-hook-form';

import { CellContext, ColumnDef } from '@tanstack/react-table';

import {
  DataTable,
  DataTableRowActions,
  EditableDatePickerCell,
  EditableDropdownCell,
  EditableInputCell,
  EditableTextArea,
} from '@/components/data-table';
import { translationWithNamespace } from '@/lib/i18nUtils';
import { getRandomNumber } from '@/lib/number';
import { useMemo } from 'react';
import { useEntity } from '@/hooks';
import { Button } from 'devextreme-react';
import { Workbook } from 'exceljs';

const [defaultRow] = defaultValuesTrainingTracking.trainingTrackingDetails;

type TrainingTrackingEditableTableProps = {
  role?: IUserPermission;
  calculateForm?: () => void;
  params: {
    year: string;
    outputFileName: string;
  };
};

const t = translationWithNamespace('trainingTracking');

export const TrainingTrackingEditableTable = ({
  role,
  calculateForm,
  params = {
    year: '',
    outputFileName: 'outputFile.xlsx',
  },
}: TrainingTrackingEditableTableProps) => {
  // const isMobile = useMediaQuery('(max-width: 768px)');

  const {
    setValue,
    control,
    formState: { errors },
  } = useFormContext<TrainingTracking>();

  const [editableData] = useWatch({
    control,
    name: ['trainingTrackingDetails'],
  });

  const { list: users } = useEntity<User>({ queryKey: [QUERIES.USERS], model: 'user' });
  const { list: trainingInstitutions } = useEntity<TrainingInstitution>({ queryKey: [QUERIES.TRAINING_INSTITUTION], model: 'training-institution' });

  const trainingTrackingEditableColumns: ColumnDef<TrainingTrackingDetail>[] = useMemo(
    () => [
      {
        id: 'classStartDate',
        accessorKey: 'classStartDate',
        header: t('fields.trainingTrackingDetails.classStartDate'),
        cell: props => (
          <EditableDatePickerCell
            {...props}
            onValueChanged={e => {
              const data = [...props.table.options.data];
              data[props.row.index] = {
                ...props.row.original,
                [props.column.id]: e.value,
              };
              if (data[data.length - 1].classStartDate !== null) {
                data.push({ ...defaultRow, id: -getRandomNumber() });
              }
            }}
          />
        ),
      },
      {
        id: 'trainingInstitutionId',
        accessorKey: 'trainingInstitutionId',
        header: t('fields.trainingTrackingDetails.trainingInstitutionId'),
        cell: (props: CellContext<TrainingTrackingDetail, unknown>) => (
          <EditableDropdownCell
            {...props}
            model="training-institution"
            showFields={['name']}
            queryKey={[QUERIES.TRAINING_INSTITUTION]}
            key={`training-institution-dropdown-${props.row.id}-${JSON.stringify(props.getValue())}`}
          />
        ),
      },
      {
        id: 'roomName',
        accessorKey: 'roomName',
        header: t('fields.trainingTrackingDetails.roomName'),
        cell: (props: CellContext<TrainingTrackingDetail, unknown>) => (
          <EditableTextArea {...props} className="max-h-[300px]" />
        ),
      },

      {
        id: 'memberIdArray',
        accessorKey: 'memberIdArray',
        // size: 300,
        header: t('fields.trainingTrackingDetails.memberIds'),
        cell: (props: CellContext<TrainingTrackingDetail, unknown>) => (
          <EditableDropdownCell
            {...props}
            multiple
            model="user"
            showFields={['name']}
            queryKey={[QUERIES.USERS]}
            onChange={value => {
              const {
                row: { original },
              } = props;

              const quantityMember = value?.length;

              const newValues = {
                ...original,
                memberIdArray: value,
                quantityMember: quantityMember,
              };

              props.table.options.meta?.updateRowValues(newValues, props.row.index);
            }}
            key={`member-dropdown-${props.row.id}-${JSON.stringify(props.getValue())}`}
          />
        ),
      },
      {
        id: 'quantityMember',
        accessorKey: 'quantityMember',
        size: 70,
        header: t('fields.trainingTrackingDetails.quantityMember'),
        cell: (props: CellContext<TrainingTrackingDetail, unknown>) => (
          <EditableInputCell {...props} className="max-h-[200px]" type="number" />
        ),
      },
      {
        id: 'note',
        accessorKey: 'note',
        header: t('fields.trainingTrackingDetails.note'),
        cell: (props: CellContext<TrainingTrackingDetail, unknown>) => (
          <EditableTextArea {...props} className="max-h-[300px]" />
        ),
      },
      {
        id: 'removeRow',
        header: '',
        size: 10,
        cell: props => {
          return (
            <DataTableRowActions
              onDelete={() => {
                props.table.options.meta?.removeRowByIndex(props.row.index);
              }}
              canDelete={role?.isCreate || role?.isUpdate}
            />
          );
        },
      },
    ],
    [role?.isCreate, role?.isUpdate, users]
  );

  const exportExcelHandler = () => {
    // Tạo workbook mới
    const workbook = new Workbook();
    const worksheet = workbook.addWorksheet('sheet1');

  

    // Tạo header
    const headers = [
      'STT',
      `${t('fields.trainingTrackingDetails.classStartDate')}`,
      `${t('fields.trainingTrackingDetails.trainingInstitutionId')}`,
      `${t('fields.trainingTrackingDetails.roomName')}`,
      `${t('fields.trainingTrackingDetails.memberIds')}`,
      `${t('fields.trainingTrackingDetails.quantityMember')}`,
      `${t('fields.trainingTrackingDetails.note')}`,
    ];

    // Cấu hình chiều rộng cột
    worksheet.columns = [
      { width: 8 }, // STT
      { width: 15 }, // Ngày mở lớp
      { width: 25 }, // Cơ sở đào tạo
      { width: 20 }, // Tên phòng học
      { width: 30 }, // Thành viên tham dự
      { width: 12 }, // Số lượng
      { width: 25 }, // Ghi chú
    ];

    // Thêm header vào worksheet
    const headerRow = worksheet.addRow(headers);
    headerRow.font = { bold: true, name: 'Times New Roman', size: 12 };
    headerRow.alignment = { horizontal: 'center', vertical: 'middle' };

    // Set border cho header
    headerRow.eachCell(cell => {
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
    });

    // Thêm dữ liệu
    editableData.forEach((item, index) => {
      // Lấy tên thành viên từ memberIdArray với format mỗi người một dòng
      const memberNames =
        item.memberIdArray
          ?.map(id => {
            const user = users.find(u => u.id === id);
            return user?.name ? `- ${user.name}` : '';
          })
          .filter(name => name !== '')
          .join('\n') || '';
      const trainingInstitutionName =
        trainingInstitutions.find(t => t.id === item.trainingInstitutionId)?.name || '';
      const calculateRowHeight = () => {
        const baseHeight = 18; // Chiều cao tối thiểu cho 1 dòng
        const columnWidth = 25; // Độ rộng ước tính của cột (ký tự)
        const charWidth = 0.8; // Độ rộng trung bình của 1 ký tự

        // Tính số dòng cần thiết cho memberNames
        const memberNamesArray = memberNames.split('\n').filter(name => name.trim() !== '');
        const memberLines = Math.max(
          1,
          memberNamesArray.reduce((total, name) => {
            return total + Math.ceil((name.length * charWidth) / columnWidth);
          }, 0)
        );

        // Tính số dòng cho các cột khác
        const noteLines = Math.ceil(((item.note || '').length * charWidth) / columnWidth);
        const roomNameLines = Math.ceil(((item.roomName || '').length * charWidth) / columnWidth);
        const institutionLines = Math.ceil(
          (trainingInstitutionName.length * charWidth) / columnWidth
        );

        // Lấy số dòng lớn nhất
        const maxLines = Math.max(memberLines, noteLines, roomNameLines, institutionLines, 1);
        return Math.max(baseHeight, maxLines * baseHeight);
      };
      
      const row = worksheet.addRow([
        index + 1, // STT
        item.classStartDate ? new Date(item.classStartDate).toLocaleDateString('vi-VN') : '', // Ngày bắt đầu lớp
        trainingInstitutionName, // Cơ sở đào tạo
        item.roomName || '', // Tên phòng học
        memberNames, // Thành viên
        item.quantityMember || 0, // Số lượng học viên
        item.note || '', // Ghi chú
      ]);
      row.height = calculateRowHeight();

      // Set style cho từng dòng dữ liệu
      row.font = { name: 'Times New Roman', size: 11 };
      row.alignment = { horizontal: 'left', vertical: 'middle', wrapText: true };

      // Set border cho từng cell
      row.eachCell(cell => {
        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' },
        };
      });
    });

    // Lưu file
    workbook.xlsx
      .writeBuffer()
      .then(buffer => {
        const blob = new Blob([buffer], { type: 'application/octet-stream' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `${params.outputFileName || 'theo-doi-dao-tao'}_${params.year || new Date().getFullYear()}.xlsx`;
        a.click();
        window.URL.revokeObjectURL(url);
      })
      .catch(error => console.log('error:', error));
  };

  return (
    <div>
      <DataTable
        tableId={TABLES.TRAINING_TRACKING}
        sortColumn="id"
        role={role}
        editableData={editableData}
        setEditableData={editedData => {
          setValue('trainingTrackingDetails', editedData);
          calculateForm?.();
        }}
        onAddButtonClick={table => {
          const newRow = { ...defaultRow, id: -getRandomNumber() };
          table.options.meta?.addNewRow(newRow);
        }}
        syncQueryParams={false}
        columns={trainingTrackingEditableColumns}
        customToolbar={() => {
          return (
            <>
              <Button
                stylingMode="text"
                icon="download"
                text="Xuất Excel"
                type="default"
                onClick={() => {
                  void exportExcelHandler();
                }}
              />
              {errors.trainingTrackingDetails && (
                <ErrorMessage
                  message={(() => {
                    const details = errors.trainingTrackingDetails;
                    if (Array.isArray(details)) {
                      const errorWithMessage = details.find(error => error?.roomName?.message);
                      return (errorWithMessage?.roomName?.message ??
                        details[0]?.roomName?.message ??
                        '') as string;
                    }
                    return details?.message || '';
                  })()}
                />
              )}
            </>
          );
        }}
      />
    </div>
  );
};
