import { requiredTextWithNamespace } from '@/lib/i18nUtils';
import { z } from 'zod';

import { defaultValuesRecordAttachment, recordAttachmentSchema } from './records-attachment';

export const requireProjectDeploymentStatusReportText = requiredTextWithNamespace(
  'projectDeploymentStatusReport'
);

export const projectDeploymentStatusReportSchema = z.object({
  id: z.number(),
  ordinalNumber: z.number().nullable(),
  budgetFundId: z.number().nullable(),
  budgetFundCode: z.string().nullable().optional(),
  budgetFundName: z.string().nullable().optional(),
  parentId: z.number().nullable(),
  numProjectsCompletedConstruction: z.number().nullable(),
  numProjectsUnderConstruction: z.number().nullable(),
  numProjectsProcurementInProgress: z.number().nullable(),
  numProjectsDesignInProgress: z.number().nullable(),
  totalProjectsOneStep: z.number().nullable(),
  totalProjectsPreparingTechnicalReport: z.number().nullable(),
  totalProjectsSubmittedForTechnicalAppraisal: z.number().nullable(),
  totalProjectsApprovedTechnicalReport: z.number().nullable(),
  totalProjectsTwoSteps: z.number().nullable(),
  totalProjectsPreparingFeasibilityStudy: z.number().nullable(),
  totalProjectsSubmittedForFeasibilityAppraisal: z.number().nullable(),
  totalProjectsApprovedFeasibilityStudy: z.number().nullable(),
  totalProjectsPreparingDetailedDesign: z.number().nullable(),
  totalProjectsSubmittedForStep2Appraisal: z.number().nullable(),
  totalProjectsApprovedDetailedDesign: z.number().nullable(),

  itemsRecordManagement: z.array(recordAttachmentSchema),
});

export type ProjectDeploymentStatusReport = z.infer<typeof projectDeploymentStatusReportSchema>;

export const defaultValuesProjectDeploymentStatusReport: ProjectDeploymentStatusReport = {
  id: 0,
  ordinalNumber: null, // Thống kê DA
  budgetFundId: null, // Id nguồn vốn
  budgetFundCode: '', // Mã nguồn vốn
  budgetFundName: '', // Tên nguồn vốn
  parentId: null, // Thuộc nguồn vốn
  numProjectsCompletedConstruction: null, // Số DA đã thi công hoàn thành
  numProjectsUnderConstruction: null, // Số DA đang triển khai thi công xây dựng
  numProjectsProcurementInProgress: null, // Số DA đang triển khai LCNT thi công xây dựng
  numProjectsDesignInProgress: null, // Số DA đang lập thiết kế
  totalProjectsOneStep: null, // Tổng số DA (1 bước)
  totalProjectsPreparingTechnicalReport: null, //  + Tổng số DA đang lập thiết kế (Báo cáo kinh tế kỹ thuật)
  totalProjectsSubmittedForTechnicalAppraisal: null, //  + Tổng số DA đã trình Cơ quan chuyên môn thẩm định BCKTKT
  totalProjectsApprovedTechnicalReport: null, //  + Tổng số DA được phê duyệt DA (Báo cáo kinh tế kỹ thuật)
  totalProjectsTwoSteps: null, // Tổng số DA (2 bước)
  totalProjectsPreparingFeasibilityStudy: null, //  + Tổng số DA đang lập Báo cáo Nghiên cứu khả thi
  totalProjectsSubmittedForFeasibilityAppraisal: null, //  + Tổng số DA đã trình Cơ quan chuyên môn thẩm định Báo cáo NCKT
  totalProjectsApprovedFeasibilityStudy: null, //  + Tổng số DA được phê duyệt DA
  totalProjectsPreparingDetailedDesign: null, //  + Tổng số DA đang lập thiết kế XD triển khai sau thiết kế cơ sở
  totalProjectsSubmittedForStep2Appraisal: null, //  + Tổng số DA đã trình Cơ quan chuyên môn thẩm định B2
  totalProjectsApprovedDetailedDesign: null, //  + Tổng số DA được duyệt thiết kế XD triển khai sau thiết kế cơ sở ,
  itemsRecordManagement: [defaultValuesRecordAttachment],
};

export type ProjectDeploymentStatusReportSummary = {
  sumNumProjectsCompletedConstruction: number; // Tổng Số DA đã thi công hoàn thành
  sumNumProjectsUnderConstruction: number; // Tổng Số DA đang triển khai thi công xây dựng
  sumNumProjectsProcurementInProgress: number; // Tổng Số DA đang triển khai LCNT thi công xây dựng
  sumNumProjectsDesignInProgress: number; // Tổng Số DA đang lập thiết kế
  sumTotalProjectsOneStep: number; // Tổng Tổng số DA (1 bước)
  sumTotalProjectsPreparingTechnicalReport: number; // Tổng  + Tổng số DA đang lập thiết kế (Báo cáo kinh tế kỹ thuật)
  sumTotalProjectsSubmittedForTechnicalAppraisal: number; // Tổng  + Tổng số DA đã trình Cơ quan chuyên môn thẩm định BCKTKT
  sumTotalProjectsApprovedTechnicalReport: number; // Tổng  + Tổng số DA được phê duyệt DA (Báo cáo kinh tế kỹ thuật)
  sumTotalProjectsTwoSteps: number; // Tổng Tổng số DA (2 bước)
  sumTotalProjectsPreparingFeasibilityStudy: number; // Tổng  + Tổng số DA đang lập Báo cáo Nghiên cứu khả thi
  sumTotalProjectsSubmittedForFeasibilityAppraisal: number; // Tổng  + Tổng số DA đã trình Cơ quan chuyên môn thẩm định Báo cáo NCKT
  sumTotalProjectsApprovedFeasibilityStudy: number; // Tổng  + Tổng số DA được phê duyệt DA
  sumTotalProjectsPreparingDetailedDesign: number; // Tổng  + Tổng số DA đang lập thiết kế XD triển khai sau thiết kế cơ sở
  sumTotalProjectsSubmittedForStep2Appraisal: number; // Tổng  + Tổng số DA đã trình Cơ quan chuyên môn thẩm định B2
  sumTotalProjectsApprovedDetailedDesign: number; // Tổng  + Tổng số DA được duyệt thiết kế XD triển khai sau thiết kế cơ sở
};
