import { requiredTextWithNamespace } from '@/lib/i18nUtils';
import { z } from 'zod';

// import { defaultValuesRecordAttachment, recordAttachmentSchema } from './records-attachment';

export const requireRptReportOnImplementationOfDirectivesByUserText = requiredTextWithNamespace(
  'rptReportOnImplementationOfDirectivesByUser'
);

export const rptReportOnImplementationOfDirectivesByUserSchema = z.object({
  id: z.number(),
  ordinalNumber: z.number().nullable(),
  executorName: z.string().nullable().optional(),
  departmentName: z.string().nullable().optional(),
  withinDeadlineQuantity: z.number().nullable(),
  lateQuantity: z.number().nullable(),
  aheadOfScheduleQuantity: z.number().nullable(),
  assignedQuantity: z.number().nullable(),
  completionRate: z.number().nullable(),
  aheadOfScheduleCompletionRate: z.number().nullable(),
  rank: z.string().nullable().optional(),
  onTimeQuantity: z.number().nullable(),
});

export type RptReportOnImplementationOfDirectivesByUser = z.infer<
  typeof rptReportOnImplementationOfDirectivesByUserSchema
>;

export const defaultValuesRptReportOnImplementationOfDirectivesByUser: RptReportOnImplementationOfDirectivesByUser =
  {
    id: 0,
    ordinalNumber: null, //
    executorName: '', //
    departmentName: '', //
    withinDeadlineQuantity: 0, // Trong hạn
    lateQuantity: 0, // Trễ hạn
    aheadOfScheduleQuantity: 0, // Vượt tiến độ
    assignedQuantity: 0, // Số lượng giao
    completionRate: 0, // Tỉ lệ hoàn thành
    aheadOfScheduleCompletionRate: 0, // Tỉ lệ vượt tiến độ
    rank: '', // Sếp hạn
    onTimeQuantity: 0, // Đúng hạn ,
  };
