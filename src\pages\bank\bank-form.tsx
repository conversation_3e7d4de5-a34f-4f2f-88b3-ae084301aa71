import { DialogFooter } from '@/components/ui/dialog';
import { Form, FormField, FormLabel } from '@/components/ui/form';
import { COMPONENT, MUTATE, QUERIES, closeLabel, enterLabel, saveLabel } from '@/constant';
import { useFormHandler } from '@/hooks';
import { useEntity } from '@/hooks/use-entity';
import { createMutationSuccessFn } from '@/lib/i18nUtils';
import { createPostMutateFn, createPutMutateFn, createQueryByIdFn } from '@/services';
import { defaultValuesBank, FormInsideModalProps } from '@/types';
import { Bank, bankSchema } from '@/types';
import { zodResolver } from '@hookform/resolvers/zod';
import { useQueryClient } from '@tanstack/react-query';
import { Button, CheckBox, TextBox } from 'devextreme-react';
import { useTranslation } from 'react-i18next';

const onBankMutationSuccess = createMutationSuccessFn('bank');

export const BankForm = ({ role, editId, toggle }: FormInsideModalProps<Bank>) => {
  const { t } = useTranslation('bank');
  const toggleForm = () => {
    if (toggle) toggle();
  };

  const { mutateQueryItem } = useEntity<Bank>({
    queryKey: [QUERIES.BANK],
    model: 'bank',
  });

  const queryClient = useQueryClient();

  const { handleSubmit, loading, methods } = useFormHandler<Bank>({
    queryKey: [MUTATE.BANK, editId],
    mutateKey: [MUTATE.BANK],
    invalidateKey: [QUERIES.BANK],
    queryId: editId,
    readFn: createQueryByIdFn<Bank>('bank'),
    createFn: createPostMutateFn<Bank>('bank'),
    updateFn: createPutMutateFn<Bank>('bank'),
    formatPayloadFn: values => {
      return values;
    },
    formatResponseFn: response => {
      mutateQueryItem(response);
      return response;
    },
    onCreateSuccess: (data, variables) => {
      mutateQueryItem({ ...variables, id: data });
      onBankMutationSuccess(data);
      void queryClient.invalidateQueries({ queryKey: [QUERIES.BANK, COMPONENT] });
      toggleForm();
    },
    onUpdateSuccess: data => {
      onBankMutationSuccess(data);
      void queryClient.invalidateQueries({ queryKey: [QUERIES.BANK, COMPONENT] });
      toggleForm();
    },
    formOptions: {
      resolver: zodResolver(bankSchema),
      defaultValues: defaultValuesBank,
    },
  });

  return (
    <Form {...methods}>
      <form className="p-1" autoComplete="off" onSubmit={handleSubmit}>
        <div className="space-y-4">
          {/* code */}
          <div className="flex items-center">
            <FormLabel name="code" className="hidden w-[85px] md:block">
              {t('fields.code')}
            </FormLabel>
            <FormField
              isRequired
              label={t('fields.code')}
              name="code"
              className="min-w-0 flex-1 md:max-w-[250px]"
            >
              <TextBox placeholder={`${enterLabel} ${t('fields.code')}`} />
            </FormField>
          </div>

          {/* name */}
          <div className="flex items-center">
            <FormLabel name="name" className="hidden w-[85px] md:block">
              {t('fields.name')}
            </FormLabel>
            <FormField
              isRequired
              label={t('fields.name')}
              name="name"
              className="min-w-0 flex-1 md:w-[500px]"
            >
              <TextBox placeholder={`${enterLabel} ${t('fields.name')}`} />
            </FormField>
          </div>

          {/* note */}
          <div className="flex items-center">
            <FormLabel name="note" className="hidden w-[85px] md:block">
              {t('fields.note')}
            </FormLabel>
            <FormField
              isRequired
              label={t('fields.note')}
              name="note"
              className="min-w-0 flex-1 md:w-[500px]"
            >
              <TextBox placeholder={`${enterLabel} ${t('fields.note')}`} />
            </FormField>
          </div>

          {/* is_active */}
          <div className="flex items-center">
            <FormField name="isActive" className="md:ml-[95px]" type="checkbox">
              <CheckBox />
            </FormField>
            <FormLabel className="">{t('fields.isActive')}</FormLabel>
          </div>
        </div>

        <DialogFooter className="mt-8 flex flex-row-reverse gap-x-2 bg-white py-1">
          <Button
            stylingMode="contained"
            text={saveLabel}
            icon="save"
            type="success"
            disabled={loading || (editId ? !role?.isUpdate : !role?.isCreate)}
            useSubmitBehavior
          />
          <Button
            stylingMode="outlined"
            text={closeLabel}
            icon="close"
            disabled={loading || (editId ? !role?.isUpdate : !role?.isCreate)}
            onClick={toggleForm}
            type="default"
          />
        </DialogFooter>
      </form>
    </Form>
  );
};
