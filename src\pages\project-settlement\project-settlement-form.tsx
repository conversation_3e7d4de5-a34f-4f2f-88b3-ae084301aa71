import { zod<PERSON>esolver } from '@hookform/resolvers/zod';
import { SyntheticEvent, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useParams } from 'react-router-dom';

import { PageLayout } from '@/components/page-layout';
import { RecordEditableTable } from '@/components/records-attachment';
import { Form, FormCombobox, FormField, FormLabel } from '@/components/ui/form';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  AGENCY_TYPE,
  enterLabel,
  MUTATE,
  PATHS,
  PERMISSIONS,
  PROFESSIONS,
  QUERIES,
  selectLabel,
} from '@/constant';
import { useAuth, useFormHandler, useFormOperation, usePermission } from '@/hooks';
import { useFormNavigate } from '@/hooks/use-form-navigate';
import { toDateType, toLocaleDate } from '@/lib/date';
import { createMutationSuccessFn } from '@/lib/i18nUtils';
import { displayExpr, getValidId } from '@/lib/utils';
import { createPostMutateFn, createPutMutateFn, createQueryByIdFn } from '@/services';
import {
  Agency,
  defaultValuesProjectSettlement,
  ProjectSettlement,
  projectSettlementSchema,
  ProjectStatus,
} from '@/types';
import { RowSelectionState } from '@tanstack/react-table';
import { DateBox, SelectBox, TextArea, TextBox } from 'devextreme-react';
import Button from 'devextreme-react/button';

const onProjectSettlementMutationSuccess = createMutationSuccessFn('projectSettlement');

export const ProjectSettlementForm = ({ projectStatus }: { projectStatus: ProjectStatus }) => {
  const { id: editId } = useParams();

  const { t } = useTranslation('projectSettlement');
  const role = usePermission(PERMISSIONS.PROJECT_SETTLEMENT);
  const { user, projects } = useAuth();
  const [rowSelection, setRowSelection] = useState<RowSelectionState>({});
  const path = PATHS.PROJECT_SETTLEMENT(projectStatus.code);
  const { goBackToList, goToUpdate, goToNew } = useFormNavigate(path);

  const defaultValues = useMemo(
    () => ({
      ...defaultValuesProjectSettlement,
      userCreatedId: user?.userId,
    }),
    [user?.userId]
  );

  const { handleSubmit, loading, methods } = useFormHandler<ProjectSettlement>({
    queryKey: [MUTATE.PROJECT_SETTLEMENT, editId],
    mutateKey: [MUTATE.PROJECT_SETTLEMENT],
    queryId: Number(editId) || 0,
    invalidateKey: [QUERIES.PROJECT_SETTLEMENT],
    readFn: createQueryByIdFn<ProjectSettlement>('project-settlement'),
    createFn: createPostMutateFn<ProjectSettlement>('project-settlement'),
    updateFn: createPutMutateFn<ProjectSettlement>('project-settlement'),
    formatPayloadFn: data => ({
      ...data,
      approvalDate: toLocaleDate(data.approvalDate!),
      projectSettlementTime: toLocaleDate(data.projectSettlementTime!),
      itemsRecordManagement: data.itemsRecordManagement
        .filter(item => item.content)
        .map(itemRecord => ({
          ...itemRecord,
          id: getValidId(itemRecord.id),
          dateCreate: toLocaleDate(itemRecord.dateCreate!),
          itemFile: itemRecord.itemFile
            .filter(file => file.fileName)
            .map(file => ({ ...file, id: getValidId(file.id) })),
        })),
    }),
    formatResponseFn: response => {
      const data = {
        ...response,
        approvalDate: toDateType(response.approvalDate!),
        projectSettlementTime: toDateType(response.projectSettlementTime!),
        itemsRecordManagement: response.itemsRecordManagement.map(i => ({
          ...i,
          dateCreate: toDateType(i.dateCreate!),
        })),
      };
      return data;
    },
    onCreateSuccess: data => {
      onProjectSettlementMutationSuccess(data);
      goToUpdate(data);
    },
    onUpdateSuccess: onProjectSettlementMutationSuccess,
    formOptions: {
      resolver: zodResolver(projectSettlementSchema),
      defaultValues,
    },
  });

  const [userCreatedName] = methods.watch(['userCreatedName']);

  const { reset, onTimeChange } = useFormOperation<ProjectSettlement>({
    model: 'project-settlement',
    fieldTime: 'projectSettlementTime',
    createCodeKey: [QUERIES.PROJECT_SETTLEMENT],
    formMethods: methods,
  });

  const onCreateNew = () => {
    goToNew();
    methods.reset(defaultValues);
    reset();
  };

  return (
    <>
      <Form {...methods}>
        <form autoComplete="off">
          <PageLayout
            onSaveChange={e => {
              handleSubmit(e.event?.currentTarget as unknown as SyntheticEvent<HTMLElement>);
            }}
            header={editId !== 'new' ? t('page.form.edit') : t('page.form.addNew')}
            canSaveChange={!isNaN(Number(editId)) ? role?.isUpdate : role?.isCreate}
            isSaving={loading}
            onCancel={goBackToList}
            customElementLeft={
              <>
                <Button
                  text={t('content.createNew', { ns: 'common' })}
                  className="uppercase"
                  stylingMode="outlined"
                  type="default"
                  icon="plus"
                  onClick={onCreateNew}
                />
              </>
            }
          >
            <div className="grid grid-cols-1 gap-x-8 gap-y-4 lg:grid-cols-4 xl:max-w-screen-2xl">
              {/* Cột 1 */}
              <div className="col-span-1 lg:col-span-2">
                <div className="grid grid-cols-1 gap-x-8 gap-y-4">
                  {/* Dự án */}
                  <div className="flex items-center">
                    <FormLabel
                      name="projectId"
                      htmlFor="projectId"
                      className="hidden w-[90px] md:block"
                    >
                      {t('fields.projectId')}
                    </FormLabel>
                    <FormField
                      label={t('fields.projectId')}
                      id="projectId"
                      name="projectId"
                      isRequired
                      className="min-w-0 flex-1"
                    >
                      <SelectBox
                        items={projects}
                        searchExpr={['name', 'code']}
                        placeholder={`${selectLabel} ${t('fields.projectId')}`}
                        valueExpr="id"
                        displayExpr={displayExpr(['name'])}
                        searchEnabled
                        searchMode="contains"
                        showClearButton
                        focusStateEnabled={false}
                      />
                    </FormField>
                  </div>
                  <div className="grid grid-cols-1 gap-x-8 gap-y-4 lg:grid-cols-2 xl:max-w-screen-2xl">
                    {/* Số quyết định */}
                    <div className="col-span-1 flex items-center">
                      <FormLabel
                        name="approvalNumber"
                        htmlFor="approvalNumber"
                        className="hidden w-[90px] md:block"
                      >
                        {t('fields.approvalNumber')}
                      </FormLabel>
                      <FormField
                        id="approvalNumber"
                        name="approvalNumber"
                        className="min-w-0 flex-1"
                        label={t('fields.approvalNumber')}
                      >
                        <TextBox placeholder={`${enterLabel} ${t('fields.approvalNumber')}`} />
                      </FormField>
                    </div>
                    {/* Ngày quyết định */}
                    <div className="col-span-1 flex items-center">
                      <FormLabel
                        name="approvalDate"
                        htmlFor="approvalDate"
                        className="hidden w-[90px] md:block"
                      >
                        {t('fields.approvalDate')}
                      </FormLabel>
                      <FormField
                        id="approvalDate"
                        name="approvalDate"
                        className="min-w-0 flex-1"
                        type="date"
                        label={t('fields.approvalDate')}
                      >
                        <DateBox
                          placeholder={`${selectLabel} ${t('fields.approvalDate')}`}
                          pickerType="calendar"
                          focusStateEnabled={false}
                        />
                      </FormField>
                    </div>
                  </div>
                  {/* Cơ quan ban hành */}
                  <div className="flex items-center">
                    <FormLabel htmlFor="agencyId" className="hidden w-[90px] md:block">
                      {t('fields.agencyId')}
                    </FormLabel>
                    <FormField
                      id="agencyId"
                      name={'agencyId'}
                      className="min-w-0 flex-1"
                      label={t('fields.agencyId')}
                    >
                      <FormCombobox<Agency>
                        placeholder={`${enterLabel} ${t('fields.agencyId')}`}
                        queryKey={[QUERIES.AGENCY]}
                        model="agency"
                        filter={item => item.agencyType === AGENCY_TYPE.ISSUE}
                      />
                    </FormField>
                  </div>
                  {/* Nội dung quyết định */}
                  <div className="flex items-center">
                    <FormLabel
                      name="approvalContent"
                      htmlFor="approvalContent"
                      className="hidden w-[90px] md:block"
                    >
                      {t('fields.approvalContent')}
                    </FormLabel>
                    <FormField
                      id="approvalContent"
                      name="approvalContent"
                      className="min-w-0 flex-1"
                      label={t('fields.approvalContent')}
                    >
                      <TextArea
                        autoResizeEnabled={true}
                        placeholder={`${enterLabel} ${t('fields.approvalContent')}`}
                      />
                    </FormField>
                  </div>

                  {/* Diên giải */}
                  <div className="flex items-center">
                    <FormLabel name="note" htmlFor="note" className="hidden w-[90px] md:block">
                      {t('fields.note')}
                    </FormLabel>
                    <FormField
                      id="note"
                      name="note"
                      className="min-w-0 flex-1"
                      label={t('fields.note')}
                    >
                      <TextBox placeholder={`${enterLabel} ${t('fields.note')}`} />
                    </FormField>
                  </div>
                </div>
              </div>

              {/* Cột 3 */}
              <div className="col-span-1">
                <div className="grid grid-cols-1 gap-x-8 gap-y-4">
                  {/* Ngày lập */}
                  <div className="flex items-center">
                    <FormLabel
                      name="projectSettlementTime"
                      htmlFor="projectSettlementTime"
                      className="hidden w-[90px] md:block"
                    >
                      {t('fields.projectSettlementTime')}
                    </FormLabel>
                    <FormField
                      id="projectSettlementTime"
                      name="projectSettlementTime"
                      className="min-w-0 flex-1"
                      type="date"
                      onChange={e => {
                        onTimeChange(e.target.value);
                      }}
                      label={t('fields.projectSettlementTime')}
                    >
                      <DateBox
                        placeholder={`${selectLabel} ${t('fields.projectSettlementTime')}`}
                        pickerType="calendar"
                        focusStateEnabled={false}
                      />
                    </FormField>
                  </div>

                  {/* Mã phiếu */}
                  <div className="flex items-center">
                    <FormLabel name="code" htmlFor="code" className="hidden w-[90px] md:block">
                      {t('fields.code')}
                    </FormLabel>
                    <FormField
                      id="code"
                      name="code"
                      className="min-w-0 flex-1"
                      label={t('fields.code')}
                    >
                      <TextBox placeholder={`${enterLabel} ${t('fields.code')}`} readOnly={true} />
                    </FormField>
                  </div>

                  {/* Người lập */}
                  <div className="flex items-center">
                    <FormLabel
                      name="userCreatedId"
                      htmlFor="userCreatedId"
                      className="hidden w-[90px] md:block"
                    >
                      {t('fields.userCreatedId')}
                    </FormLabel>
                    <FormField
                      id="userCreatedId"
                      name="userCreatedId"
                      className="min-w-0 flex-1"
                      label={t('fields.userCreatedId')}
                    >
                      <FormCombobox
                        defaultText={userCreatedName}
                        placeholder={`${selectLabel} ${t('fields.userCreatedId')}`}
                        model="user"
                        queryKey={[QUERIES.USERS]}
                        disabled
                      />
                    </FormField>
                  </div>
                </div>
              </div>
            </div>
            <div className="mt-8">
              <Tabs defaultValue="attachment">
                <div className="w-full">
                  <TabsList>
                    <TabsTrigger value="attachment">{t('page.tabs.attachment')}</TabsTrigger>
                  </TabsList>
                </div>
                <TabsContent value="attachment" className="mt-4">
                  <RecordEditableTable
                    role={role}
                    rowSelection={rowSelection}
                    setRowSelection={setRowSelection}
                    folder="project-settlement"
                    profession={PROFESSIONS.PROJECT_SETTLEMENT}
                  />
                </TabsContent>
              </Tabs>
            </div>
          </PageLayout>
        </form>
      </Form>
    </>
  );
};
