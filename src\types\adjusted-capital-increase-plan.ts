import { requiredTextWithNamespace } from '@/lib/i18nUtils';
import { z } from 'zod';
import { ArrayElement } from './common';
import { recordAttachmentSchema } from './records-attachment';

export const requireCapitalIncreasePlanText = requiredTextWithNamespace(
  'adjustedCapitalIncreasePlan'
);

export const adjustedCapitalIncreasePlanSchema = z.object({
  storeId: z.number().nullable(),
  branchId: z.number().nullable(),
  id: z.number(),
  code: z.string().nullable().optional(),
  adjustedCapitalIncreasePlanTime: z.date().nullable().optional(),
  userCreatedName: z.string().nullable().optional(),
  userCreatedId: z.number().nullable(),
  budgetFundName: z.string().nullable().optional(),
  budgetFundId: z
    .number({
      required_error: requireCapitalIncreasePlanText('budgetFundId', 'select'),
      invalid_type_error: requireCapitalIncreasePlanText('budgetFundId', 'select'),
    })
    .min(1, requireCapitalIncreasePlanText('budgetFundId', 'select')),
  approvalDate: z.date().nullable().optional(),
  approvalNumber: z.string().nullable().optional(),
  budgetYear: z.date().nullable().optional(),
  totalAmount: z.number().nullable(),
  note: z.string().nullable().optional(),
  capitalIncreasePlanId: z.number().nullable().optional(),
  capitalIncreasePlanCode: z.string().nullable().optional(),
  adjustedCapitalIncreasePlanDetails: z.array(
    z.object({
      id: z.number(),
      capitalIncreasePlanName: z.string().nullable().optional(),
      adjustedCapitalIncreasePlanId: z.number().nullable(),
      projectName: z.string().nullable().optional(),
      projectId: z.number().nullable(),
      budgetSourceCodeName: z.string().nullable().optional(),
      budgetSourceCodeId: z.number().nullable(),
      fundingProgramCodeId: z.number().nullable().optional(),
      sectorCodeId: z.number().nullable().optional(),
      typeCodeId: z.number().nullable().optional(),
      budgetItemCodeId: z.number().nullable().optional(),
      totalAmount: z.number().nullable(),
      adjustmentProposal: z.number().nullable().optional(),
      yearlyPlan: z.number().nullable().optional(),
      assignedPlanYearly: z.number().nullable().optional(),
      capitalIncreasePlanDetailId: z.number().nullable().optional(),
    })
  ),
  itemsRecordManagement: z.array(recordAttachmentSchema),
});

export type AdjustedCapitalIncreasePlan = z.infer<typeof adjustedCapitalIncreasePlanSchema>;
export type AdjustedCapitalIncreasePlanDetail = ArrayElement<
  AdjustedCapitalIncreasePlan['adjustedCapitalIncreasePlanDetails']
>;

export const defaultValuesAdjustedCapitalIncreasePlan: AdjustedCapitalIncreasePlan = {
  storeId: null, //
  branchId: null, //
  id: 0, // Khóa chính
  code: '', // Mã phiếu
  adjustedCapitalIncreasePlanTime: new Date(), // Ngày lập
  userCreatedId: null, // Người lập
  budgetFundId: 0, // Nguồn ngân sách
  approvalDate: new Date(), // Ngày quyết định
  approvalNumber: '', // Số quyết định
  budgetYear: new Date(), // Năm ngân sách
  totalAmount: 0, // Tổng tiền
  note: '', // Ghi chú,
  capitalIncreasePlanId: 0,
  adjustedCapitalIncreasePlanDetails: [
    {
      id: 0, // Khóa chính
      adjustedCapitalIncreasePlanId: 0, // Kế hoạch giao vốn
      projectId: null, // Dự án
      budgetSourceCodeId: null, // Mã nguồn NS
      fundingProgramCodeId: null, // Mã chương
      sectorCodeId: null, // Mã ngành KT
      typeCodeId: null, // Loại
      budgetItemCodeId: null, // Khoản
      totalAmount: 0, // Số tiền
    },
  ],
  itemsRecordManagement: [],
};
