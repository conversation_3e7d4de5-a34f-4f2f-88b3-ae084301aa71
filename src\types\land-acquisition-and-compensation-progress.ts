import { z } from 'zod';

export const LandAcquisitionAndCompensationProgressSchema = z.object({
  ordinalNumber: z.number().nullable().optional(),
  projectName: z.string().nullable().optional(),
  projectId: z.number().nullable().optional(),
  totalInvestment: z.number().nullable().optional(),
  totalFundingRequirement: z.number().nullable().optional(),
  totalFundingRequirementCompensation: z.number().nullable().optional(),
  totalFundingRequirementConstructionConsulting: z.number().nullable().optional(),
  cumulativeDisbursementYearToDate: z.number().nullable().optional(),
  compensationDisbursementYearToDate: z.number().nullable().optional(),
  constructionConsultingDisbursementYearToDate: z.number().nullable().optional(),
  disbursementRateYearToDate: z.number().nullable().optional(),
  constructionPeriod: z.number().nullable().optional(),
  ...Object.fromEntries(
    Array.from({ length: 52 }, (_, i) => [
      `landWeek${String(i + 1).padStart(2, '0')}`,
      z.number().nullable().optional(),
    ])
  ),
  issuesAndRecommendations: z.number().nullable().optional(),
  locationMap: z.number().nullable().optional(),
  planningInformation: z.number().nullable().optional(),
  departmentInChargeId: z.number().nullable().optional(),
  departmentInChargeName: z.string().nullable().optional(),
});

export type LandAcquisitionAndCompensationProgress = z.infer<
  typeof LandAcquisitionAndCompensationProgressSchema
>;
