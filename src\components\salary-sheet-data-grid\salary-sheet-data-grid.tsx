import { saveDataGridState } from '@/components/devex-data-grid';
import { DEV_EXTREME_STORAGE_KEY } from '@/constant';
import { Button } from 'devextreme-react';
import DataGrid, {
  Column,
  ColumnChooser,
  ColumnChooserSearch,
  ColumnChooserSelection,
  ColumnFixing,
  DataGridRef,
  Editing,
  FilterRow,
  Grouping,
  GroupPanel,
  HeaderFilter,
  Item,
  LoadPanel,
  Pager,
  Paging,
  Scrolling,
  Search,
  SearchPanel,
  StateStoring,
  Toolbar,
} from 'devextreme-react/data-grid';
import dxDataGrid, { dxDataGridColumn, dxDataGridRowObject } from 'devextreme/ui/data_grid';
import React, { useRef, useState } from 'react';
import { useReactToPrint } from 'react-to-print';
import { preprocessPrint } from './salary-sheet-data-grid-helper';

type ColumnBand = { name: string; columns?: ColumnBand[] };
export type PrintProps = {
  salarySheet: any;
  headerTitle: string;
  summaryCell: string;
};

type Props = {
  onAddNewClick?: () => void;
  onRefresh?: () => void;
  customToolbar?: React.ReactNode | React.ReactNode[];
  hideSerialNumber?: boolean;
  columnsBands?: ColumnBand[];
  printProps?: PrintProps;
};

export const SalarySheetDataGrid = React.forwardRef<
  DataGridRef,
  React.PropsWithChildren & React.ComponentProps<typeof DataGrid> & Props
>(
  (
    {
      children,
      onRefresh,
      onAddNewClick,
      customToolbar,
      hideSerialNumber: hideSerialNumber,
      ...props
    },
    ref
  ) => {
    const [expanded, setExpanded] = useState(true);
    const storageKey = `${DEV_EXTREME_STORAGE_KEY}_${props.id}`;
    const dataGridRef = useRef<DataGridRef | null>(null);
    const printRef = useRef(null);

    const instance = dataGridRef.current?.instance();

    React.useImperativeHandle(ref, () => ({
      instance: () => instance as dxDataGrid<any, any>,
    }));

    const loadState = (): any => {
      const config = localStorage.getItem(storageKey);
      if (!config) return {};
      return JSON.parse(config);
    };

    const customSave = (state: any) => {
      saveDataGridState(storageKey, state, instance as dxDataGrid<any, any>);
    };

    const table = preprocessPrint(
      instance,
      props.printProps ?? { salarySheet: null, headerTitle: '', summaryCell: '' }
    );

    // Khởi tạo mẫu in
    const [printoutElement, setPrintoutElement] = useState(
      <div ref={printRef} dangerouslySetInnerHTML={{ __html: table.html }} />
    );

    const printFn = useReactToPrint({
      content: () => printRef.current,
      pageStyle: `
          @media print {
            body { -webkit-print-color-adjust: exact; }
            .dx-datagrid-headers { background-color: #f5f5f5 !important; }
            .dx-datagrid-rowsview { font-size: 12px !important; }
          }
        `,
      removeAfterPrint: true,
      onBeforeGetContent: () => {
        const table = preprocessPrint(
          instance,
          props.printProps ?? { salarySheet: null, headerTitle: '', summaryCell: '' }
        );
        // cập nhật lại mẫu in
        setPrintoutElement(<div ref={printRef} dangerouslySetInnerHTML={{ __html: table.html }} />);
        return Promise.resolve();
      },
    });

    const printHandler = () => {
      // const visibleRows = instance?.getVisibleRows() || [];
      // console.log('visibleRows: ', visibleRows);
      if (printRef.current) {
        printFn();
      }
    };

    return (
      <>
        <DataGrid
          keyExpr={'id'}
          ref={dataGridRef}
          id="employeePayrollDataGrid"
          columnAutoWidth
          allowColumnResizing
          allowColumnReordering
          showBorders
          showColumnLines
          showRowLines
          hoverStateEnabled
          className="column-header-wrap max-h-[calc(100vh-9.8rem)]"
          columnFixing={{
            enabled: true,
            texts: {
              fix: 'Cố định',
              leftPosition: 'Cố định bên trái',
              rightPosition: 'Cố định bên phải',
              unfix: 'Bỏ cố định',
            },
          }}
          {...props}
        >
          <LoadPanel enabled={false} />
          <ColumnChooser enabled mode="select" height="45rem">
            <ColumnChooserSearch enabled />
            <ColumnChooserSelection allowSelectAll selectByClick recursive />
          </ColumnChooser>
          <FilterRow visible showOperationChooser />
          <Scrolling mode="standard" rowRenderingMode="standard" />
          <Paging enabled defaultPageSize={10} />
          <Pager
            visible
            showInfo
            showNavigationButtons
            showPageSizeSelector
            displayMode="adaptive"
            allowedPageSizes={[5, 10, 50, 100, 'all']}
          />
          <Grouping autoExpandAll={expanded} contextMenuEnabled={true} expandMode="rowClick" />
          <StateStoring
            enabled
            type="custom"
            storageKey={storageKey}
            // customSave={state => {
            //   console.log('state:', state);
            // }}
            customLoad={loadState}
            customSave={customSave}
          />
          <HeaderFilter visible>
            <Search enabled mode="contains" />
          </HeaderFilter>
          <SearchPanel visible />
          <GroupPanel visible />
          {/* <FilterPanel visible /> */}
          <Editing confirmDelete allowUpdating allowDeleting allowAdding useIcons />
          <Toolbar>
            <Item name="groupPanel" />
            <Item location="after">
              <Button
                // text={expanded ? 'Collapse All' : 'Expand All'}
                icon={expanded ? 'chevrondown' : 'chevronleft'}
                onClick={() => setExpanded(prevExpanded => !prevExpanded)}
              />
            </Item>
            {customToolbar}
            {onAddNewClick && (
              <Item>
                <Button icon="plus" onClick={onAddNewClick} />
              </Item>
            )}
            <Item location="after">
              <Button icon="refresh" onClick={onRefresh} />
            </Item>
            <Item name="columnChooserButton" />
            <Item name="exportButton" />
            <Item name="print">
              <Button icon="print" onClick={printHandler} />
            </Item>
            {/* <Item name="searchPanel" /> */}
          </Toolbar>
          {/* <Column dataField="branchName" /> */}
          <ColumnFixing enabled />
          {!hideSerialNumber && (
            <Column
              dataField="serialNumber"
              caption="STT"
              dataType="number"
              format={',##0,##'}
              alignment="center"
              width={60}
              allowFiltering={false}
              allowSorting={false}
              cellRender={(cellInfo: {
                column: dxDataGridColumn;
                columnIndex: number;
                component: dxDataGrid;
                data: Record<string, any>;
                displayValue: any;
                oldValue: any;
                row: dxDataGridRowObject;
                rowIndex: number;
                rowType: string;
                text: string;
                value: any;
                watch: () => void;
              }) => {
                if (cellInfo.rowType === 'data') {
                  const pageIndex = cellInfo.component.pageIndex();
                  const pageSize = cellInfo.component.pageSize();
                  const visibleRowIndex = cellInfo.component
                    .getVisibleRows()
                    .filter(item => item.rowType === 'data')
                    .indexOf(cellInfo.row);
                  return pageIndex * pageSize + visibleRowIndex + 1;
                }
              }}
            />
          )}
          {children}
        </DataGrid>
        <div id="print-region" className="hidden">
          {printoutElement}
        </div>
      </>
    );
  }
);
