import { PeriodFilter } from '@/components/period-filter-form';
import i18next from '@/i18n';
import { getActionLabel } from '@/lib/i18nUtils';
import { getLocalKeyWithPrefix, hash } from '@/lib/utils';
import { QueryPaginationParams } from '@/types';
import { startOfMonth } from 'date-fns';

export const TAB_KEY = 'tabCount'; // Key để lưu số lượng tab

export const COD_GENERATOR_PORT = 5000;

export const LOCAL_STORE_KEYS = {
  HOST: import.meta.env.PROD ? document.location.host : 'qlda-hocmon-dev.phanmemviet.net.vn',
  USER: getLocalKeyWithPrefix('user'),
  ROLES: getLocalKeyWithPrefix('roles'),
  DATA_TABLE_APPEARANCE: () => getLocalKeyWithPrefix('data_table_appearance', true),
  DATA_TABLE_APPEARANCE_MOBILE: () => getLocalKeyWithPrefix('data_table_appearance_mobile', true),
};

export const DEV_EXTREME_STORAGE_KEY = 'DEV_EXTREME_STORAGE';
export const TOKEN_KEY = 'MKS_PRODUCTION_MANAGEMENT';

const queryKeys = {
  TRAINING_TRACKING: 'TRAINING_TRACKING',
  USERS: 'USERS',
  PERMISSION_GROUP: 'PERMISSION_GROUP',
  CUSTOMERS: 'CUSTOMERS',
  STAFFS: 'STAFFS',
  PRODUCT_GROUPS: 'PRODUCT_GROUPS',
  PRODUCTS: 'PRODUCTS',

  BRANCHES: 'BRANCHES',
  WAREHOUSES: 'WAREHOUSES',
  SALES_ORDERS: 'SALES_ORDERS',
  SALES_ORDER_CODE: 'SALES_ORDER_CODE',
  PERMISSIONS: 'PERMISSIONS',
  PRINT_LABELS: 'PRINT_LABELS',
  PRINT_TYPES: 'PRINT_TYPES',
  PRINT_THEME: 'PRINT_THEME',
  PRINT_FORM: 'PRINT_FORM',

  RECEIPTS_AND_EXPENSES_CONTENT: 'RECEIPTS_AND_EXPENSES_CONTENT',
  ACCOUNT_FUND: 'ACCOUNT_FUND',
  PRINT_FORMS: 'PRINT_FORMS',

  PROJECT_GROUP: 'PROJECT_GROUP',
  PROJECT_OWNER: 'PROJECT_OWNER',
  AGENCY: 'AGENCY',
  DEPLOYMENT_PHASE: 'DEPLOYMENT_PHASE',
  CONTRACTOR_TYPE: 'CONTRACTOR_TYPE',
  CONTRACTOR: 'CONTRACTOR',
  TENDER_TYPE: 'TENDER_TYPE',
  BIDDING_SECTOR: 'BIDDING_SECTOR',
  BIDDING_METHOD: 'BIDDING_METHOD',
  CONTRACT_TYPE: 'CONTRACT_TYPE',
  COST_ITEM_TYPE: 'COST_ITEM_TYPE',
  DOCUMENT_GROUP: 'DOCUMENT_GROUP',
  FILE_TYPE: 'FILE_TYPE',
  DEPARTMENT: 'DEPARTMENT',
  POSITION: 'POSITION',
  PROFESSION_TYPES: 'PROFESSION_TYPES',
  CONFIG_IMPORT_BY_PROFESSION: 'CONFIG_IMPORT_BY_PROFESSION',
  WORK_POSITION: 'WORK_POSITION',
  EVALUATION_RESULT: 'EVALUATION_RESULT',
  EXPERTISE: 'EXPERTISE',
  MAJOR: 'MAJOR',
  POLITICS: 'POLITICS',
  CAREER_TRAINING: 'CAREER_TRAINING',
  IT_COURSE: 'IT_COURSE',
  FOREIGN_LANGUAGE: 'FOREIGN_LANGUAGE',
  CORRESPONDENCE_TYPE: 'CORRESPONDENCE_TYPE',
  GENDER_TYPE: 'GENDER_TYPE',
  INVENTORY_ITEM: 'INVENTORY_ITEM',
  ASSET: 'ASSET',
  TRAINING_INSTITUTION: 'TRAINING_INSTITUTION',
  INVENTORY_ITEM_TYPE: 'INVENTORY_ITEM_TYPE',
  ASSET_TYPE: 'ASSET_TYPE',
  DISTRICT: 'DISTRICT',
  PROJECT_STATUS: 'PROJECT_STATUS',
  WARD: 'WARD',
  PROJECT_MANAGEMENT_TYPE: 'PROJECT_MANAGEMENT_TYPE',
  BUDGET_FUND: 'BUDGET_FUND',
  BUDGET_FUND_CHILD: 'BUDGET_FUND_CHILD',
  BUDGET_SOURCE_CODE: 'BUDGET_SOURCE_CODE',
  COST_ITEM: 'COST_ITEM',
  PROJECT: 'PROJECT',
  PROJECT_CODE: 'PROJECT_CODE',
  CONSTRUCTION_ITEM: 'CONSTRUCTION_ITEM',
  CONSTRUCTION_TASK: 'CONSTRUCTION_TASK',
  EMPLOYEE_TYPE: 'EMPLOYEE_TYPE',
  EMPLOYEE: 'EMPLOYEE',
  CONTRACT_APPENDIX_TYPE: 'CONTRACT_APPENDIX_TYPE',
  UNIT: 'UNIT',
  STATE_MANAGEMENT: 'STATE_MANAGEMENT',
  CONTRACTOR_SELECTION_PLAN: 'CONTRACTOR_SELECTION_PLAN',
  CONTRACTOR_SELECTION_PLAN_CODE: 'CONTRACTOR_SELECTION_PLAN_CODE',
  STATUS: 'STATUS',
  CONTRACT_TASK_MANAGEMENT: 'CONTRACT_TASK_MANAGEMENT',
  CONTRACT_TASK_MANAGEMENT_CODE: 'CONTRACT_TASK_MANAGEMENT_CODE',

  DESIGN_TASK_MANAGEMENT: 'DESIGN_TASK_MANAGEMENT',
  DESIGN_TASK_MANAGEMENT_CODE: 'DESIGN_TASK_MANAGEMENT_CODE',

  TRAINING_MANAGEMENT: 'TRAINING_MANAGEMENT',
  TRAINING_MANAGEMENT_CODE: 'TRAINING_MANAGEMENT_CODE',
  TENDER_PACKAGE: 'TENDER_PACKAGE',
  TENDER_PACKAGE_CODE: 'TENDER_PACKAGE_CODE',
  TENDER_PACKAGE_EDIT_PRICE: 'TENDER_PACKAGE_EDIT_PRICE',
  CONTRACT: 'CONTRACT',
  CONTRACT_CODE: 'CONTRACT_CODE',
  CONTRACT_APPENDIX: 'CONTRACT_APPENDIX',
  CONTRACT_APPENDIX_CODE: 'CONTRACT_APPENDIX_CODE',
  COMPLETION_ACCEPTANCE: 'COMPLETION_ACCEPTANCE',
  COMPLETION_ACCEPTANCE_CODE: 'COMPLETION_ACCEPTANCE_CODE',
  BORROW_DOCUMENT: 'BORROW_DOCUMENT',
  LEAVE: 'LEAVE',

  BOARD_OF_DIRECTORS_WORK_SCHEDULE: 'BOARD_OF_DIRECTORS_WORK_SCHEDULE',
  EMPLOYEE_CODE: 'EMPLOYEE_CODE',

  OVERTIME_REGISTRATION: 'OVERTIME_REGISTRATION',
  APPROVAL_PROCESS: 'APPROVAL_PROCESS',
  APPROVAL_PROCESS_CODE: 'APPROVAL_PROCESS_CODE',
  SALARY_SHEET: 'SALARY_SHEET',
  TASK: 'TASK',
  TASK_CODE: 'TASK_CODE',
  DIRECTIVE_CONTENT: 'DIRECTIVE_CONTENT',
  DIRECTIVE_CONTENT_CODE: 'DIRECTIVE_CONTENT_CODE',

  TARGET: 'TARGET',
  TARGET_CODE: 'TARGET_CODE',
  REPORT_TEMPLATE: 'REPORT_TEMPLATE',
  REPORT_TEMPLATE_CODE: 'REPORT_TEMPLATE_CODE',
  REPORT_SERIAL_MANAGEMENT: 'REPORT_SERIAL_MANAGEMENT',
  REPORT_SERIAL_MANAGEMENT_CODE: 'REPORT_SERIAL_MANAGEMENT_CODE',
  WORK_MANAGEMENT_DIRECTIVE_CONTENT: 'WORK_MANAGEMENT_DIRECTIVE_CONTENT',
  WORK_MANAGEMENT_DIRECTIVE_CONTENT_CODE: 'WORK_MANAGEMENT_DIRECTIVE_CONTENT_CODE',
  WORK_MANAGEMENT_TARGET: 'WORK_MANAGEMENT_TARGET',
  WORK_MANAGEMENT_TARGET_CODE: 'WORK_MANAGEMENT_TARGET_CODE',
  WORK_MANAGEMENT_TASK: 'WORK_MANAGEMENT_TASK',
  WORK_MANAGEMENT_TASK_CODE: 'WORK_MANAGEMENT_TASK_CODE',
  INSURANCE_CONTRIBUTION_REPORT: 'INSURANCE_CONTRIBUTION_REPORT',

  WORK_MANAGEMENT_DESIGN_BID_ESTIMATION: 'WORK_MANAGEMENT_DESIGN_BID_ESTIMATION',
  WORK_MANAGEMENT_DESIGN_BID_ESTIMATION_CODE: 'WORK_MANAGEMENT_DESIGN_BID_ESTIMATION_CODE',
  PAYMENT_BENEFICIARY_REPORT: 'PAYMENT_BENEFICIARY_REPORT',

  DOCUMENT_FORM_ENTRY: 'DOCUMENT_FORM_ENTRY',
  EMPLOYEE_PAYROLL_REPORT: 'EMPLOYEE_PAYROLL_REPORT',
  SALARY_SHEET_FOR_LABOR_CONTRACT_REPORT: 'SALARY_SHEET_FOR_LABOR_CONTRACT_REPORT',
  SETUP_ANNUAL_HOLIDAY: 'SETUP_ANNUAL_HOLIDAY',

  WORK_MANAGEMENT_OTHER: 'WORK_MANAGEMENT_OTHER',
  WORK_MANAGEMENT_OTHER_CODE: 'WORK_MANAGEMENT_OTHER_CODE',

  GUARANTEE_LETTER_TRACKING: 'GUARANTEE_LETTER_TRACKING',
  OVERTIME_ATTENDANCE_TRACKING: 'OVERTIME_ATTENDANCE_TRACKING',
  OVERTIME_ATTENDANCE_TRACKING_DETAIL: 'OVERTIME_ATTENDANCE_TRACKING_DETAIL',
  DOCUMENT_DECISION: 'DOCUMENT_DECISION',

  CAPITAL_INCREASE_PLAN: 'CAPITAL_INCREASE_PLAN',
  CAPITAL_INCREASE_PLAN_CODE: 'CAPITAL_INCREASE_PLAN_CODE',
  ADJUSTED_CAPITAL_INCREASE_PLAN: 'ADJUSTED_CAPITAL_INCREASE_PLAN',
  ADJUSTED_CAPITAL_INCREASE_PLAN_CODE: 'ADJUSTED_CAPITAL_INCREASE_PLAN_CODE',
  OUTSTANDING_EQUIPMENT: 'OUTSTANDING_EQUIPMENT',

  ADJUSTED_INVESTMENT_CODE: 'ADJUSTED_INVESTMENT_CODE',
  ADJUSTED_INVESTMENT: 'ADJUSTED_INVESTMENT',
  BACKLOG_PROJECT_MANAGEMENT: 'BACKLOG_PROJECT_MANAGEMENT',
  BACKLOG_PROJECT_MANAGEMENT_CODE: 'BACKLOG_PROJECT_MANAGEMENT_CODE',
  ASSET_INCREMENT: 'ASSET_INCREMENT',
  ASSET_INCREMENT_CODE: 'ASSET_INCREMENT_CODE',
  FORM_DOCUMENT_MANAGER: 'FORM_DOCUMENT_MANAGER',

  ADJUSTED_COST_ESTIMATION: 'ADJUSTED_COST_ESTIMATION',
  PROJECT_SCHEDULE_SETUP: 'PROJECT_SCHEDULE_SETUP',
  PROJECT_SCHEDULE_SETUP_DETAIL: 'PROJECT_SCHEDULE_SETUP_DETAIL',

  HISTORY_ACTION: 'HISTORY_ACTION',
  SPENDING_COMMITMENT: 'SPENDING_COMMITMENT',
  SPENDING_COMMITMENT_CODE: 'SPENDING_COMMITMENT_CODE',
  COMMON_GET_BUDGET_SOURCE_CODE: 'COMMON_GET_BUDGET_SOURCE_CODE',

  INVESTMENT_FORM: 'INVESTMENT_FORM',
  INVESTMENT_TYPE: 'INVESTMENT_TYPE',
  CONSTRUCTION_TYPE: 'CONSTRUCTION_TYPE',
  PAYMENT_RECEIPT: 'PAYMENT_RECEIPT',
  NOTIFICATION: 'NOTIFICATION',
  NOTIFICATION_UNREAD_TOTAL: 'NOTIFICATION_UNREAD_TOTAL',
  NOTIFICATION_UNREAD: 'NOTIFICATION_UNREAD',
  PROFESSION_APPROVAL_PROCESS_DETAILS: 'PROFESSION_APPROVAL_PROCESS_DETAILS',
  RPT_ANNUAL_TASK_LIST_STATISTICS: 'RPT_ANNUAL_TASK_LIST_STATISTICS',
  RPT_YEARLY_SUMMARY: 'RPT_YEARLY_SUMMARY',
  RPT_SUMMARY_TARGETS_TASKS_FIRST_6_MONTHS: 'RPT_SUMMARY_TARGETS_TASKS_FIRST_6_MONTHS',
  RPT_SUMMARY_TARGETS_TASKS_LAST_6_MONTHS: 'RPT_SUMMARY_TARGETS_TASKS_LAST_6_MONTHS',
  CONTRACTOR_SELECTION_RESULT: 'CONTRACTOR_SELECTION_RESULT',
  CONTRACTOR_SELECTION_RESULT_DETAIL_REPORT: 'CONTRACTOR_SELECTION_RESULT_DETAIL_REPORT',
  RPT_REPORT_ON_IMPLEMENTATION_OF_DIRECTIVES: 'RPT_REPORT_ON_IMPLEMENTATION_OF_DIRECTIVES',
  A_B_SETTLEMENT: 'A_B_SETTLEMENT',
  REPORT_ANNEX_3A_PAYMENT_RECEIPT: 'REPORT_ANNEX_3A_PAYMENT_RECEIPT',
  CONTRACTOR_SELECTION_RESULT_BY_MANAGING_DIRECTOR_REPORT:
    'CONTRACTOR_SELECTION_RESULT_BY_MANAGING_DIRECTOR_REPORT',
  CONTRACTOR_SELECTION_RESULT_BY_DEPARTMENT_REPORT:
    'CONTRACTOR_SELECTION_RESULT_BY_DEPARTMENT_REPORT',
  ANNUAL_BIDDING_SUMMARY_REPORT: 'ANNUAL_BIDDING_SUMMARY_REPORT',
  REPORT_ANNEX_3A_CONTRACTOR: 'REPORT_ANNEX_3A_CONTRACTOR',
  REPORT_ANNEX_3A_FINANCE: 'REPORT_ANNEX_3A_FINANCE',
  REPORT_ANNEX_3A_PROJECT_MANAGEMENT: 'REPORT_ANNEX_3A_PROJECT_MANAGEMENT',
  A_B_ADJUSTMENT_SETTLEMENT: 'A_B_ADJUSTMENT_SETTLEMENT',
  PROJECT_DISBURSEMENT: 'PROJECT_DISBURSEMENT',
  PROJECT_DISBURSEMENT_REPORT: 'PROJECT_DISBURSEMENT_REPORT',
  PROPOSED_SETTLEMENT_INVESTMENT_COST: 'PROPOSED_SETTLEMENT_INVESTMENT_COST',
  ADVANCE_PAYMENT: 'ADVANCE_PAYMENT',
  ADVANCE_PAYMENT_CODE: 'ADVANCE_PAYMENT_CODE',
  PROJECT_DEBT_STATUS_STATISTICS_REPORT: 'PROJECT_DEBT_STATUS_STATISTICS_REPORT',
  CONTRACT_SETTLEMENT: 'CONTRACT_SETTLEMENT',
  GET_CONTRACT_SETTLEMENT_TOTAL_AMOUNT_REPORT: 'GET_CONTRACT_SETTLEMENT_TOTAL_AMOUNT_REPORT',
  GET_CONTRACT_SETTLEMENT_AMOUNT_PAID_BY_A_TO_B_REPORT:
    'GET_CONTRACT_SETTLEMENT_AMOUNT_PAID_BY_A_TO_B_REPORT',
  TEMPLATE_STATISTICS_REPORT: 'TEMPLATE_STATISTICS_REPORT',
  DATA_RECONCILIATION_TABLE: 'DATA_RECONCILIATION_TABLE',
  DATA_RECONCILIATION_TABLE_REPORT: 'DATA_RECONCILIATION_TABLE_REPORT',
  SAVING_RATE_CONTRACTOR_SELECTION_PLAN_REPORT: 'SAVING_RATE_CONTRACTOR_SELECTION_PLAN_REPORT',
  SAVING_RATE_CONTRACTOR_SELECTION_PLAN_REPORT_TOTAL:
    'SAVING_RATE_CONTRACTOR_SELECTION_PLAN_REPORT_TOTAL',
  SAVING_RATE_CONTRACTOR_SELECTION_PLAN_REPORT_DETAILS:
    'SAVING_RATE_CONTRACTOR_SELECTION_PLAN_REPORT_DETAILS',
  CAPITAL_PLAN_DISBURSEMENT_PROGRESS_REPORT: 'CAPITAL_PLAN_DISBURSEMENT_PROGRESS_REPORT',
  PROJECT_DEPARTMENT_DISBURSEMENT_PROGRESS_REPORT:
    'PROJECT_DEPARTMENT_DISBURSEMENT_PROGRESS_REPORT',
  PROJECT_DEPARTMENT_DISBURSEMENT_PROGRESS_DETAIL_REPORT:
    'PROJECT_DEPARTMENT_DISBURSEMENT_PROGRESS_DETAIL_REPORT',
  SAVING_IN_BIDDING_REPORT: 'SAVING_IN_BIDDING_REPORT',
  REPORT_PUBLIC_INVESTMENT_SETTLEMENT: 'REPORT_PUBLIC_INVESTMENT_SETTLEMENT',
  SECTOR_CODE: 'SECTOR_CODE',
  FUNDING_PROGRAM_CODE: 'FUNDING_PROGRAM_CODE',
  TYPE_CODE: 'TYPE_CODE',
  BUDGET_ITEM_CODE: 'BUDGET_ITEM_CODE',
  DOCUMENT_TYPE: 'DOCUMENT_TYPE',
  EMPLOYEE_ANNUAL_EVALUATION_RESULT_REPORT: 'EMPLOYEE_ANNUAL_EVALUATION_RESULT_REPORT',
  CONTRACTOR_PROJECT_STAFF_REPORT: 'CONTRACTOR_PROJECT_STAFF_REPORT',
  WEEKLY_PROJECT_SCHEDULE_PLAN: 'WEEKLY_PROJECT_SCHEDULE_PLAN',
  MONTHLY_PLANNED_DISBURSEMENT: 'MONTHLY_PLANNED_DISBURSEMENT',
  DIRECTIVE_IMPLEMENTATION: 'DIRECTIVE_IMPLEMENTATION',
  DIRECTIVE_IMPLEMENTATION_REPORT: 'DIRECTIVE_IMPLEMENTATION_REPORT',

  PROJECT_DEPLOYMENT_STATUS_REPORT: 'PROJECT_DEPLOYMENT_STATUS_REPORT',
  DISBURSEMENT_PROGRESS_BY_FUNDING_SOURCE: 'DISBURSEMENT_PROGRESS_BY_FUNDING_SOURCE',
  FINANCIAL_SETTLEMENT_REPORT: 'FINANCIAL_SETTLEMENT_REPORT',
  FINANCIAL_SETTLEMENT_REPORT_CODE: 'FINANCIAL_SETTLEMENT_REPORT_CODE',
  RPT_REPORT_ON_IMPLEMENTATION_OF_DIRECTIVES_BY_USER:
    'RPT_REPORT_ON_IMPLEMENTATION_OF_DIRECTIVES_BY_USER',
  DISBURSEMENT_PROGRESS_SUMMARY: 'DISBURSEMENT_PROGRESS_SUMMARY',
  SETUP_ANNUAL_HOLIDAY_REPORT: 'SETUP_ANNUAL_HOLIDAY_REPORT',
  REPORT_ANNEX_3A_DETAIL: 'REPORT_ANNEX_3A_DETAIL',
  LAND_ACQUISITION_AND_COMPENSATION_PROGRESS_REPORT:
    'LAND_ACQUISITION_AND_COMPENSATION_PROGRESS_REPORT',
  BOARD_OF_DIRECTOR: 'BOARD_OF_DIRECTOR',
  BANK: 'BANK',
  COMPLETION_ACCEPTANCE_NOTICE: 'COMPLETION_ACCEPTANCE_NOTICE',
  PROJECT_SETTLEMENT: 'PROJECT_SETTLEMENT',
};

const mutateKeys = {
  TRAINING_TRACKING: 'TRAINING_TRACKING',
  DELETE_TRAINING_TRACKING: 'DELETE_TRAINING_TRACKING',

  LOGIN: 'LOGIN',

  CHANGE_PASSWORD: 'CHANGE_PASSWORD',
  COMPANY: 'MUTATE_COMPANY',
  USER: 'USER',
  DELETE_USER: 'DELETE_USER',

  PERMISSION_GROUP: 'PERMISSION_GROUP',
  DELETE_PERMISSION_GROUP: 'DELETE_PERMISSION_GROUP',

  UPLOAD_FILE: 'UPLOAD_FILE',
  GET_PRESIGNED_URL: 'GET_PRESIGNED_URL',
  UPLOAD_FILE_S3: 'UPLOAD_FILE_S3',
  GET_DOWNLOAD_ONE_TIME_TOKEN: 'GET_DOWNLOAD_ONE_TIME_TOKEN',
  DOWNLOAD_FROM_S3: 'DOWNLOAD_FROM_S3',
  PRODUCT_GROUP: 'PRODUCT_GROUP',
  DELETE_PRODUCT_GROUP: 'DELETE_PRODUCT_GROUP',
  PRODUCT: 'PRODUCT',
  DELETE_PRODUCT: 'DELETE_PRODUCT',
  SALES_ORDER: 'SALES_ORDER',
  DELETE_SALES_ORDER: 'DELETE_SALES_ORDER',

  PRINT_FORM: 'PRINT_FORM',
  DELETE_PRINT_FORM: 'DELETE_PRINT_FORM',

  PROJECT_GROUP: 'PROJECT_GROUP',
  DELETE_PROJECT_GROUP: 'DELETE_PROJECT_GROUP',
  PROJECT_OWNER: 'PROJECT_OWNER',
  DELETE_PROJECT_OWNER: 'DELETE_PROJECT_OWNER',
  AGENCY: 'AGENCY',
  DELETE_AGENCY: 'DELETE_AGENCY',
  DEPLOYMENT_PHASE: 'DEPLOYMENT_PHASE',
  DELETE_DEPLOYMENT_PHASE: 'DELETE_DEPLOYMENT_PHASE',
  CONTRACTOR_TYPE: 'CONTRACTOR_TYPE',
  DELETE_CONTRACTOR_TYPE: 'DELETE_CONTRACTOR_TYPE',
  CONTRACTOR: 'CONTRACTOR',
  DELETE_CONTRACTOR: 'DELETE_CONTRACTOR',
  TENDER_TYPE: 'TENDER_TYPE',
  DELETE_TENDER_TYPE: 'DELETE_TENDER_TYPE',
  BIDDING_SECTOR: 'BIDDING_SECTOR',
  DELETE_BIDDING_SECTOR: 'DELETE_BIDDING_SECTOR',
  BIDDING_METHOD: 'BIDDING_METHOD',
  DELETE_BIDDING_METHOD: 'DELETE_BIDDING_METHOD',
  CONTRACT_TYPE: 'CONTRACT_TYPE',
  DELETE_CONTRACT_TYPE: 'DELETE_CONTRACT_TYPE',
  COST_ITEM_TYPE: 'COST_ITEM_TYPE',
  DELETE_COST_ITEM_TYPE: 'DELETE_COST_ITEM_TYPE',
  DOCUMENT_GROUP: 'DOCUMENT_GROUP',
  DELETE_DOCUMENT_GROUP: 'DELETE_DOCUMENT_GROUP',
  FILE_TYPE: 'FILE_TYPE',
  DELETE_FILE_TYPE: 'DELETE_FILE_TYPE',
  DEPARTMENT: 'DEPARTMENT',
  DELETE_DEPARTMENT: 'DELETE_DEPARTMENT',
  POSITION: 'POSITION',
  DELETE_POSITION: 'DELETE_POSITION',
  CONFIG_IMPORT_EXCEL: 'CONFIG_IMPORT_EXCEL',
  IMPORT_EXCEL: 'IMPORT_EXCEL',
  WORK_POSITION: 'WORK_POSITION',
  DELETE_WORK_POSITION: 'DELETE_WORK_POSITION',
  EVALUATION_RESULT: 'EVALUATION_RESULT',
  DELETE_EVALUATION_RESULT: 'DELETE_EVALUATION_RESULT',
  EXPERTISE: 'EXPERTISE',
  DELETE_EXPERTISE: 'DELETE_EXPERTISE',
  MAJOR: 'MAJOR',
  DELETE_MAJOR: 'DELETE_MAJOR',
  POLITICS: 'POLITICS',
  DELETE_POLITICS: 'DELETE_POLITICS',
  CAREER_TRAINING: 'CAREER_TRAINING',
  DELETE_CAREER_TRAINING: 'DELETE_CAREER_TRAINING',
  IT_COURSE: 'IT_COURSE',
  DELETE_IT_COURSE: 'DELETE_IT_COURSE',
  FOREIGN_LANGUAGE: 'FOREIGN_LANGUAGE',
  DELETE_FOREIGN_LANGUAGE: 'DELETE_FOREIGN_LANGUAGE',
  CORRESPONDENCE_TYPE: 'CORRESPONDENCE_TYPE',
  DELETE_CORRESPONDENCE_TYPE: 'DELETE_CORRESPONDENCE_TYPE',
  GENDER_TYPE: 'GENDER_TYPE',
  DELETE_GENDER_TYPE: 'DELETE_GENDER_TYPE',
  INVENTORY_ITEM: 'INVENTORY_ITEM',
  DELETE_INVENTORY_ITEM: 'DELETE_INVENTORY_ITEM',
  ASSET: 'ASSET',
  DELETE_ASSET: 'DELETE_ASSET',
  TRAINING_INSTITUTION: 'TRAINING_INSTITUTION',
  DELETE_TRAINING_INSTITUTION: 'DELETE_TRAINING_INSTITUTION',
  INVENTORY_ITEM_TYPE: 'INVENTORY_ITEM_TYPE',
  DELETE_INVENTORY_ITEM_TYPE: 'DELETE_INVENTORY_ITEM_TYPE',
  ASSET_TYPE: 'ASSET_TYPE',
  DELETE_ASSET_TYPE: 'DELETE_ASSET_TYPE',
  DISTRICT: 'DISTRICT',
  DELETE_DISTRICT: 'DELETE_DISTRICT',
  PROJECT_STATUS: 'PROJECT_STATUS',
  DELETE_PROJECT_STATUS: 'DELETE_PROJECT_STATUS',
  WARD: 'WARD',
  DELETE_WARD: 'DELETE_WARD',
  PROJECT_MANAGEMENT_TYPE: 'PROJECT_MANAGEMENT_TYPE',
  DELETE_PROJECT_MANAGEMENT_TYPE: 'DELETE_PROJECT_MANAGEMENT_TYPE',
  BUDGET_FUND: 'BUDGET_FUND',
  DELETE_BUDGET_FUND: 'DELETE_BUDGET_FUND',
  BUDGET_SOURCE_CODE: 'BUDGET_SOURCE_CODE',
  DELETE_BUDGET_SOURCE_CODE: 'DELETE_BUDGET_SOURCE_CODE',
  COST_ITEM: 'COST_ITEM',
  DELETE_COST_ITEM: 'DELETE_COST_ITEM',
  CONSTRUCTION_ITEM: 'CONSTRUCTION_ITEM',
  DELETE_CONSTRUCTION_ITEM: 'DELETE_CONSTRUCTION_ITEM',
  CONSTRUCTION_TASK: 'CONSTRUCTION_TASK',
  DELETE_CONSTRUCTION_TASK: 'DELETE_CONSTRUCTION_TASK',
  ACCOUNT_FUND: 'ACCOUNT_FUND',
  DELETE_ACCOUNT_FUND: 'DELETE_ACCOUNT_FUND',
  EMPLOYEE_TYPE: 'EMPLOYEE_TYPE',
  DELETE_EMPLOYEE_TYPE: 'DELETE_EMPLOYEE_TYPE',
  CONTRACT_APPENDIX_TYPE: 'CONTRACT_APPENDIX_TYPE',
  DELETE_CONTRACT_APPENDIX_TYPE: 'DELETE_CONTRACT_APPENDIX_TYPE',
  UNIT: 'UNIT',
  DELETE_UNIT: 'DELETE_UNIT',
  STATE_MANAGEMENT: 'STATE_MANAGEMENT',
  DELETE_STATE_MANAGEMENT: 'DELETE_STATE_MANAGEMENT',
  CONTRACTOR_SELECTION_PLAN: 'CONTRACTOR_SELECTION_PLAN',
  DELETE_CONTRACTOR_SELECTION_PLAN: 'DELETE_CONTRACTOR_SELECTION_PLAN',
  STATUS: 'STATUS',
  DELETE_STATUS: 'DELETE_STATUS',
  CONTRACT_TASK_MANAGEMENT: 'CONTRACT_TASK_MANAGEMENT',
  DELETE_CONTRACT_TASK_MANAGEMENT: 'DELETE_CONTRACT_TASK_MANAGEMENT',

  ASSET_INCREMENT: 'ASSET_INCREMENT',
  DELETE_ASSET_INCREMENT: 'DELETE_ASSET_INCREMENT',

  DESIGN_TASK_MANAGEMENT: 'DESIGN_TASK_MANAGEMENT',
  DELETE_DESIGN_TASK_MANAGEMENT: 'DELETE_DESIGN_TASK_MANAGEMENT',

  TRAINING_MANAGEMENT: 'TRAINING_MANAGEMENT',
  DELETE_TRAINING_MANAGEMENT: 'DELETE_TRAINING_MANAGEMENT',
  TENDER_PACKAGE: 'TENDER_PACKAGE',
  DELETE_TENDER_PACKAGE: 'DELETE_TENDER_PACKAGE',
  TENDER_PACKAGE_EDIT_PRICE: 'TENDER_PACKAGE_EDIT_PRICE',
  PROJECT: 'PROJECT',
  DELETE_PROJECT: 'DELETE_PROJECT',
  CONTRACT: 'CONTRACT',
  CONTRACT_DELETE: 'CONTRACT_DELETE',
  CONTRACT_APPENDIX: 'CONTRACT_APPENDIX',
  CONTRACT_APPENDIX_DELETE: 'CONTRACT_APPENDIX_DELETE',
  COMPLETION_ACCEPTANCE: 'COMPLETION_ACCEPTANCE',
  COMPLETION_ACCEPTANCE_DELETE: 'COMPLETION_ACCEPTANCE_DELETE',
  BORROW_DOCUMENT: 'BORROW_DOCUMENT',
  BORROW_DOCUMENT_DELETE: 'BORROW_DOCUMENT_DELETE',
  LEAVE: 'LEAVE',

  BOARD_OF_DIRECTORS_WORK_SCHEDULE: 'BOARD_OF_DIRECTORS_WORK_SCHEDULE',
  DELETE_BOARD_OF_DIRECTORS_WORK_SCHEDULE: 'DELETE_BOARD_OF_DIRECTORS_WORK_SCHEDULE',
  EMPLOYEE: 'EMPLOYEE',
  EMPLOYEE_DELETE: 'EMPLOYEE_DELETE',

  OVERTIME_REGISTRATION: 'OVERTIME_REGISTRATION',
  DELETE_OVERTIME_REGISTRATION: 'DELETE_OVERTIME_REGISTRATION',

  SALARY_SHEET: 'SALARY_SHEET',
  DELETE_SALARY_SHEET: 'DELETE_SALARY_SHEET',

  TASK: 'TASK',
  DELETE_TASK: 'DELETE_TASK',
  DIRECTIVE_CONTENT: 'DIRECTIVE_CONTENT',
  DELETE_DIRECTIVE_CONTENT: 'DELETE_DIRECTIVE_CONTENT',
  DIRECTIVE_CONTENT_SEND_FOR_PROCESSING: 'DIRECTIVE_CONTENT_SEND_FOR_PROCESSING',
  DIRECTIVE_CONTENT_SEND_REPROCESSED: 'DIRECTIVE_CONTENT_SEND_REPROCESSED',
  DIRECTIVE_CONTENT_UPDATE_IS_CLOSE: 'DIRECTIVE_CONTENT_UPDATE_IS_CLOSE',
  DIRECTIVE_CONTENT_CONVERT_PDF: 'DIRECTIVE_CONTENT_CONVERT_PDF',

  TARGET: 'TARGET',
  DELETE_TARGET: 'DELETE_TARGET',
  REPORT_TEMPLATE: 'REPORT_TEMPLATE',
  DELETE_REPORT_TEMPLATE: 'REPORT_TEMPLATE',
  REPORT_SERIAL_MANAGEMENT: 'REPORT_SERIAL_MANAGEMENT',
  DELETE_REPORT_SERIAL_MANAGEMENT: 'DELETE_REPORT_SERIAL_MANAGEMENT',
  WORK_MANAGEMENT_DIRECTIVE_CONTENT: 'WORK_MANAGEMENT_DIRECTIVE_CONTENT',
  DELETE_WORK_MANAGEMENT_DIRECTIVE_CONTENT: 'DELETE_MANAGEMENT_DIRECTIVE_CONTENT',
  WORK_MANAGEMENT_TARGET: 'WORK_MANAGEMENT_TARGET',
  DELETE_WORK_MANAGEMENT_TARGET: 'DELETE_WORK_MANAGEMENT_TARGET',
  WORK_MANAGEMENT_TASK: 'WORK_MANAGEMENT_TASK',
  DELETE_WORK_MANAGEMENT_TASK: 'DELETE_WORK_MANAGEMENT_TASK',
  INSURANCE_CONTRIBUTION_REPORT: 'INSURANCE_CONTRIBUTION_REPORT',

  WORK_MANAGEMENT_DESIGN_BID_ESTIMATION: 'WORK_MANAGEMENT_DESIGN_BID_ESTIMATION',
  DELETE_WORK_MANAGEMENT_DESIGN_BID_ESTIMATION: 'DELETE_WORK_MANAGEMENT_DESIGN_BID_ESTIMATION',
  PAYMENT_BENEFICIARY_REPORT: 'PAYMENT_BENEFICIARY_REPORT',

  DOCUMENT_FORM_ENTRY: 'DOCUMENT_FORM_ENTRY',
  DELETE_DOCUMENT_FORM_ENTRY: 'DELETE_DOCUMENT_FORM_ENTRY',
  EMPLOYEE_PAYROLL_REPORT: 'EMPLOYEE_PAYROLL_REPORT',
  SETUP_ANNUAL_HOLIDAY: 'SETUP_ANNUAL_HOLIDAY',
  DELETE_SETUP_ANNUAL_HOLIDAY: 'DELETE_SETUP_ANNUAL_HOLIDAY',

  APPROVAL_PROCESS: 'APPROVAL_PROCESS',
  DELETE_APPROVAL_PROCESS: 'DELETE_APPROVAL_PROCESS',
  WORK_MANAGEMENT_OTHER: 'WORK_MANAGEMENT_OTHER',
  DELETE_WORK_MANAGEMENT_OTHER: 'DELETE_WORK_MANAGEMENT_OTHER',

  DELETE_GUARANTEE_LETTER_TRACKING: 'DELETE_GUARANTEE_LETTER_TRACKING',
  GUARANTEE_LETTER_TRACKING: 'GUARANTEE_LETTER_TRACKING',

  FORM_DOCUMENT_MANAGER: 'FORM_DOCUMENT_MANAGER',
  DELETE_FORM_DOCUMENT_MANAGER: 'DELETE_FORM_DOCUMENT_MANAGER',

  DOCUMENT_DECISION: 'DOCUMENT_DECISION',
  DELETE_DOCUMENT_DECISION: ' DELETE_DOCUMENT_DECISION',
  CAPITAL_INCREASE_PLAN: 'CAPITAL_INCREASE_PLAN',
  DELETE_CAPITAL_INCREASE_PLAN: 'DELETE_CAPITAL_INCREASE_PLAN',

  ADJUSTED_CAPITAL_INCREASE_PLAN: 'ADJUSTED_CAPITAL_INCREASE_PLAN',
  DELETE_ADJUSTED_CAPITAL_INCREASE_PLAN: 'DELETE_ADJUSTED_CAPITAL_INCREASE_PLAN',

  DELETE_OUTSTANDING_EQUIPMENT: 'DELETE_OUTSTANDING_EQUIPMENT',
  OUTSTANDING_EQUIPMENT: 'OUTSTANDING_EQUIPMENT',

  DELETE_ADJUSTED_INVESTMENT: 'DELETE_ADJUSTED_INVESTMENT',
  ADJUSTED_INVESTMENT: 'ADJUSTED_INVESTMENT',
  BACKLOG_PROJECT_MANAGEMENT: 'BACKLOG_PROJECT_MANAGEMENT',
  DELETE_BACKLOG_PROJECT_MANAGEMENT: 'DELETE_BACKLOG_PROJECT_MANAGEMENT',

  ADJUSTED_COST_ESTIMATION: 'ADJUSTED_COST_ESTIMATION',
  DELETE_ADJUSTED_COST_ESTIMATION: 'DELETE_ADJUSTED_COST_ESTIMATION',

  DELETE_PROJECT_SCHEDULE_SETUP: 'DELETE_PROJECT_SCHEDULE_SETUP',
  PROJECT_SCHEDULE_SETUP: 'PROJECT_SCHEDULE_SETUP',

  SPENDING_COMMITMENT: 'SPENDING_COMMITMENT',
  DELETE_SPENDING_COMMITMENT: 'DELETE_SPENDING_COMMITMENT',

  COMPLETION_ACCEPTANCE_NOTICE: 'COMPLETION_ACCEPTANCE_NOTICE',
  DELETE_COMPLETION_ACCEPTANCE_NOTICE: 'DELETE_COMPLETION_ACCEPTANCE_NOTICE',

  INVESTMENT_FORM: 'INVESTMENT_FORM',
  DELETE_INVESTMENT_FORM: 'DELETE_INVESTMENT_FORM',

  DELETE_INVESTMENT_TYPE: 'DELETE_INVESTMENT_TYPE',
  INVESTMENT_TYPE: 'INVESTMENT_TYPE',

  DELETE_CONSTRUCTION_TYPE: 'DELETE_CONSTRUCTION_TYPE',
  CONSTRUCTION_TYPE: 'CONSTRUCTION_TYPE',

  PAYMENT_RECEIPT: 'PAYMENT_RECEIPT',
  DELETE_PAYMENT_RECEIPT: 'DELETE_PAYMENT_RECEIPT',

  CONTRACTOR_SELECTION_RESULT: 'CONTRACTOR_SELECTION_RESULT',
  DELETE_CONTRACTOR_SELECTION_RESULT: 'DELETE_CONTRACTOR_SELECTION_RESULT',

  NOTIFICATION: 'NOTIFICATION',
  DELETE_NOTIFICATION: 'DELETE_NOTIFICATION',

  DEVICE: 'DEVICE',

  MARK_AS_READ_NOTIFICATION: 'MARK_AS_READ_NOTIFICATION',
  MARK_AS_READ_NOTIFICATION_ALL: 'MARK_AS_READ_NOTIFICATION_ALL',
  SEND_NOTIFICATION: 'SEND_NOTIFICATION',
  PROFESSION_APPROVAL_PROCESS_FORWARD: 'PROFESSION_APPROVAL_PROCESS_FORWARD',
  PROFESSION_APPROVAL_PROCESS_APPROVAL: 'PROFESSION_APPROVAL_PROCESS_APPROVAL',
  DELETE_A_B_SETTLEMENT: 'DELETE_A_B_SETTLEMENT',
  A_B_SETTLEMENT: 'A_B_SETTLEMENT',
  DELETE_REPORT_ANNEX_3A_PROJECT_MANAGEMENT: 'DELETE_REPORT_ANNEX_3A_PROJECT_MANAGEMENT',
  REPORT_ANNEX_3A_PROJECT_MANAGEMENT: 'REPORT_ANNEX_3A_PROJECT_MANAGEMENT',

  DELETE_A_B_ADJUSTMENT_SETTLEMENT: 'DELETE_A_B_ADJUSTMENT_SETTLEMENT',
  A_B_ADJUSTMENT_SETTLEMENT: 'A_B_ADJUSTMENT_SETTLEMENT',
  PROJECT_DISBURSEMENT: 'PROJECT_DISBURSEMENT',
  DELETE_PROJECT_DISBURSEMENT: 'DELETE_PROJECT_DISBURSEMENT',
  DELETE_PROPOSED_SETTLEMENT_INVESTMENT_COST: 'DELETE_PROPOSED_SETTLEMENT_INVESTMENT_COST',
  PROPOSED_SETTLEMENT_INVESTMENT_COST: 'PROPOSED_SETTLEMENT_INVESTMENT_COST',
  DELETE_ADVANCE_PAYMENT: 'DELETE_ADVANCE_PAYMENT',
  ADVANCE_PAYMENT: 'ADVANCE_PAYMENT',
  ADVANCE_PAYMENT_REPORT_GET_REPORT: 'ADVANCE_PAYMENT_REPORT_GET_REPORT',

  DELETE_CONTRACT_SETTLEMENT: 'DELETE_CONTRACT_SETTLEMENT',
  CONTRACT_SETTLEMENT: 'CONTRACT_SETTLEMENT',
  DATA_RECONCILIATION_TABLE: 'DATA_RECONCILIATION_TABLE',
  DELETE_DATA_RECONCILIATION_TABLE: 'DELETE_DATA_RECONCILIATION_TABLE',
  SAVING_RATE_CONTRACTOR_SELECTION_PLAN_REPORT: 'SAVING_RATE_CONTRACTOR_SELECTION_PLAN_REPORT',
  CAPITAL_PLAN_DISBURSEMENT_PROGRESS_REPORT: 'CAPITAL_PLAN_DISBURSEMENT_PROGRESS_REPORT',
  PROJECT_DEPARTMENT_DISBURSEMENT_PROGRESS_REPORT:
    'PROJECT_DEPARTMENT_DISBURSEMENT_PROGRESS_REPORT',

  DELETE_REPORT_PUBLIC_INVESTMENT_SETTLEMENT: 'DELETE_REPORT_PUBLIC_INVESTMENT_SETTLEMENT',
  REPORT_PUBLIC_INVESTMENT_SETTLEMENT: 'REPORT_PUBLIC_INVESTMENT_SETTLEMENT',
  DELETE_SECTOR_CODE: 'DELETE_SECTOR_CODE',
  SECTOR_CODE: 'SECTOR_CODE',

  DELETE_FUNDING_PROGRAM_CODE: 'DELETE_FUNDING_PROGRAM_CODE',
  FUNDING_PROGRAM_CODE: 'FUNDING_PROGRAM_CODE',

  DELETE_TYPE_CODE: 'DELETE_TYPE_CODE',
  TYPE_CODE: 'TYPE_CODE',
  DELETE_BUDGET_ITEM_CODE: 'DELETE_BUDGET_ITEM_CODE',
  BUDGET_ITEM_CODE: 'BUDGET_ITEM_CODE',

  DOCUMENT_TYPE: 'DOCUMENT_TYPE',
  DELETE_DOCUMENT_TYPE: 'DELETE_DOCUMENT_TYPE',
  DELETE_WEEKLY_PROJECT_SCHEDULE_PLAN: 'DELETE_WEEKLY_PROJECT_SCHEDULE_PLAN',
  WEEKLY_PROJECT_SCHEDULE_PLAN: 'WEEKLY_PROJECT_SCHEDULE_PLAN',

  MONTHLY_PLANNED_DISBURSEMENT: 'MONTHLY_PLANNED_DISBURSEMENT',
  DELETE_MONTHLY_PLANNED_DISBURSEMENT: 'DELETE_MONTHLY_PLANNED_DISBURSEMENT',
  OVERTIME_ATTENDANCE_TRACKING: 'OVERTIME_ATTENDANCE_TRACKING',
  DELETE_OVERTIME_ATTENDANCE_TRACKING: 'DELETE_OVERTIME_ATTENDANCE_TRACKING',
  FINANCIAL_SETTLEMENT_REPORT_GET_REPORT: 'FINANCIAL_SETTLEMENT_REPORT_GET_REPORT',
  FINANCIAL_SETTLEMENT_REPORT: 'FINANCIAL_SETTLEMENT_REPORT',
  DELETE_FINANCIAL_SETTLEMENT_REPORT: 'DELETE_FINANCIAL_SETTLEMENT_REPORT',
  DELETE_DIRECTIVE_IMPLEMENTATION: 'DELETE_DIRECTIVE_IMPLEMENTATION',

  DELETE_PROJECT_DEPLOYMENT_STATUS_REPORT: 'DELETE_PROJECT_DEPLOYMENT_STATUS_REPORT',

  DELETE_DISBURSEMENT_PROGRESS_BY_FUNDING_SOURCE: 'DELETE_DISBURSEMENT_PROGRESS_BY_FUNDING_SOURCE',
  DISBURSEMENT_PROGRESS_BY_FUNDING_SOURCE: 'DISBURSEMENT_PROGRESS_BY_FUNDING_SOURCE',
  DISBURSEMENT_PROGRESS_BY_FUNDING_SOURCE_GET_REPORT:
    'DISBURSEMENT_PROGRESS_BY_FUNDING_SOURCE_GET_REPORT',
  DIRECTIVE_IMPLEMENTATION: 'DIRECTIVE_IMPLEMENTATION',
  DELETE_DISBURSEMENT_PROGRESS_SUMMARY: 'DELETE_DISBURSEMENT_PROGRESS_SUMMARY',
  DISBURSEMENT_PROGRESS_SUMMARY: 'DISBURSEMENT_PROGRESS_SUMMARY',

  DELETE_BOARD_OF_DIRECTOR: 'DELETE_BOARD_OF_DIRECTOR',
  BOARD_OF_DIRECTOR: 'BOARD_OF_DIRECTOR',
  DELETE_TEMPLATE_STATISTICS_REPORT: 'DELETE_TEMPLATE_STATISTICS_REPORT',
  TEMPLATE_STATISTICS_REPORT: 'TEMPLATE_STATISTICS_REPORT',
  DELETE_BANK: 'DELETE_BANK',
  BANK: 'BANK',
  DELETE_PROJECT_SETTLEMENT: 'DELETE_PROJECT_SETTLEMENT',
  PROJECT_SETTLEMENT: 'PROJECT_SETTLEMENT',
};

const groupTypes = {
  USER: 1,

  CUSTOMER: 2,
  SUPPLIER: 3,
  UNIT: 4,
  PRICE_TYPE: 5,
  ORDER_STATUS: 6,
  ORDER_PRINT_STATUS: 7,
  AREA: 9,
};

const paths = {
  SALES_ORDER: '/operations/sales/sales-order',
  CONTRACTOR_SELECTION_PLAN: (code: string) =>
    `/project-management/project/${code}/contractor-selection-plan`,
  TENDER_PACKAGE: (code: string) => `/project-management/project/${code}/tender-package`,
  CONTRACT: (code: string) => `/project-management/project/${code}/contract`,
  CONTRACT_APPENDIX: (code: string) => `/project-management/project/${code}/contract-appendix`,
  COMPLETION_ACCEPTANCE: (code: string) =>
    `/project-management/project/${code}/completion-acceptance`,
  COMPLETION_ACCEPTANCE_NOTICE: (code: string) =>
    `/project-management/project/${code}/completion-acceptance-notice`,
  SUN_PRINT_FORM: '/system/print-form',
  //quản lý công việc hợp đồng
  CONTRACT_TASK_MANAGEMENT: '/work-management/contract-task-management',
  //quản lý công việc thiết kế
  DESIGN_TASK_MANAGEMENT: '/work-management/design-task-management',

  PROJECT: '/project-management/project',

  BOARD_OF_DIRECTORS_WORK_SCHEDULE: '/work-management/board-of-directors-work-schedule',
  EMPLOYEE: '/hr/employee',

  //theo dõi nghỉ phép
  LEAVE: '/hr/leave',
  INSURANCE_CONTRIBUTION_REPORT: 'hr/insurance-contribution-report',
  //Theo dõi đào tạo
  TRAINING_MANAGEMENT: '/hr/training-management',

  TRAINING_TRACKING: '/hr/training-tracking',
  // Theo dõi cho mượn hồ sơ
  BORROW_DOCUMENT: '/hr/borrow-document',
  // đăng ký làm thêm giờ
  OVERTIME_REGISTRATION: '/hr/overtime-registration',
  SALARY_SHEET: '/general-finance/salary-sheet',

  DIRECTIVE_CONTENT: '/work-management/directives/content',
  TARGET: '/work-management/directives/target',
  TASK: '/work-management/directives/task',

  REPORT_TEMPLATE: '/planning/report-template',
  REPORT_SERIAL_MANAGEMENT: '/planning/report-serial-management',

  //QLCV - Nội dung chỉ đạo
  WORK_MANAGEMENT_DIRECTIVE_CONTENT: '/work-management/work/directive-content',
  WORK_MANAGEMENT_TARGET: '/work-management/work/target',
  WORK_MANAGEMENT_TASK: '/work-management/work/task',
  WORK_MANAGEMENT_DESIGN_BID_ESTIMATION: '/work-management/work/design-bid-estimation',
  WORK_MANAGEMENT_OTHER: '/work-management/work/other',
  PAYMENT_BENEFICIARY_REPORT: 'hr/payment-beneficiary-report',
  CAPITAL_INCREASE_PLAN: '/general-finance/capital-increase-plan',
  ADJUSTED_CAPITAL_INCREASE_PLAN: '/general-finance/adjusted-capital-increase-plan',

  DOCUMENT_FORM_ENTRY: '/planning/document-form-entry',
  EMPLOYEE_PAYROLL_REPORT: '/hr/employee-payroll',
  SETUP_ANNUAL_HOLIDAY: '/system/setup-annual-holiday',

  GUARANTEE_LETTER_TRACKING: '/general-finance/guarantee-letter-tracking',

  OUTSTANDING_EQUIPMENT: '/general-finance/outstanding-equipment',
  // PRINT_FORM: '/system/print-form',
  PROJECT_SCHEDULE_SETUP: '/project-management/project-schedule-setup',
  HISTORY_ACTION: './history-action',
  APPROVAL_PROCESS: '/system/approval-process',
  DOCUMENT_DECISION: '/base/contractor/document-decision',

  ADJUSTED_INVESTMENT: '/project-management/adjusted-investment',
  ASSET_INCREMENT: '/general-finance/asset-increment',
  FORM_DOCUMENT_MANAGER: '/system/form-document-manager',
  ADJUSTED_COST_ESTIMATION: '/project-management/adjusted-cost-estimation',
  BACKLOG_PROJECT_MANAGEMENT: '/planning/backlog-project-management',
  BACKLOG_PROJECT_MANAGEMENT_DIRECTOR: '/summary-report/backlog-project-management',

  // Cam kết chi
  SPENDING_COMMITMENT: '/general-finance/spending-commitment',
  // Chứng từ thanh toán
  PAYMENT_RECEIPT: '/general-finance/payment-receipt',

  ANNUAL_TASK_LIST_STATISTICS: '/work-management/directives-report/annual-task-list-statistics',
  YEARLY_SUMMARY: '/work-management/directives-report/yearly-summary',
  SUMMARY_TARGETS_TASKS_FIRST_6_MONTHS:
    '/work-management/directives-report/summary-targets-tasks-first-six-months',
  SUMMARY_TARGETS_TASKS_LAST_6_MONTHS:
    '/work-management/directives-report/summary-targets-tasks-last-six-months',

  CONTRACTOR_SELECTION_RESULT: '/project-management/contractor-selection-result',
  A_B_SETTLEMENT: '/general-finance/a-b-settlement',
  PROJECT_SETTLEMENT: (code: string) => `/project-management/project/${code}/project-settlement`,
  A_B_ADJUSTMENT_SETTLEMENT: '/general-finance/a-b-adjustment-settlement',
  // REPORT_ANNEX_3A: '/general-finance/report-annex-3a',
  REPORT_ANNEX_3A_PROJECT_MANAGEMENT: '/project-management/report-annex-3a-project-management',
  REPORT_ANNEX_3A_FINANCE: '/general-finance/report-annex-3a-finance',
  CONTRACTOR_SELECTION_RESULT_BY_MANAGING_DIRECTOR_REPORT:
    '/project-management/contractor-selection-result-by-managing-director-report',
  CONTRACTOR_SELECTION_RESULT_BY_DEPARTMENT_REPORT:
    '/planning/contractor-selection-result-by-department-report',
  PROJECT_DISBURSEMENT: '/project-management/project-disbursement',
  PROPOSED_SETTLEMENT_INVESTMENT_COST: '/general-finance/proposed-settlement-investment-cost',
  ADVANCE_PAYMENT: '/general-finance/reports/advance-payment',
  CONTRACT_SETTLEMENT: '/general-finance/contract-settlement',
  DATA_RECONCILIATION_TABLE: '/general-finance/data-reconciliation-table',
  SAVING_RATE_CONTRACTOR_SELECTION_PLAN_REPORT:
    '/planning/saving-rate-contractor-selection-plan-report',
  CAPITAL_PLAN_DISBURSEMENT_PROGRESS_REPORT: '/planning/capital-plan-disbursement-progress-report',
  PROJECT_DEPARTMENT_DISBURSEMENT_PROGRESS_REPORT:
    '/planning/project-department-disbursement-progress-report',
  REPORT_PUBLIC_INVESTMENT_SETTLEMENT:
    '/general-finance/reports/report-public-investment-settlement',
  WEEKLY_PROJECT_SCHEDULE_PLAN: '/project-management/project/weekly-project-schedule-plan',
  MONTHLY_PLANNED_DISBURSEMENT: '/project-management/project/monthly-planned-disbursement',
  DISBURSEMENT_PROGRESS_BY_FUNDING_SOURCE: '/planning/disbursement-progress-by-funding-source',
  DISBURSEMENT_PROGRESS_BY_FUNDING_SOURCE_DIRECTOR:
    '/summary-report/disbursement-progress-by-funding-source',
  DISBURSEMENT_PROGRESS_SUMMARY: '/planning/disbursement-progress-summary',
  DISBURSEMENT_PROGRESS_SUMMARY_DIRECTOR: '/summary-report/disbursement-progress-summary',
  OVERTIME_ATTENDANCE_TRACKING: '/hr/overtime-attendance-tracking',
  FINANCIAL_SETTLEMENT_REPORT: '/general-finance/reports/financial-settlement-report',
  DIRECTIVE_IMPLEMENTATION: '/work-management/directive-implementation-report',
  TEMPLATE_STATISTICS_REPORT: '/planning/template-statistics-report',
};

export type ProfessionPath = {
  [K in keyof typeof PATHS]: {
    ID: number;
    PATH: (typeof PATHS)[K];
  };
};
export const PROFESSIONS_PATH: ProfessionPath = {
  SALES_ORDER: {
    ID: -1, // QUYỀN NÀY KHÔNG CÓ
    PATH: paths.SALES_ORDER,
  },
  SUN_PRINT_FORM: {
    ID: -1, // QUYỀN NÀY KHÔNG CÓ
    PATH: paths.SUN_PRINT_FORM,
  },
  ANNUAL_TASK_LIST_STATISTICS: {
    ID: -1, //Không có quyền
    PATH: paths.ANNUAL_TASK_LIST_STATISTICS,
  },
  YEARLY_SUMMARY: {
    ID: -1, //Không có quyền
    PATH: paths.YEARLY_SUMMARY,
  },
  SUMMARY_TARGETS_TASKS_FIRST_6_MONTHS: {
    ID: -1, //Không có quyền
    PATH: paths.SUMMARY_TARGETS_TASKS_FIRST_6_MONTHS,
  },
  SUMMARY_TARGETS_TASKS_LAST_6_MONTHS: {
    ID: -1, //Không có quyền
    PATH: paths.SUMMARY_TARGETS_TASKS_LAST_6_MONTHS,
  },
  PROJECT: {
    ID: 14,
    PATH: paths.PROJECT,
  },
  APPROVAL_PROCESS: {
    ID: 44,
    PATH: paths.APPROVAL_PROCESS,
  },
  CONTRACTOR_SELECTION_PLAN: {
    ID: 45,
    PATH: paths.CONTRACTOR_SELECTION_PLAN,
  },
  CONTRACT_TASK_MANAGEMENT: {
    ID: 46,
    PATH: paths.CONTRACT_TASK_MANAGEMENT,
  },
  TENDER_PACKAGE: {
    ID: 47,
    PATH: paths.TENDER_PACKAGE,
  },
  CONTRACT: {
    ID: 48,
    PATH: paths.CONTRACT,
  },
  CONTRACT_APPENDIX: {
    ID: 49,
    PATH: paths.CONTRACT_APPENDIX,
  },
  ADJUSTED_INVESTMENT: {
    ID: 50,
    PATH: paths.ADJUSTED_INVESTMENT,
  },
  ADJUSTED_COST_ESTIMATION: {
    ID: 51,
    PATH: paths.ADJUSTED_COST_ESTIMATION,
  },
  COMPLETION_ACCEPTANCE: {
    ID: 52,
    PATH: paths.COMPLETION_ACCEPTANCE,
  },
  EMPLOYEE: {
    ID: 53,
    PATH: paths.EMPLOYEE,
  },
  TRAINING_MANAGEMENT: {
    ID: 54,
    PATH: paths.TRAINING_MANAGEMENT,
  },
  LEAVE: {
    ID: 55,
    PATH: paths.LEAVE,
  },
  BORROW_DOCUMENT: {
    ID: 56,
    PATH: paths.BORROW_DOCUMENT,
  },
  OVERTIME_REGISTRATION: {
    ID: 57,
    PATH: paths.OVERTIME_REGISTRATION,
  },
  BOARD_OF_DIRECTORS_WORK_SCHEDULE: {
    ID: 58,
    PATH: paths.BOARD_OF_DIRECTORS_WORK_SCHEDULE,
  },
  DIRECTIVE_CONTENT: {
    ID: 59,
    PATH: paths.DIRECTIVE_CONTENT,
  },
  TARGET: {
    ID: 60,
    PATH: paths.TARGET,
  },
  TASK: {
    ID: 61,
    PATH: paths.TASK,
  },
  WORK_MANAGEMENT_DIRECTIVE_CONTENT: {
    ID: 62,
    PATH: paths.WORK_MANAGEMENT_DIRECTIVE_CONTENT,
  },
  WORK_MANAGEMENT_TARGET: {
    ID: 63,
    PATH: paths.WORK_MANAGEMENT_TARGET,
  },
  WORK_MANAGEMENT_TASK: {
    ID: 64,
    PATH: paths.WORK_MANAGEMENT_TASK,
  },
  WORK_MANAGEMENT_DESIGN_BID_ESTIMATION: {
    ID: 65,
    PATH: paths.WORK_MANAGEMENT_DESIGN_BID_ESTIMATION,
  },
  WORK_MANAGEMENT_OTHER: {
    ID: 66,
    PATH: paths.WORK_MANAGEMENT_OTHER,
  },
  SALARY_SHEET: {
    ID: 67,
    PATH: paths.SALARY_SHEET,
  },
  INSURANCE_CONTRIBUTION_REPORT: {
    ID: 68,
    PATH: paths.INSURANCE_CONTRIBUTION_REPORT,
  },
  REPORT_TEMPLATE: {
    ID: 69,
    PATH: paths.REPORT_TEMPLATE,
  },
  DOCUMENT_FORM_ENTRY: {
    ID: 70,
    PATH: paths.DOCUMENT_FORM_ENTRY,
  },
  PAYMENT_BENEFICIARY_REPORT: {
    ID: 71,
    PATH: paths.PAYMENT_BENEFICIARY_REPORT,
  },
  REPORT_SERIAL_MANAGEMENT: {
    ID: 72,
    PATH: paths.REPORT_SERIAL_MANAGEMENT,
  },
  EMPLOYEE_PAYROLL_REPORT: {
    ID: 73,
    PATH: paths.EMPLOYEE_PAYROLL_REPORT,
  },
  OVERTIME_ATTENDANCE_TRACKING: {
    ID: 75,
    PATH: paths.OVERTIME_ATTENDANCE_TRACKING,
  },
  GUARANTEE_LETTER_TRACKING: {
    ID: 77,
    PATH: paths.GUARANTEE_LETTER_TRACKING,
  },
  SETUP_ANNUAL_HOLIDAY: {
    ID: 78,
    PATH: paths.SETUP_ANNUAL_HOLIDAY,
  },
  ASSET_INCREMENT: {
    ID: 81,
    PATH: paths.ASSET_INCREMENT,
  },
  OUTSTANDING_EQUIPMENT: {
    ID: 82,
    PATH: paths.OUTSTANDING_EQUIPMENT,
  },
  BACKLOG_PROJECT_MANAGEMENT: {
    ID: 83,
    PATH: paths.BACKLOG_PROJECT_MANAGEMENT,
  },
  CAPITAL_INCREASE_PLAN: {
    ID: 84,
    PATH: paths.CAPITAL_INCREASE_PLAN,
  },
  SPENDING_COMMITMENT: {
    ID: 85,
    PATH: paths.SPENDING_COMMITMENT,
  },
  PAYMENT_RECEIPT: {
    ID: 86,
    PATH: paths.PAYMENT_RECEIPT,
  },
  PROJECT_SCHEDULE_SETUP: {
    ID: 87,
    PATH: paths.PROJECT_SCHEDULE_SETUP,
  },
  DOCUMENT_DECISION: {
    ID: 88,
    PATH: paths.DOCUMENT_DECISION,
  },
  // PRINT_FORM: {
  //   ID: 89,
  //   PATH: paths.PRINT_FORM,
  // },
  FORM_DOCUMENT_MANAGER: {
    ID: 89,
    PATH: paths.FORM_DOCUMENT_MANAGER,
  },
  CONTRACTOR_SELECTION_RESULT: {
    ID: 95,
    PATH: paths.CONTRACTOR_SELECTION_RESULT,
  },
  A_B_SETTLEMENT: {
    ID: 100,
    PATH: paths.A_B_SETTLEMENT,
  },
  REPORT_ANNEX_3A_PROJECT_MANAGEMENT: {
    ID: 101,
    PATH: paths.REPORT_ANNEX_3A_PROJECT_MANAGEMENT,
  },
  DESIGN_TASK_MANAGEMENT: {
    ID: 102,
    PATH: paths.DESIGN_TASK_MANAGEMENT,
  },
  SAVING_RATE_CONTRACTOR_SELECTION_PLAN_REPORT: {
    ID: 105,
    PATH: paths.SAVING_RATE_CONTRACTOR_SELECTION_PLAN_REPORT,
  },
  CONTRACTOR_SELECTION_RESULT_BY_MANAGING_DIRECTOR_REPORT: {
    ID: 106,
    PATH: paths.CONTRACTOR_SELECTION_RESULT_BY_MANAGING_DIRECTOR_REPORT,
  },
  CAPITAL_PLAN_DISBURSEMENT_PROGRESS_REPORT: {
    ID: 107,
    PATH: paths.CAPITAL_PLAN_DISBURSEMENT_PROGRESS_REPORT,
  },
  CONTRACTOR_SELECTION_RESULT_BY_DEPARTMENT_REPORT: {
    ID: 108,
    PATH: paths.CONTRACTOR_SELECTION_RESULT_BY_DEPARTMENT_REPORT,
  },
  PROJECT_DISBURSEMENT: {
    ID: 109,
    PATH: paths.PROJECT_DISBURSEMENT,
  },
  A_B_ADJUSTMENT_SETTLEMENT: {
    ID: 110,
    PATH: paths.A_B_ADJUSTMENT_SETTLEMENT,
  },
  PROPOSED_SETTLEMENT_INVESTMENT_COST: {
    ID: 111,
    PATH: paths.PROPOSED_SETTLEMENT_INVESTMENT_COST,
  },
  CONTRACT_SETTLEMENT: {
    ID: 112,
    PATH: paths.CONTRACT_SETTLEMENT,
  },
  ADVANCE_PAYMENT: {
    ID: 113,
    PATH: paths.ADVANCE_PAYMENT,
  },
  PROJECT_DEPARTMENT_DISBURSEMENT_PROGRESS_REPORT: {
    ID: 114,
    PATH: paths.PROJECT_DEPARTMENT_DISBURSEMENT_PROGRESS_REPORT,
  },
  TEMPLATE_STATISTICS_REPORT: {
    ID: 115,
    PATH: paths.TEMPLATE_STATISTICS_REPORT,
  },
  DATA_RECONCILIATION_TABLE: {
    ID: 117,
    PATH: paths.DATA_RECONCILIATION_TABLE,
  },
  REPORT_PUBLIC_INVESTMENT_SETTLEMENT: {
    ID: 118,
    PATH: paths.REPORT_PUBLIC_INVESTMENT_SETTLEMENT,
  },
  REPORT_ANNEX_3A_FINANCE: {
    ID: 125,
    PATH: paths.REPORT_ANNEX_3A_FINANCE,
  },
  MONTHLY_PLANNED_DISBURSEMENT: {
    ID: 128,
    PATH: paths.MONTHLY_PLANNED_DISBURSEMENT,
  },
  WEEKLY_PROJECT_SCHEDULE_PLAN: {
    ID: 129,
    PATH: paths.WEEKLY_PROJECT_SCHEDULE_PLAN,
  },
  FINANCIAL_SETTLEMENT_REPORT: {
    ID: 130,
    PATH: paths.FINANCIAL_SETTLEMENT_REPORT,
  },
  DIRECTIVE_IMPLEMENTATION: {
    ID: 131,
    PATH: paths.DIRECTIVE_IMPLEMENTATION,
  },
  DISBURSEMENT_PROGRESS_BY_FUNDING_SOURCE: {
    ID: 133,
    PATH: paths.DISBURSEMENT_PROGRESS_BY_FUNDING_SOURCE,
  },
  DISBURSEMENT_PROGRESS_SUMMARY: {
    ID: 135,
    PATH: paths.DISBURSEMENT_PROGRESS_SUMMARY,
  },
  HISTORY_ACTION: {
    ID: 147,
    PATH: paths.HISTORY_ACTION,
  },
  DISBURSEMENT_PROGRESS_SUMMARY_DIRECTOR: {
    ID: 154,
    PATH: paths.DISBURSEMENT_PROGRESS_SUMMARY_DIRECTOR,
  },
  DISBURSEMENT_PROGRESS_BY_FUNDING_SOURCE_DIRECTOR: {
    ID: 156,
    PATH: paths.DISBURSEMENT_PROGRESS_BY_FUNDING_SOURCE_DIRECTOR,
  },
  BACKLOG_PROJECT_MANAGEMENT_DIRECTOR: {
    ID: 158,
    PATH: paths.BACKLOG_PROJECT_MANAGEMENT_DIRECTOR,
  },
  ADJUSTED_CAPITAL_INCREASE_PLAN: {
    ID: 164,
    PATH: paths.ADJUSTED_CAPITAL_INCREASE_PLAN,
  },
  COMPLETION_ACCEPTANCE_NOTICE: {
    ID: 165,
    PATH: paths.COMPLETION_ACCEPTANCE_NOTICE,
  },
  PROJECT_SETTLEMENT: {
    ID: 167,
    PATH: paths.PROJECT_SETTLEMENT,
  },
  TRAINING_TRACKING: {
    ID: 168,
    PATH: paths.TRAINING_TRACKING,
  },
} as const satisfies ProfessionPath;

export const tables = {
  TRAINING_TRACKING: 'TRAINING_TRACKING',
  USER: 'USER',
  PRODUCT_GROUP: 'PRODUCT_GROUP',
  PRODUCT: 'PRODUCT',
  SALES_ORDER: 'SALES_ORDER',
  SALES_ORDER_DETAIL: 'SALES_ORDER_DETAIL',
  SALES_ORDER_RECORDS: 'SALES_ORDER_RECORDS',
  SALES_ORDER_RECORD_FILES: 'SALES_ORDER_RECORD_FILES',
  SALES_ORDER_RECORD_FILES_READ_ONLY: 'SALES_ORDER_RECORD_FILES_READ_ONLY',
  PRINT_FORM: 'PRINT_FORM',
  PRINT_LABEL: 'PRINT_LABEL',
  PRINT_LABEL_EDITING: 'PRINT_LABEL_EDITING',
  PRINT_FORMS: 'PRINT_FORMS',

  RECORDS_EDITABLE_DATA: 'RECORDS_EDITABLE_DATA',
  RECORDS_EDITABLE_DATA_READONLY: 'RECORDS_EDITABLE_DATA_READONLY',

  PROJECT_GROUP: 'PROJECT_GROUP',
  PROJECT_OWNER: 'PROJECT_OWNER',
  AGENCY: 'AGENCY',
  DEPLOYMENT_PHASE: 'DEPLOYMENT_PHASE',
  CONTRACTOR_TYPE: 'CONTRACTOR_TYPE',
  CONTRACTOR: 'CONTRACTOR',
  TENDER_TYPE: 'TENDER_TYPE',
  BIDDING_SECTOR: 'BIDDING_SECTOR',
  BIDDING_METHOD: 'BIDDING_METHOD',
  CONTRACT_TYPE: 'CONTRACT_TYPE',
  COST_ITEM_TYPE: 'COST_ITEM_TYPE',
  DOCUMENT_GROUP: 'DOCUMENT_GROUP',
  FILE_TYPE: 'FILE_TYPE',
  DEPARTMENT: 'DEPARTMENT',
  POSITION: 'POSITION',
  IMPORT_CONFIGURATION_PRESENTATION: 'IMPORT_CONFIGURATION_PRESENTATION',
  IMPORT_CONFIGURATION_SETTING: 'IMPORT_CONFIGURATION_SETTING',
  WORK_POSITION: 'WORK_POSITION',
  EVALUATION_RESULT: 'EVALUATION_RESULT',
  EXPERTISE: 'EXPERTISE',
  MAJOR: 'MAJOR',
  POLITICS: 'POLITICS',
  CAREER_TRAINING: 'CAREER_TRAINING',
  IT_COURSE: 'IT_COURSE',
  FOREIGN_LANGUAGE: 'FOREIGN_LANGUAGE',
  STATISTICS_BY_REPORT_TEMPLATE: 'STATISTICS_BY_REPORT_TEMPLATE',
  CORRESPONDENCE_TYPE: 'CORRESPONDENCE_TYPE',
  GENDER_TYPE: 'GENDER_TYPE',
  INVENTORY_ITEM: 'INVENTORY_ITEM',
  ASSET: 'ASSET',
  TRAINING_INSTITUTION: 'TRAINING_INSTITUTION',
  INVENTORY_ITEM_TYPE: 'INVENTORY_ITEM_TYPE',
  ASSET_TYPE: 'ASSET_TYPE',
  DISTRICT: 'DISTRICT',
  PROJECT_STATUS: 'PROJECT_STATUS',
  WARD: 'WARD',
  CONTRACT_TASK_MANAGEMENT: 'CONTRACT_TASK_MANAGEMENT',
  CONTRACT_TASK_MANAGEMENT_DOCUMENTS: 'CONTRACT_TASK_MANAGEMENT_DOCUMENTS',

  DESIGN_TASK_MANAGEMENT: 'DESIGN_TASK_MANAGEMENT',
  DESIGN_TASK_MANAGEMENT_DOCUMENTS: 'DESIGN_TASK_MANAGEMENT_DOCUMENTS',

  TRAINING_MANAGEMENT: 'TRAINING_MANAGEMENT',
  PROJECT_MANAGEMENT_TYPE: 'PROJECT_MANAGEMENT_TYPE',
  PROJECT: 'PROJECT',
  PROJECT_DIRECTOR_CHANGE_HISTORY: 'PROJECT_DIRECTOR_CHANGE_HISTORY',
  PROJECT_IMPLEMENT_TIME_CHANGE_HISTORY: 'PROJECT_IMPLEMENT_TIME_CHANGE_HISTORY',
  PROJECT_PMO_MEMBER: 'PROJECT_PMO_MEMBER',
  PROJECT_ACCOUNTING_MEMBER: 'PROJECT_ACCOUNTING_MEMBER',
  PROJECT_QUANTITY_PLANING_MEMBER: 'PROJECT_QUANTITY_PLANING_MEMBER',
  PROJECT_BOARD_OF_DIRECTOR: 'PROJECT_BOARD_OF_DIRECTOR',
  PREPARATION_DETAIL: 'PREPARATION_DETAIL',
  PREPARATION_DETAIL_SUMMARY: 'PREPARATION_DETAIL_SUMMARY',
  TOTAL_INVESTMENT_DETAIL: 'TOTAL_INVESTMENT_DETAIL',
  TOTAL_INVESTMENT_DETAIL_SUMMARY: 'TOTAL_INVESTMENT_DETAIL_SUMMARY',
  ADJUSTED_INVESTMENT_DETAIL: 'ADJUSTED_INVESTMENT_DETAIL',
  ADJUSTED_INVESTMENT_DETAIL_SUMMARY: 'ADJUSTED_INVESTMENT_DETAIL_SUMMARY',
  COST_ESTIMATION_DETAIL: 'COST_ESTIMATION_DETAIL',
  COST_ESTIMATION_DETAIL_SUMMARY: 'COST_ESTIMATION_DETAIL_SUMMARY',
  ADJUSTED_COST_ESTIMATION_DETAIL: 'ADJUSTED_COST_ESTIMATION_DETAIL',
  PROJECT_DISBURSEMENT_INITIALS: 'PROJECT_DISBURSEMENT_INITIALS',
  PROJECT_APPROVALS: 'PROJECT_APPROVALS',
  ADJUSTED_COST_ESTIMATION_DETAIL_SUMMARY: 'ADJUSTED_COST_ESTIMATION_DETAIL_SUMMARY',
  PROJECT_DOCUMENTS: 'PROJECT_DOCUMENTS',
  SETUP_REPORT_TEMPLATE_COLUMN_CONFIGURE: 'SETUP_REPORT_TEMPLATE_COLUMN_CONFIGURE',
  SETUP_REPORT_TEMPLATE_COLUMN_VISIBLE: 'SETUP_REPORT_TEMPLATE_COLUMN_VISIBLE',
  BUDGET_FUND: 'BUDGET_FUND',
  BUDGET_SOURCE_CODE: 'BUDGET_SOURCE_CODE',
  COST_ITEM: 'COST_ITEM',
  CONSTRUCTION_ITEM: 'CONSTRUCTION_ITEM',
  CONSTRUCTION_TASK: 'CONSTRUCTION_TASK',
  ACCOUNT_FUND: 'ACCOUNT_FUND',
  EMPLOYEE_TYPE: 'EMPLOYEE_TYPE',
  CONTRACT_APPENDIX_TYPE: 'CONTRACT_APPENDIX_TYPE',
  UNIT: 'UNIT',
  STATE_MANAGEMENT: 'STATE_MANAGEMENT',
  CONTRACTOR_SELECTION_PLAN: 'CONTRACTOR_SELECTION_PLAN',
  STATUS: 'STATUS',
  CONTRACTOR_SELECTION_PLAN_DETAIL: 'CONTRACTOR_SELECTION_PLAN_DETAIL',
  CONTRACTOR_SELECTION_PLAN_APPROVAL: 'CONTRACTOR_SELECTION_PLAN_APPROVAL',
  TENDER_PACKAGE: 'TENDER_PACKAGE',
  TENDER_PACKAGE_EMPLOYEE_DETAILS: 'TENDER_PACKAGE_EMPLOYEE_DETAILS',
  CONTRACT: 'CONTRACT',
  CONTRACT_DETAIL: 'CONTRACT_DETAIL',
  CONTRACT_APPENDIX: 'CONTRACT_APPENDIX',
  CONTRACT_APPENDIX_DETAIL: 'CONTRACT_APPENDIX_DETAIL',
  COMPLETION_ACCEPTANCE: 'COMPLETION_ACCEPTANCE',
  COMPLETION_ACCEPTANCE_DETAIL: 'COMPLETION_ACCEPTANCE_DETAIL',
  BORROW_DOCUMENT: 'BORROW_DOCUMENT',

  BOARD_OF_DIRECTORS_WORK_SCHEDULE: 'BOARD_OF_DIRECTORS_WORK_SCHEDULE',
  BOARD_OF_DIRECTORS_WORK_SCHEDULE_DETAIL: 'BOARD_OF_DIRECTORS_WORK_SCHEDULE_DETAIL',
  EMPLOYEE: 'EMPLOYEE',
  EMPLOYEE_DETAIL: 'COMPLETION_ACCEPTANCE_DETAIL',
  LEAVE: 'LEAVE',

  OVERTIME_REGISTRATION: 'OVERTIME_REGISTRATION',
  SALARY_SHEET: 'SALARY_SHEET',
  SALARY_SHEET_DETAIL: 'SALARY_SHEET_DETAIL',
  TASK: 'TASK',

  DIRECTIVE_CONTENT: 'DIRECTIVE_CONTENT',
  DIRECTIVE_CONTENT_ATTACHMENT: 'DIRECTIVE_CONTENT_ATTACHMENT',

  TARGET: 'TARGET',
  REPORT_TEMPLATE: 'REPORT_TEMPLATE',
  REPORT_TEMPLATE_DETAILS: 'REPORT_TEMPLATE_DETAILS',
  REPORT_SERIAL_MANAGEMENT: 'REPORT_SERIAL_MANAGEMENT',
  WORK_MANAGEMENT_DIRECTIVE_CONTENT: 'WORK_MANAGEMENT_DIRECTIVE_CONTENT',
  WORK_MANAGEMENT_DIRECTIVE_CONTENT_ITEMS_RECORD_MANAGEMENT:
    'WORK_MANAGEMENT_DIRECTIVE_CONTENT_ITEMS_RECORD_MANAGEMENT',
  WORK_MANAGEMENT_DIRECTIVE_CONTENT_ITEMS_RECORDS_MANAGEMENT_DIRECTIVE_CONTENT:
    'WORK_MANAGEMENT_DIRECTIVE_CONTENT_ITEMS_RECORDS_MANAGEMENT_DIRECTIVE_CONTENT',
  WORK_MANAGEMENT_TARGET: 'WORK_MANAGEMENT_TARGET',
  WORK_MANAGEMENT_TASK: 'WORK_MANAGEMENT_TASK',
  INSURANCE_CONTRIBUTION_REPORT: 'INSURANCE_CONTRIBUTION_REPORT',
  PAYMENT_BENEFICIARY_REPORT: 'PAYMENT_BENEFICIARY_REPORT',

  DOCUMENT_FORM_ENTRY: 'DOCUMENT_FORM_ENTRY',
  DOCUMENT_FORM_ENTRY_DETAIL: 'DOCUMENT_FORM_ENTRY_DETAIL',

  WORD_MANAGEMENT_DESIGN_BID_ESTIMATION: 'WORD_MANAGEMENT_DESIGN_BID_ESTIMATION',

  EMPLOYEE_PAYROLL_REPORT: 'EMPLOYEE_PAYROLL_REPORT',
  APPROVAL_PROCESS: 'APPROVAL_PROCESS',
  APPROVAL_PROCESS_DETAIL: 'APPROVAL_PROCESS_DETAIL',
  SALARY_SHEET_FOR_LABOR_CONTRACT_REPORT: 'SALARY_SHEET_FOR_LABOR_CONTRACT_REPORT',
  SETUP_ANNUAL_HOLIDAY: 'SETUP_ANNUAL_HOLIDAY',
  SETUP_ANNUAL_HOLIDAY_DETAIL: 'SETUP_ANNUAL_HOLIDAY_DETAIL',
  WORK_MANAGEMENT_OTHER: 'WORK_MANAGEMENT_OTHER',

  GUARANTEE_LETTER_TRACKING: 'GUARANTEE_LETTER_TRACKING',
  OVERTIME_ATTENDANCE_TRACKING: 'OVERTIME_ATTENDANCE_TRACKING',
  DOCUMENT_DECISION: 'DOCUMENT_DECISION',
  CAPITAL_INCREASE_PLAN: 'CAPITAL_INCREASE_PLAN',
  CAPITAL_INCREASE_PLAN_DETAIL: 'CAPITAL_INCREASE_PLAN_DETAIL',
  ADJUSTED_CAPITAL_INCREASE_PLAN: 'ADJUSTED_CAPITAL_INCREASE_PLAN',
  ADJUSTED_CAPITAL_INCREASE_PLAN_DETAIL: 'ADJUSTED_CAPITAL_INCREASE_PLAN_DETAIL',
  OUTSTANDING_EQUIPMENT: 'OUTSTANDING_EQUIPMENT',
  OUTSTANDING_EQUIPMENT_DETAIL: 'OUTSTANDING_EQUIPMENT_DETAIL',
  ADJUSTED_INVESTMENT: 'ADJUSTED_INVESTMENT',

  BACKLOG_PROJECT_MANAGEMENT: 'BACKLOG_PROJECT_MANAGEMENT',
  BACKLOG_PROJECT_MANAGEMENT_PROJECT: 'BACKLOG_PROJECT_MANAGEMENT_PROJECT',
  BACKLOG_PROJECT_MANAGEMENT_PROJECT_DETAIL: 'BACKLOG_PROJECT_MANAGEMENT_PROJECT_DETAIL',
  ASSET_INCREMENT: 'ASSET_INCREMENT',
  ASSET_INCREMENT_DETAIL: 'ASSET_INCREMENT_DETAIL',

  FORM_DOCUMENT_MANAGER: 'FORM_DOCUMENT_MANAGER',
  PROFESSION_KEY_WORD: 'PROFESSION_KEY_WORD',
  ADJUSTED_COST_ESTIMATION: 'ADJUSTED_COST_ESTIMATION',

  PROJECT_SCHEDULE_SETUP: 'PROJECT_SCHEDULE_SETUP',
  PROJECT_SCHEDULE_SETUP_DETAIL: 'PROJECT_SCHEDULE_SETUP_DETAIL',
  HISTORY_ACTION: 'HISTORY_ACTION',

  SPENDING_COMMITMENT: 'SPENDING_COMMITMENT',

  INVESTMENT_FORM: 'INVESTMENT_FORM',
  INVESTMENT_TYPE: 'INVESTMENT_TYPE',
  CONSTRUCTION_TYPE: 'CONSTRUCTION_TYPE',
  PERMISSION_GROUP: 'PERMISSION_GROUPS',
  PAYMENT_RECEIPT: 'PAYMENT_RECEIPT',
  PROFESSION_APPROVAL_PROCESS_DETAIL: 'PROFESSION_APPROVAL_PROCESS_DETAIL',
  RPT_ANNUAL_TASK_LIST_STATISTICS: 'RPT_ANNUAL_TASK_LIST_STATISTICS',
  RPT_YEARLY_SUMMARY: 'RPT_YEARLY_SUMMARY',
  RPT_SUMMARY_TARGETS_TASKS_FIRST_6_MONTHS: 'RPT_SUMMARY_TARGETS_TASKS_FIRST_6_MONTHS',
  RPT_SUMMARY_TARGETS_TASKS_LAST_6_MONTHS: 'RPT_SUMMARY_TARGETS_TASKS_LAST_6_MONTHS',
  CONTRACTOR_SELECTION_RESULT: 'CONTRACTOR_SELECTION_RESULT',
  CONTRACTOR_SELECTION_RESULT_BIDDING_SECTOR_REPORT:
    'CONTRACTOR_SELECTION_RESULT_BIDDING_SECTOR_REPORT',
  RPT_REPORT_ON_IMPLEMENTATION_OF_DIRECTIVES: 'RPT_REPORT_ON_IMPLEMENTATION_OF_DIRECTIVES',
  A_B_SETTLEMENT: 'A_B_SETTLEMENT',
  A_B_SETTLEMENT_DETAIL: 'A_B_SETTLEMENT_DETAIL',
  REPORT_ANNEX_3A: 'REPORT_ANNEX_3A',
  REPORT_ANNEX_3A_DETAIL: 'REPORT_ANNEX_3A_DETAIL',
  REPORT_ANNEX_3A_OVERVIEW: 'REPORT_ANNEX_3A_OVERVIEW',
  CONTRACTOR_SELECTION_RESULT_BY_MANAGING_DIRECTOR_REPORT:
    'CONTRACTOR_SELECTION_RESULT_BY_MANAGING_DIRECTOR_REPORT',
  CONTRACTOR_SELECTION_RESULT_BY_DEPARTMENT_REPORT:
    'CONTRACTOR_SELECTION_RESULT_BY_DEPARTMENT_REPORT',
  ANNUAL_BIDDING_SUMMARY_REPORT: 'ANNUAL_BIDDING_SUMMARY_REPORT',
  A_B_ADJUSTMENT_SETTLEMENT: 'A_B_ADJUSTMENT_SETTLEMENT',
  A_B_ADJUSTMENT_SETTLEMENT_DETAIL: 'A_B_ADJUSTMENT_SETTLEMENT_DETAIL',
  PROJECT_DISBURSEMENT: 'PROJECT_DISBURSEMENT',
  PROJECT_DISBURSEMENT_REPORT: 'PROJECT_DISBURSEMENT_REPORT',
  PROPOSED_SETTLEMENT_INVESTMENT_COST: 'PROPOSED_SETTLEMENT_INVESTMENT_COST',
  PROPOSED_SETTLEMENT_INVESTMENT_COST_DETAIL: 'PROPOSED_SETTLEMENT_INVESTMENT_COST_DETAIL',
  ADVANCE_PAYMENT: 'ADVANCE_PAYMENT',
  ADVANCE_PAYMENT_DETAIL: 'ADVANCE_PAYMENT_DETAIL',
  PROJECT_DEBT_STATUS_STATISTICS_REPORT: 'PROJECT_DEBT_STATUS_STATISTICS_REPORT',
  CONTRACT_SETTLEMENT: 'CONTRACT_SETTLEMENT',

  TEMPLATE_STATISTICS_REPORT: 'TEMPLATE_STATISTICS_REPORT',
  DATA_RECONCILIATION_TABLE: 'DATA_RECONCILIATION_TABLE',
  DATA_RECONCILIATION_TABLE_REPORT: 'DATA_RECONCILIATION_TABLE_REPORT',
  SAVING_RATE_CONTRACTOR_SELECTION_PLAN_REPORT: 'SAVING_RATE_CONTRACTOR_SELECTION_PLAN_REPORT',
  SAVING_RATE_CONTRACTOR_SELECTION_PLAN_REPORT_TOTAL:
    'SAVING_RATE_CONTRACTOR_SELECTION_PLAN_REPORT_TOTAL',
  SAVING_RATE_CONTRACTOR_SELECTION_PLAN_REPORT_DETAILS:
    'SAVING_RATE_CONTRACTOR_SELECTION_PLAN_REPORT_DETAILS',
  CAPITAL_PLAN_DISBURSEMENT_PROGRESS_REPORT: 'CAPITAL_PLAN_DISBURSEMENT_PROGRESS_REPORT',
  PROJECT_DEPARTMENT_DISBURSEMENT_PROGRESS_REPORT:
    'PROJECT_DEPARTMENT_DISBURSEMENT_PROGRESS_REPORT',
  PROJECT_DEPARTMENT_DISBURSEMENT_PROGRESS_DETAIL_REPORT:
    'PROJECT_DEPARTMENT_DISBURSEMENT_PROGRESS_DETAIL_REPORT',
  SAVING_IN_BIDDING_REPORT: 'SAVING_IN_BIDDING_REPORT',
  REPORT_PUBLIC_INVESTMENT_SETTLEMENT: 'REPORT_PUBLIC_INVESTMENT_SETTLEMENT',
  REPORT_PUBLIC_INVESTMENT_SETTLEMENT_DETAIL: 'REPORT_PUBLIC_INVESTMENT_SETTLEMENT_DETAIL',
  SECTOR_CODE: 'SECTOR_CODE',
  FUNDING_PROGRAM_CODE: 'FUNDING_PROGRAM_CODE',
  TYPE_CODE: 'TYPE_CODE',
  BUDGET_ITEM_CODE: 'BUDGET_ITEM_CODE',
  DOCUMENT_TYPE: 'DOCUMENT_TYPE',
  EMPLOYEE_ANNUAL_EVALUATION_RESULT_REPORT: 'EMPLOYEE_ANNUAL_EVALUATION_RESULT_REPORT',
  OVERTIME_ATTENDANCE_TRACKING_DETAIL: 'OVERTIME_ATTENDANCE_TRACKING_DETAIL',
  CONTRACTOR_PROJECT_STAFF_REPORT: 'CONTRACTOR_PROJECT_STAFF_REPORT',
  WEEKLY_PROJECT_SCHEDULE_PLAN: 'WEEKLY_PROJECT_SCHEDULE_PLAN',
  WEEKLY_PROJECT_SCHEDULE_PLAN_DETAIL: 'WEEKLY_PROJECT_SCHEDULE_PLAN_DETAIL',
  MONTHLY_PLANNED_DISBURSEMENT: 'MONTHLY_PLANNED_DISBURSEMENT',
  MONTHLY_PLANNED_DISBURSEMENT_DETAIL: 'MONTHLY_PLANNED_DISBURSEMENT_DETAIL',
  FINANCIAL_SETTLEMENT_REPORT_PROPOSED_SETTLEMENT_INVESTMENT_COST:
    'FINANCIAL_SETTLEMENT_REPORT_PROPOSED_SETTLEMENT_INVESTMENT_COST',
  FINANCIAL_SETTLEMENT_REPORT_ASSET_GROUP: 'FINANCIAL_SETTLEMENT_REPORT_ASSET_GROUP',
  FINANCIAL_SETTLEMENT_REPORT_BUBGET_FUND_DETAIL: 'FINANCIAL_SETTLEMENT_REPORT_BUBGET_FUND_DETAIL',
  FINANCIAL_SETTLEMENT_REPORT_DATA_RECONCILIATION:
    'FINANCIAL_SETTLEMENT_REPORT_DATA_RECONCILIATION',
  FINANCIAL_SETTLEMENT_REPORT: 'FINANCIAL_SETTLEMENT_REPORT',
  FINANCIAL_SETTLEMENT_REPORT_DOCUMENT_LIST: 'FINANCIAL_SETTLEMENT_REPORT_DOCUMENT_LIST',
  FINANCIAL_SETTLEMENT_REPORT_INVESTMENT_COSTS: 'FINANCIAL_SETTLEMENT_REPORT_INVESTMENT_COSTS',
  FINANCIAL_SETTLEMENT_REPORT_PROJECT_DEBT_STATUS_STATISTICS:
    'FINANCIAL_SETTLEMENT_REPORT_PROJECT_DEBT_STATUS_STATISTICS',
  DIRECTIVE_IMPLEMENTATION: 'DIRECTIVE_IMPLEMENTATION',

  PROJECT_DEPLOYMENT_STATUS_REPORT: 'PROJECT_DEPLOYMENT_STATUS_REPORT',
  DISBURSEMENT_PROGRESS_BY_FUNDING_SOURCE: 'DISBURSEMENT_PROGRESS_BY_FUNDING_SOURCE',
  RPT_REPORT_ON_IMPLEMENTATION_OF_DIRECTIVES_BY_USER_DETAIL:
    'RPT_REPORT_ON_IMPLEMENTATION_OF_DIRECTIVES_BY_USER_DETAIL',
  RPT_REPORT_ON_IMPLEMENTATION_OF_DIRECTIVES_BY_USER:
    'RPT_REPORT_ON_IMPLEMENTATION_OF_DIRECTIVES_BY_USER',
  DISBURSEMENT_PROGRESS_SUMMARY: 'DISBURSEMENT_PROGRESS_SUMMARY',
  DISBURSEMENT_PROGRESS_SUMMARY_DETAIL: 'DISBURSEMENT_PROGRESS_SUMMARY_DETAIL',
  DISBURSEMENT_PROGRESS_SUMMARY_DETAIL_BY_ROOM: 'DISBURSEMENT_PROGRESS_SUMMARY_DETAIL_BY_ROOM',
  LAND_ACQUISITION_AND_COMPENSATION_PROGRESS_REPORT:
    'LAND_ACQUISITION_AND_COMPENSATION_PROGRESS_REPORT',
  BOARD_OF_DIRECTOR: 'BOARD_OF_DIRECTOR',
  BANK: 'BANK',
  COMPLETION_ACCEPTANCE_NOTICE: 'COMPLETION_ACCEPTANCE_NOTICE',
  PROJECT_SETTLEMENT: 'PROJECT_SETTLEMENT',
};
const printTypes = {
  SALE: 'sale',
  CONTRACTOR_SELECTION_PLAN: 'CONTRACTOR_SELECTION_PLAN',
  TENDER_PACKAGE: 'TENDER_PACKAGE',
  CONTRACT_TASK_MANAGEMENT: 'CONTRACT_TASK_MANAGEMENT',
};

export const payerRecipientTypes = {
  CUSTOMER: 1,
  SUPPLIER: 2,
  STAFF: 3,
};

const businessTableAppearance = {
  columnVisibility: {
    specifications: false,
    unitExchange: false,
    percentDiscount: false,
    cashDiscount: false,
    percentVat: false,
    cashVat: false,
    priceBuyAfterDiscount: false,
    quantityPromotion: false,
    quantityReceive: false,
    weight: false,
    totalWeight: false,
    branchId: false,
  },
};

const productTypes = {
  PRODUCT: 1,
  SHELL: 2,
};

export const QUERIES = Object.freeze(queryKeys);
export const MUTATE = Object.freeze(mutateKeys);
export const TABLES = Object.freeze(tables);
export const PATHS = Object.freeze(paths);
export const GROUP_TYPES = Object.freeze(groupTypes);
export const BUSINESS_TABLE_APPEARANCE = Object.freeze(businessTableAppearance);
export const PRODUCT_TYPES = Object.freeze(productTypes);
export const PAYER_RECIPIENT_TYPES = Object.freeze(payerRecipientTypes);
export const PRINT_TYPES = Object.freeze(printTypes);

export const DEFAULT_QUERY_PAGINATION_PARAMS: QueryPaginationParams = {
  pageIndex: 1,
  // pageSize: 20,
  pageSize: -1,
  sortColumn: 'Id',
  sortOrder: 1,
  isPage: true,
};

export const DEFAULT_PERIOD_SEARCH: PeriodFilter = {
  range: [startOfMonth(new Date()), new Date()],
};

export const [
  saveLabel,
  createLabel,
  updateLabel,
  editLabel,
  deleteLabel,
  copyLabel,
  backLabel,
  closeLabel,
  selectLabel,
  enterLabel,
  cancelLabel,
  continueLabel,
  searchLabel,
  viewLabel,
  importLabel,
  exportLabel,
  okLabel,
  scanLabel,
  applyLabel,
  saveTempLabel,
  sendLabel,
  backToHomeLabel,
  addLabel,
  attachLabel,
  downloadTemplateLabel,
  cloneLabel,
  getDataLabel,
  rejectLabel,
  reloadLabel,
  saveAndForwardLabel,
] = getActionLabel([
  'save',
  'create',
  'update',
  'edit',
  'delete',
  'copy',
  'back',
  'close',
  'select',
  'enter',
  'cancel',
  'continue',
  'search',
  'view',
  'import',
  'export',
  'ok',
  'scan',
  'apply',
  'saveTemp',
  'send',
  'backToHome',
  'add',
  'attach',
  'downloadTemplate',
  'clone',
  'getData',
  'reject',
  'reload',
  'saveAndForward',
]);

export const FORMAT_DATE = 'dd/MM/yyyy';
export const FORMAT_DEFAULT_DATE_TIME = 'dd/MM/yyyy HH:mm';
export const FORMAT_DATE_FORM = 'yyyy-MM-dd';
export const FORMAT_DATE_TIME = 'yyyy-MM-dd HH:mm';
export const FILTER_DATE = 'yyyyMMdd';
export const WIDTH_MD = 768;
export const ZOOM_MAP_DEFAULT = 15;
export const COORDINATE_DECIMAL_SCALE = 14;
export const DEFAULT_DECIMAL_SCALE = 4;
export const DEFAULT_DECIMAL = 0;
export const DEFAULT_ROUND_SCALE = 10000;

export const COMPONENT = 'COMPONENT';
export const NEW_ROW_INDEX = -1;

export const PRICE_TYPES = [
  {
    id: 1,
    name: i18next.t('priceType.importPrice'),
    fieldName: 'purchasePrice',
  },
  {
    id: 2,
    name: i18next.t('priceType.retailPrice'),
    fieldName: 'retailPrice',
  },
  {
    id: 3,
    name: i18next.t('priceType.wholesalePrice'),
    fieldName: 'wholesalePrice',
  },
  {
    id: 4,
    name: i18next.t('priceType.specialPrice'),
    fieldName: 'specialPrice',
  },
];

export const priceTypeHash = hash(PRICE_TYPES)!;

export const RECEIPT_EXPEND_CONTENT_TYPES = [
  {
    id: 0,
    name: i18next.t('receiptExpendContent.receipt'),
  },
  {
    id: 1,
    name: i18next.t('receiptExpendContent.expend'),
  },
  // {
  //   id: 2,
  //   name: i18next.t('receiptExpendContent.export'),
  // },
  // {
  //   id: 3,
  //   name: i18next.t('receiptExpendContent.import'),
  // },
];

export const DIRECTIVE_COMPLETION_TYPES = [
  {
    id: 1,
    name: i18next.t('page.completionTypeOptions.onTime', { ns: 'directiveContent' }),
  },
  {
    id: 2,
    name: i18next.t('page.completionTypeOptions.overdue', { ns: 'directiveContent' }),
  },
];

export const METHOD_TENDER_TYPES = [
  {
    id: 1,
    name: i18next.t('page.methodTenderTypes.domestic', { ns: 'contractorSelectionPlan' }),
  },
  {
    id: 2,
    name: i18next.t('page.methodTenderTypes.international', { ns: 'contractorSelectionPlan' }),
  },
];

export const METHOD_BIDDING_TYPES = [
  {
    id: 1,
    name: i18next.t('page.methodBiddingTypes.online', { ns: 'contractorSelectionPlan' }),
  },
  {
    id: 2,
    name: i18next.t('page.methodBiddingTypes.offline', { ns: 'contractorSelectionPlan' }),
  },
];

export const GUARANTEE_LETTER_TRACKING_STATUS_TYPES = [
  {
    id: 1,
    name: i18next.t('page.statusOptions.onTime', { ns: 'guaranteeLetterTracking' }),
  },
  {
    id: 2,
    name: i18next.t('page.statusOptions.overdue', { ns: 'guaranteeLetterTracking' }),
  },
];

const receiptExpendTypeStyles = {
  0: 'border-transparent bg-primary-400 text-primary-foreground shadow hover:bg-primary-400/80',
  1: 'border-transparent bg-yellow-300 text-yellow-foreground shadow hover:bg-yellow-400/80',
  2: 'border-transparent bg-info-400 text-info-foreground shadow hover:bg-info-400/80',
  3: 'border-transparent bg-success-400 text-destructive-foreground shadow hover:bg-success-400/80',
};
export const RECEIPT_EXPEND_TYPE_STYLES = Object.freeze(receiptExpendTypeStyles);
export const receiptExpendHash = hash(RECEIPT_EXPEND_CONTENT_TYPES);

const receiptExpendContentTypesMap = {
  RECEIPT: 0,
  EXPEND: 1,
  EXPORT: 2,
  IMPORT: 3,
};

export const RECEIPT_EXPEND_CONTENT_TYPE_MAP = Object.freeze(receiptExpendContentTypesMap);

export const ORDER_RECEIVE_STATUS = [
  {
    id: 0,
    name: i18next.t('orderReceiveStatus.notYetReceived'),
  },
  {
    id: 1,
    name: i18next.t('orderReceiveStatus.receiving'),
  },
  {
    id: 2,
    name: i18next.t('orderReceiveStatus.received'),
  },
  {
    id: 3,
    name: i18next.t('orderReceiveStatus.canceled'),
  },
];

const orderReceiveStatusMap = {
  NOT_RECEIVED: 0,
  RECEIVING: 1,
  RECEIVED: 2,
  CANCELED: 3,
};

export const ORDER_RECEIVE_STATUS_MAP = Object.freeze(orderReceiveStatusMap);

export const ORDER_DELIVERY_STATUS = [
  {
    id: 0,
    name: i18next.t('orderDeliveryStatus.notYetDelivery'),
  },
  {
    id: 1,
    name: i18next.t('orderDeliveryStatus.delivering'),
  },
  {
    id: 2,
    name: i18next.t('orderDeliveryStatus.delivered'),
  },
  {
    id: 3,
    name: i18next.t('orderDeliveryStatus.canceled'),
  },
];

const orderDeliveryStatusMap = {
  NOT_DELIVERY: 0,
  DELIVERING: 1,
  DELIVERED: 2,
  CANCELED: 3,
};

export const ORDER_DELIVERY_STATUS_MAP = Object.freeze(orderDeliveryStatusMap);

export const UNIT_DEFAULTS = {
  unit: i18next.t('unitDefault.unit'),
  unit1: i18next.t('unitDefault.unit1'),
  unit2: i18next.t('unitDefault.unit2'),
  unit3: i18next.t('unitDefault.unit3'),
};

export const CAPITAL_CALCULATION_METHODS = [
  {
    id: 0,
    name: i18next.t('capitalCalculationMethods.averageDuringPeriod'),
  },
  {
    id: 1,
    name: i18next.t('capitalCalculationMethods.weightedAverageOfTheEntirePeriodOfReserves'),
  },
  {
    id: 2,
    name: i18next.t('capitalCalculationMethods.firstInFirstOut'),
  },
  {
    id: 3,
    name: i18next.t('capitalCalculationMethods.lastInFirstOutWholePeriod'),
  },
  {
    id: 4,
    name: i18next.t('capitalCalculationMethods.weightedAverageAfterEachImportAndExport'),
  },
  {
    id: 5,
    name: i18next.t('capitalCalculationMethods.lastInFirstOutWhenExporting'),
  },
];

export const TOP_10_BY = [
  {
    id: 0,
    name: 'THEO DOANH SỐ', //i18next.t('capitalCalculationMethods.weightedAverageOfTheEntirePeriodOfReserves'),
  },
  {
    id: 1,
    name: 'THEO SỐ LƯỢNG', //i18next.t('capitalCalculationMethods.averageDuringPeriod'),
  },
];

export const productionOrderTypeValues = {
  AUTO: 1,
  MANUAL: 2,
};
export const PRODUCTION_ORDER_TYPE_VALUES = Object.freeze(productionOrderTypeValues);

export const productionStatusValues = {
  NON_PRODUCTION: 1,
  IN_PRODUCTION: 2,
  PRODUCED: 3,
};
export const PRODUCTION_STATUS_VALUES = Object.freeze(productionStatusValues);

export const PRODUCTION_ORDER_TYPES = [
  {
    id: 1,
    name: i18next.t('fields.productionOrderTypeContent.autoIE', { ns: 'productionOrder' }),
  },
  {
    id: 2,
    name: i18next.t('fields.productionOrderTypeContent.manualIE', { ns: 'productionOrder' }),
  },
];

export const PRODUCTION_STATUS = [
  {
    id: 1,
    name: i18next.t('fields.productionStatusContent.nonProduction', { ns: 'productionOrder' }),
  },
  {
    id: 2,
    name: i18next.t('fields.productionStatusContent.inProduction', { ns: 'productionOrder' }),
  },
  {
    id: 3,
    name: i18next.t('fields.productionStatusContent.produced', { ns: 'productionOrder' }),
  },
];

export const LAND_CLEARANCE_PLAN_TYPE = [
  { id: 1, name: i18next.t('landClearancePlanType.withoutCompensation', { ns: 'common' }) },
  {
    id: 2,
    name: i18next.t('landClearancePlanType.withCompensation', { ns: 'common' }),
  },
];

export const PROFESSIONS = {
  SALARY_SHEET: 0,
  SALE: 1,
  PROJECT: 14,
  CONTRACTOR_SELECTION_PLAN: 45,
  CONTRACT_TASK_MANAGEMENT: 46,
  TENDER_PACKAGE: 47,
  CONTRACT: 48,
  CONTRACT_APPENDIX: 49,
  EMPLOYEE: 50,
  ADJUSTED_INVESTMENT: 50,
  ADJUSTED_COST_ESTIMATION: 51,
  COMPLETION_ACCEPTANCE: 52,
  TRAINING_MANAGEMENT: 54,
  LEAVE: 55,
  BORROW_DOCUMENT: 56,
  OVERTIME_REGISTRATION: 57,
  BOARD_OF_DIRECTORS_WORK_SCHEDULE: 58,
  DIRECTIVE_CONTENT: 59,
  TARGET: 60,
  TASK: 61,
  WORK_MANAGEMENT_DIRECTIVE_CONTENT: 62,
  WORK_MANAGEMENT_TASK: 62,
  WORK_MANAGEMENT_TARGET: 63,
  WORK_MANAGEMENT_DESIGN_BID_ESTIMATION: 65,
  ADJUSTED_CAPITAL_INCREASE_PLAN: 65,
  WORK_MANAGEMENT_OTHER: 66,
  GUARANTEE_LETTER_TRACKING: 77, //Theo dõi thư bảo lãnh
  CAPITAL_INCREASE_PLAN: 84,
  SPENDING_COMMITMENT: 85, // Cam kết chi
  PAYMENT_RECEIPT: 86, // Chứng từ thanh toán
  A_B_SETTLEMENT: 100, // Quyết toán A-B
  REPORT_ANNEX_3A: 101, // Để tạm
  DESIGN_TASK_MANAGEMENT: 102,
  A_B_ADJUSTMENT_SETTLEMENT: 110, // Quyết toán A-B điều chỉnh
  ADVANCE_PAYMENT: 113, // Báo cáo tình hình tạm ứng
  REPORT_PUBLIC_INVESTMENT_SETTLEMENT: 118,
  FINANCIAL_SETTLEMENT_REPORT: 130,
  DIRECTIVE_IMPLEMENTATION_REPORT: 131, // Báo cáo kết quả thực hiện chỉ đạo
  DISBURSEMENT_PROGRESS_BY_FUNDING_SOURCE: 133,
  COMPLETION_ACCEPTANCE_NOTICE: 165,
  PROJECT_COST_ESTIMATION_DEVICE: 166,
  PROJECT_SETTLEMENT: 167,
};

export const TASK_TYPE = [
  {
    id: 1,
    name: i18next.t('taskType.finishToStart', { ns: 'common' }),
  },
  {
    id: 2,
    name: i18next.t('taskType.startToStart', { ns: 'common' }),
  },
  {
    id: 3,
    name: i18next.t('taskType.finishToFinish', { ns: 'common' }),
  },
];

export const LEVEL_TYPE = [
  { id: 1, name: 'I' },
  { id: 2, name: 'II' },
  { id: 3, name: 'III' },
  { id: 4, name: 'IV' },
];

export const COST_ITEM_GROUPS = [
  {
    id: 1,
    name: i18next.t('costItemGroup.compensation', { ns: 'common' }),
  },
  {
    id: 2,
    name: i18next.t('costItemGroup.constructionConsulting', { ns: 'common' }),
  },
];

export const DEPARTMENT_TYPES = [
  {
    id: 1,
    name: i18next.t('departmentTypes.projectManagement', { ns: 'common' }),
  },
  {
    id: 2,
    name: i18next.t('departmentTypes.planning', { ns: 'common' }),
  },
  {
    id: 3,
    name: i18next.t('departmentTypes.accounting', { ns: 'common' }),
  },
];

export const PROJECT_IMPLEMENTATION_STEPS = [
  {
    id: 1,
    name: i18next.t('fields.implementationSteps.step1', { ns: 'project' }),
  },
  {
    id: 2,
    name: i18next.t('fields.implementationSteps.step2', { ns: 'project' }),
  },
  {
    id: 3,
    name: i18next.t('fields.implementationSteps.step3', { ns: 'project' }),
  },
  {
    id: 4,
    name: i18next.t('fields.implementationSteps.step4', { ns: 'project' }),
  },
  {
    id: 5,
    name: i18next.t('fields.implementationSteps.step5', { ns: 'project' }),
  },
  {
    id: 6,
    name: i18next.t('fields.implementationSteps.step6', { ns: 'project' }),
  },
  {
    id: 7,
    name: i18next.t('fields.implementationSteps.step7', { ns: 'project' }),
  },
  {
    id: 8,
    name: i18next.t('fields.implementationSteps.step8', { ns: 'project' }),
  },
];

export const CONTRACT_TYPES = [
  {
    id: 1,
    name: i18next.t('contractTypes.consulting', { ns: 'common' }),
  },
  {
    id: 3,
    name: i18next.t('contractTypes.construction', { ns: 'common' }),
  },
  {
    id: 4,
    name: i18next.t('contractTypes.equipment', { ns: 'common' }),
  },
  {
    id: 5,
    name: i18next.t('contractTypes.consultingFee', { ns: 'common' }),
  },
  {
    id: 2,
    name: i18next.t('contractTypes.other', { ns: 'common' }),
  },
];

export const ACTION_TYPES = [
  {
    id: 1,
    name: i18next.t('actionTypes.modify', { ns: 'common' }),
  },
  {
    id: 2,
    name: i18next.t('actionTypes.approve', { ns: 'common' }),
  },
];

export const COMPLETION_ACCEPTANCE_TYPE_OPTIONS = [
  {
    id: 1,
    name: i18next.t('completionAcceptanceTypes.period', { ns: 'common' }),
  },
  {
    id: 2,
    name: i18next.t('completionAcceptanceTypes.complete', { ns: 'common' }),
  },
];
export const DIRECTIVE_IMPLEMENTATION_REPORT_TYPES = {
  TARGET: 1,
  TASK: 2,
};
export const IMPLEMENTATION_STEPS_TYPES = [
  { id: 1, name: i18next.t('implementationStepsTypes.1', { ns: 'common' }) },
  { id: 2, name: i18next.t('implementationStepsTypes.2', { ns: 'common' }) },
  { id: 3, name: i18next.t('implementationStepsTypes.3', { ns: 'common' }) },
  { id: 4, name: i18next.t('implementationStepsTypes.4', { ns: 'common' }) },
  { id: 5, name: i18next.t('implementationStepsTypes.5', { ns: 'common' }) },
  { id: 6, name: i18next.t('implementationStepsTypes.6', { ns: 'common' }) },
  { id: 7, name: i18next.t('implementationStepsTypes.7', { ns: 'common' }) },
  { id: 8, name: i18next.t('implementationStepsTypes.8', { ns: 'common' }) },
];

export const PROJECT_STATUS_PREPARING = {
  creatorName: null,
  updaterName: null,
  storeId: 12,
  branchId: null,
  isActive: true,
  id: 3,
  code: 'gdcb',
  name: 'Giai đoạn chuẩn bị dự án',
  note: 'lập đề xuất chương trình, dự án sử dụng vốn vay ODA và vốn vay ưu đãi nước ngoài (nếu có); lập, thẩm định Báo cáo nghiên cứu tiền khả thi đầu tư xây dựng hoặc Báo cáo đề xuất chủ trương đầu tư để quyết định hoặc chấp thuận chủ trương đầu tư (nếu có); khảo sát xây dựng phục vụ lập dự án; lập, thẩm định, phê duyệt quy hoạch xây dựng làm cơ sở lập dự án; lập, thẩm định Báo cáo nghiên cứu khả thi đầu tư xây dựng hoặc Báo cáo kinh tế - kỹ thuật đầu tư xây dựng để phê duyệt dự án, quyết định đầu tư xây dựng; các công việc cần thiết khác liên quan đến chuẩn bị dự án;',
};
export const PROJECT_STATUS_IMPLEMENTING = {
  creatorName: null,
  updaterName: null,
  storeId: 12,
  branchId: null,
  isActive: true,
  id: 4,
  code: 'gdth',
  name: 'Giai đoạn thực hiện dự án',
  note: 'chuẩn bị mặt bằng xây dựng, rà phá bom mìn (nếu có); khảo sát xây dựng phục vụ thiết kế triển khai sau thiết kế cơ sở; lập, thẩm định, phê duyệt thiết kế, dự toán xây dựng; cấp giấy phép xây dựng (đối với công trình theo quy định phải có giấy phép xây dựng); ký kết hợp đồng xây dựng; thi công xây dựng công trình; giám sát thi công xây dựng; tạm ứng, thanh toán khối lượng hoàn thành; vận hành, chạy thử; nghiệm thu hoàn thành công trình xây dựng; quyết toán hợp đồng xây dựng; giám sát, đánh giá dự án đầu tư xây dựng; các công việc cần thiết khác liên quan đến thực hiện dự án;',
};
export const PROJECT_STATUS_COMPLETED = {
  creatorName: null,
  updaterName: null,
  storeId: 12,
  branchId: null,
  isActive: true,
  id: 5,
  code: 'gdkt',
  name: 'Giai đoạn kết thúc xây dựng',
  note: 'quyết toán hợp đồng xây dựng, quyết toán vốn đầu tư dự án hoàn thành, xác nhận hoàn thành công trình; bàn giao công trình đưa vào sử dụng; bảo hành công trình xây dựng, bàn giao các hồ sơ liên quan; giám sát, đánh giá dự án đầu tư xây dựng; các công việc cần thiết khác.',
};

export const PROJECT_STATUS = [
  PROJECT_STATUS_PREPARING,
  PROJECT_STATUS_IMPLEMENTING,
  PROJECT_STATUS_COMPLETED,
];

export const AGENCY_TYPES = [
  {
    id: 1,
    name: i18next.t('agencyTypes.decide', { ns: 'common' }),
  },
  {
    id: 2,
    name: i18next.t('agencyTypes.issue', { ns: 'common' }),
  },
  {
    id: 3,
    name: i18next.t('agencyTypes.publisher', { ns: 'common' }),
  },
];

export const AGENCY_TYPE = {
  DECIDE: 1, // quyết định
  ISSUE: 2, // ban hành
  PUBLISHER: 3, // phát hành
};

export enum GROUP_DOC_CODE {
  BBNTDVSD = 'BBNTDVSD', // Biên bản nghiệm thu bàn giao đưa vào sử dụng
}

/**
 * Enum định nghĩa số tháng hết hạn cho các loại thời hạn
 * @readonly
 * @enum {number}
 */
export enum EXPIRATION_MONTH {
  /**
   * Thời hạn bảo hành (tính bằng tháng)
   * @type {number}
   * @default 12
   */
  WARRANTY_EXPIRATION_DATE = 12,
}

// Hằng số khởi tạo indexedDB
export const INDEXEDDB_DB_NAME = 'DevExtremeGridDB';
export const STORE_NAME = 'gridStateStore';
export const DB_VERSION = 1;
