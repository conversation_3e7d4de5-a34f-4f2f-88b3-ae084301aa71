import { saveDataGridStateToIndexedDB } from '@/components/devex-data-grid';
import { DEV_EXTREME_STORAGE_KEY } from '@/constant';
import { useScreenSize } from '@/hooks';
import { useGridState } from '@/hooks/useGridState';
import { cn } from '@/lib/utils';
import { Button } from 'devextreme-react';
import DataGrid, {
  Column,
  ColumnChooser,
  ColumnChooserSearch,
  ColumnChooserSelection,
  ColumnFixing,
  DataGridRef,
  Editing,
  FilterRow,
  Grouping,
  GroupPanel,
  HeaderFilter,
  Item,
  LoadPanel,
  Pager,
  Paging,
  Scrolling,
  Search,
  SearchPanel,
  StateStoring,
  Toolbar,
} from 'devextreme-react/data-grid';
import dxDataGrid, { dxDataGridColumn, dxDataGridRowObject } from 'devextreme/ui/data_grid';
import React, { useEffect, useRef, useState } from 'react';

type ColumnBand = { name: string; columns?: ColumnBand[] };

type Props = {
  onAddNewClick?: () => void;
  onRefresh?: () => void;
  customToolbar?: React.ReactNode | React.ReactNode[];
  hideSerialNumber?: boolean;
  columnsBands?: ColumnBand[];
  setRef?: (current: DataGridRef | null) => void;
};

export function DevexDataGrid({
  children,
  // ref,
  onRefresh,
  onAddNewClick,
  customToolbar,
  hideSerialNumber: hideSerialNumber,
  setRef,
  selectedRowKeys,
  ...props
}: React.PropsWithChildren & React.ComponentProps<typeof DataGrid> & Props) {
  const [expanded, setExpanded] = useState(true);
  const storageKey = `${DEV_EXTREME_STORAGE_KEY}_${props.id}`;
  const ref = useRef<DataGridRef>(null);
  useEffect(() => {
    if (setRef) {
      setRef(ref.current);
    }
  }, [setRef]);

  const { height } = useScreenSize();

  const { loadState, isLoadingState } = useGridState(storageKey, {
    selectedRowKeys,
  });

  const customSave = (state: any) => {
    if (state) {
      delete state.selectedRowKeys;
    }
    void saveDataGridStateToIndexedDB(storageKey, state, ref?.current?.instance());
  };

  return (
    <DataGrid
      keyExpr={'id'}
      ref={ref}
      id="dataGrid"
      columnAutoWidth
      allowColumnResizing
      columnResizingMode="widget"
      allowColumnReordering
      showBorders
      showColumnLines
      showRowLines
      hoverStateEnabled
      // rowAlternationEnabled
      wordWrapEnabled={true}
      columnFixing={{
        enabled: true,
        texts: {
          fix: 'Cố định',
          leftPosition: 'Cố định bên trái',
          rightPosition: 'Cố định bên phải',
          unfix: 'Bỏ cố định',
        },
      }}
      className={cn(
        'column-header-wrap',
        'max-h-[calc(100vh-9.8rem-32.8px)]',
        height < 600 ? 'min-h-[550px]' : 'min-h-[300px]'
      )}
      {...props}
    >
      <LoadPanel enabled={isLoadingState} />
      <ColumnChooser enabled mode="select" height="45rem">
        <ColumnChooserSearch enabled />
        <ColumnChooserSelection allowSelectAll selectByClick recursive />
      </ColumnChooser>
      <FilterRow visible showOperationChooser />
      <Scrolling mode="standard" rowRenderingMode="standard" />
      <Paging enabled defaultPageSize={10} />
      <Pager
        visible
        showInfo
        showNavigationButtons
        showPageSizeSelector
        displayMode="adaptive"
        allowedPageSizes={[5, 10, 50, 100, 'all']}
      />
      <Grouping autoExpandAll={expanded} contextMenuEnabled={true} expandMode="rowClick" />
      <StateStoring
        enabled
        type="custom"
        storageKey={storageKey}
        // customSave={state => {
        //   console.log('state:', state);
        // }}
        customLoad={loadState}
        customSave={customSave}
      />
      <HeaderFilter visible>
        <Search enabled mode="contains" />
      </HeaderFilter>
      <SearchPanel visible />
      <GroupPanel visible />
      {/* <FilterPanel visible /> */}
      <Editing confirmDelete allowUpdating allowDeleting allowAdding useIcons />
      <Toolbar>
        <Item name="groupPanel" />
        <Item location="after">
          <Button
            icon={expanded ? 'chevrondown' : 'chevronleft'}
            onClick={() => setExpanded(prevExpanded => !prevExpanded)}
          />
        </Item>
        {customToolbar}
        {onAddNewClick && (
          <Item>
            <Button icon="plus" onClick={onAddNewClick} />
          </Item>
        )}
        {onRefresh && (
          <Item location="after">
            <Button icon="refresh" onClick={onRefresh} />
          </Item>
        )}
        <Item name="columnChooserButton" />
        <Item name="exportButton" />
        {/* <Item name="searchPanel" /> */}
      </Toolbar>
      {/* <Column dataField="branchName" /> */}
      <ColumnFixing enabled />
      {!hideSerialNumber && (
        <Column
          dataField="serialNumber"
          caption="STT"
          dataType="number"
          format={',##0,##'}
          alignment="center"
          width={60}
          allowFiltering={false}
          allowSorting={false}
          fixed={true}
          fixedPosition="left"
          cellRender={(cellInfo: {
            column: dxDataGridColumn;
            columnIndex: number;
            component: dxDataGrid;
            data: Record<string, any>;
            displayValue: any;
            oldValue: any;
            row: dxDataGridRowObject;
            rowIndex: number;
            rowType: string;
            text: string;
            value: any;
            watch: () => void;
          }) => {
            if (cellInfo.rowType === 'data') {
              const pageIndex = cellInfo.component.pageIndex();
              const pageSize = cellInfo.component.pageSize();
              const visibleRowIndex = cellInfo.component
                .getVisibleRows()
                .filter(item => item.rowType === 'data')
                .indexOf(cellInfo.row);
              return pageIndex * pageSize + visibleRowIndex + 1;
            }
          }}
        />
      )}
      {children}
    </DataGrid>
  );
}
