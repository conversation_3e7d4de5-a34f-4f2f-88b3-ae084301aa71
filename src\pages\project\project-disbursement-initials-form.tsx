import { BasicDialog } from '@/components/basic-dialog';
import {
  DataTable,
  DataTableRowActions,
  EditableDatePickerCell,
  EditableInputCell,
} from '@/components/data-table';
import { ImportExcelConfigForm } from '@/components/import-excel-config-form';
import { PageLayout } from '@/components/page-layout';
import { FormField, FormLabel } from '@/components/ui/form';
import { enterLabel, PROFESSIONS, QUERIES, TABLES } from '@/constant';
import { useBoolean, useEntity } from '@/hooks';
import {
  defaultValuesProjectDisbursementInitial,
  Project,
  ProjectDetail,
  ProjectDisbursementInitial,
  ProjectTabChildrenProps,
} from '@/types';
import { CellContext, ColumnDef, Table } from '@tanstack/react-table';
import { Button } from 'devextreme-react';
import { SyntheticEvent, useMemo } from 'react';
import { useFormContext, useWatch } from 'react-hook-form';
import { InputNumber } from '@/components/ui/input';
import { getRandomNumber } from '@/lib/number';

const defaultRow = defaultValuesProjectDisbursementInitial;

export const ProjectDisbursementInitialsForm = ({
  role,
  loading,
  onBackToList,
  onCreateNew,
  onSubmit,
  t,
}: ProjectTabChildrenProps) => {
  const { state: isImportFormOpen, toggle: toggleImportForm } = useBoolean(false);
  const { control, setValue } = useFormContext<Project>();
  const [editableData, projectId] = useWatch({
    control,
    name: ['projectDisbursementInitials', 'id'],
  });

  const { fetch: fetchCostItems } = useEntity({
    queryKey: [QUERIES.COST_ITEM],
    model: 'cost-item',
  });

  const handleValueChange =
    (props: CellContext<ProjectDisbursementInitial, unknown>) =>
    (value: string | number): void => {
      const { original } = props.row;
      if (!original) return;

      const updatedRow = {
        ...props.row.original,
        [props.column.id]: Number(value),
      };
      props.table.options.meta?.updateRowValues(updatedRow, props.row.index);
    };
  const columns: ColumnDef<ProjectDisbursementInitial>[] = useMemo(
    () => [
      {
        id: 'disbursementYear',
        accessorKey: 'disbursementYear',
        header: t('fields.projectDisbursementInitials.disbursementYear'),
        cell: (props: CellContext<ProjectDisbursementInitial, unknown>) => (
          <EditableDatePickerCell {...props} />
        ),
      },
      {
        id: 'totalCompensationDisbursement',
        accessorKey: 'totalCompensationDisbursement',
        header: t('fields.projectDisbursementInitials.totalCompensationDisbursement'),
        cell: props => (
          <EditableInputCell
            {...props}
            type="number"
            hideDecimal
            onValueChange={value => handleValueChange(props)(value)}
          />
        ),
      },
      {
        id: 'totalConstructionConsultingDisbursement',
        accessorKey: 'totalConstructionConsultingDisbursement',
        header: t('fields.projectDisbursementInitials.totalConstructionConsultingDisbursement'),
        cell: props => (
          <EditableInputCell
            {...props}
            type="number"
            hideDecimal
            onValueChange={value => handleValueChange(props)(value)}
          />
        ),
      },
      {
        id: 'totalOtherDisbursement',
        accessorKey: 'totalOtherDisbursement',
        header: t('fields.projectDisbursementInitials.totalOtherDisbursement'),
        cell: props => (
          <EditableInputCell
            {...props}
            type="number"
            hideDecimal
            onValueChange={value => handleValueChange(props)(value)}
          />
        ),
      },
      {
        id: 'removeRow',
        header: ' ',
        size: 10,
        cell: props => {
          return (
            <DataTableRowActions
              onDelete={() => {
                props.table.options.meta?.removeRowByIndex(props.row.index);
              }}
              canDelete={role?.isCreate || role?.isUpdate}
            />
          );
        },
      },
    ],
    [t, role?.isCreate, role?.isUpdate]
  );

  const columnsForImportConfig = columns.map(column => {
    return {
      field: column.id,
      header: column.header as string,
    };
  });

  const handleAddNewRow = (table: Table<ProjectDisbursementInitial>) => {
    const newRow = { ...defaultRow, projectId, id: -getRandomNumber() };
    table.options.meta?.addNewRow(newRow);
  };

  return (
    <PageLayout
      onSaveChange={e => {
        const target = e.event?.currentTarget as unknown as SyntheticEvent<HTMLElement>;
        onSubmit(target);
      }}
      canSaveChange={role?.isUpdate}
      isSaving={loading}
      onCancel={onBackToList}
      customElementLeft={
        <>
          <Button
            text={t('content.createNew', { ns: 'common' })}
            className="uppercase"
            stylingMode="outlined"
            type="default"
            icon="plus"
            onClick={onCreateNew}
          />
        </>
      }
      contentClassName="!h-[calc(100vh-220px)]"
    >
      <div className="grid grid-cols-1 gap-x-8 gap-y-4 xl:grid-cols-24">
        <div className="col-span-1 space-y-4 xl:col-span-8">
          <div className="flex w-full items-center md:w-2/3 xl:w-full">
            <FormLabel
              className="w-[130px]"
              name="initialTotalInvestmentCapital"
              htmlFor="initialTotalInvestmentCapital"
            >
              {t('fields.initialTotalInvestmentCapital')}
            </FormLabel>
            <FormField
              className="min-w-0 flex-1"
              name="initialTotalInvestmentCapital"
              label={t('fields.initialTotalInvestmentCapital')}
            >
              <InputNumber
                placeholder={`${enterLabel} ${t('fields.initialTotalInvestmentCapital')}`}
              />
            </FormField>
          </div>
          <div className="flex w-full items-center md:w-2/3 xl:w-full">
            <FormLabel
              className="w-[130px]"
              name="initialTotalFundingRequirementCompensationl"
              htmlFor="initialTotalFundingRequirementCompensationl"
              title={t('fields.initialTotalFundingRequirementCompensationlLong')}
            >
              {t('fields.initialTotalFundingRequirementCompensationl')}
            </FormLabel>
            <FormField
              className="min-w-0 flex-1"
              name="initialTotalFundingRequirementCompensationl"
              label={t('fields.initialTotalFundingRequirementCompensationl')}
            >
              <InputNumber
                placeholder={`${enterLabel} ${t('fields.initialTotalFundingRequirementCompensationl')}`}
              />
            </FormField>
          </div>
          <div className="flex w-full items-center md:w-2/3 xl:w-full">
            <FormLabel
              className="w-[130px]"
              name="initialTotalFundingRequirementConstructionConsulting"
              htmlFor="initialTotalFundingRequirementConstructionConsulting"
              title={t('fields.initialTotalFundingRequirementConstructionConsultingLong')}
            >
              {t('fields.initialTotalFundingRequirementConstructionConsulting')}
            </FormLabel>
            <FormField
              className="min-w-0 flex-1"
              name="initialTotalFundingRequirementConstructionConsulting"
              label={t('fields.initialTotalFundingRequirementConstructionConsulting')}
            >
              <InputNumber
                placeholder={`${enterLabel} ${t('fields.initialTotalFundingRequirementConstructionConsulting')}`}
              />
            </FormField>
          </div>
          <div className="flex w-full items-center md:w-2/3 xl:w-full">
            <FormLabel
              className="w-[130px]"
              name="initialTotalFundingRequirementOther"
              htmlFor="initialTotalFundingRequirementOther"
              title={t('fields.initialTotalFundingRequirementOtherLong')}
            >
              {t('fields.initialTotalFundingRequirementOther')}
            </FormLabel>
            <FormField
              className="min-w-0 flex-1"
              name="initialTotalFundingRequirementOther"
              label={t('fields.initialTotalFundingRequirementOther')}
            >
              <InputNumber
                placeholder={`${enterLabel} ${t('fields.initialTotalFundingRequirementOther')}`}
              />
            </FormField>
          </div>
        </div>

        <div className="col-span-1 xl:col-span-24">
          <DataTable
            role={role}
            showPagination={false}
            editableData={editableData || []}
            tableId={TABLES.PROJECT_DISBURSEMENT_INITIALS}
            syncQueryParams={false}
            initialState={{
              columnVisibility: {
                costItemId: true,
                symbol: true,
                percentageRate: true,
                calculationMethod: true,
                preTaxValue: true,
                vat: false,
                vatTax: true,
                postTaxValue: true,
              },
            }}
            setEditableData={editedData => {
              setValue('projectDisbursementInitials', editedData);
            }}
            columns={columns}
            onAddButtonClick={handleAddNewRow}
          />
        </div>
      </div>
      <BasicDialog
        open={isImportFormOpen}
        title="Import Excel"
        toggle={toggleImportForm}
        className="max-w-[100vw] md:max-w-[90vw]"
      >
        <ImportExcelConfigForm<ProjectDetail>
          onApply={data => {
            setValue(
              'projectDisbursementInitials',
              data.map(item => {
                return {
                  ...defaultRow,
                  ...item,
                  projectId,
                };
              })
            );
            toggleImportForm();
          }}
          importModel="project"
          onClose={toggleImportForm}
          professionType={PROFESSIONS.PROJECT}
          professionColumns={columnsForImportConfig}
          onImported={() => {
            fetchCostItems({});
          }}
          additionalFormValues={[{ key: 'refId', value: projectId?.toString() || '' }]}
        />
      </BasicDialog>
    </PageLayout>
  );
};
