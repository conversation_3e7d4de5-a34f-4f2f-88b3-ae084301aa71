import { TABLES } from '@/constant';
import {
  IUserPermission,
  ReportPublicInvestmentSettlement,
  ReportPublicInvestmentSettlementDetail,
} from '@/types';
import { useFormContext, useWatch } from 'react-hook-form';

import { translationWithNamespace } from '@/lib/i18nUtils';
import { useEffect, useRef } from 'react';
import { DevexTreeList, getTreeListInstance } from '@/components/devex-tree-list';
import { Column } from 'devextreme-react/tree-list';
import { Summary, TotalItem } from 'devextreme-react/data-grid';
import { snakeCase } from 'lodash';
import { removeAccents } from '@/lib/text';
import { createTreeListExportingEventMultiHeader } from '@/lib/file';
import dxTreeList, { RowUpdatingEvent } from 'devextreme/ui/tree_list';
import { customizeNumberCell } from '@/components/devex-data-grid';

type ReportPublicInvestmentSettlementEditableTableProps = {
  role?: IUserPermission;
  calculateForm?: (
    details: ReportPublicInvestmentSettlementDetail[]
  ) => ReportPublicInvestmentSettlementDetail[];
  setRef?: (current: dxTreeList<any, any> | null) => void;
};

const t = translationWithNamespace('reportPublicInvestmentSettlement');

const exportFileName = snakeCase(removeAccents(t('model')));

export const ReportPublicInvestmentSettlementEditableTableTreeView = ({
  calculateForm,
  setRef,
}: ReportPublicInvestmentSettlementEditableTableProps) => {
  // const isMobile = useMediaQuery('(max-width: 768px)');
  const { control } = useFormContext<ReportPublicInvestmentSettlement>();

  const [editableData] = useWatch({
    control,
    name: ['reportPublicInvestmentSettlementDetails'],
  });

  const ref = useRef(null);
  const onExporting = () => {
    const treeListInstance = getTreeListInstance(ref);
    const renderColumnValues = {};

    if (treeListInstance) {
      const headerRow1 = [
        'STT',
        // t('fields.reportPublicInvestmentSettlementDetails.budgetFundId'),
        t('fields.reportPublicInvestmentSettlementDetails.projectId'),
        t('fields.reportPublicInvestmentSettlementDetails.accountOpeningLocation'),
        t('fields.reportPublicInvestmentSettlementDetails.investmentProjectCode'),
        t('fields.reportPublicInvestmentSettlementDetails.totalInvestment'),
        'Lũy kế vốn đã giải ngân từ K/c đến hết năm NS trước năm QT',
        '',
        t('fields.reportPublicInvestmentSettlementDetails.adjustedRecoveredAdvance'),
        t('fields.reportPublicInvestmentSettlementDetails.settledWorkloadPayment'),
        'Kế hoạch và giải ngân vốn kế hoạch các năm trước được kéo dài thời gian thực hiện và giải ngân sang năm QT',
        '',
        '',
        '',
        '',
        '',
        'Kế hoạch và giải ngân vốn kế hoạch năm QT',
        '',
        '',
        '',
        '',
        '',
        t('fields.reportPublicInvestmentSettlementDetails.totalSettledFunding'),
        t('fields.reportPublicInvestmentSettlementDetails.cumulativeUnrecoveredAdvance'),
        t('fields.reportPublicInvestmentSettlementDetails.cumulativeDisbursedFunding'),
      ];
      const headerRow2 = [
        '',
        '',
        '',
        '',
        '',
        t('fields.reportPublicInvestmentSettlementDetails.totalFunding'),
        t('fields.reportPublicInvestmentSettlementDetails.advanceFundsUnrecovered'),
        '',
        '',
        t('fields.reportPublicInvestmentSettlementDetails.extendedFundingPlanBeforeTheSettlement'),
        'Giải ngân',
        '',
        '',
        t(
          'fields.reportPublicInvestmentSettlementDetails.furtherExtendedFundingPlanBeforeTheSettlement'
        ),
        t('fields.reportPublicInvestmentSettlementDetails.unusedFundsCancellation'),
        t('fields.reportPublicInvestmentSettlementDetails.fundingPlan'),
        'Giải ngân',
        '',
        '',
        t('fields.reportPublicInvestmentSettlementDetails.furtherExtendedFundingPlanCurrentYear'),
        t('fields.reportPublicInvestmentSettlementDetails.currentYearUnusedFundsCancellation'),
      ];
      const headerRow3 = [
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        t(
          'fields.reportPublicInvestmentSettlementDetails.totalExtendedFundsDisbursementBeforeTheSettlement'
        ),
        t(
          'fields.reportPublicInvestmentSettlementDetails.extendedFundingPaymentDisbursementBeforeTheSettlement'
        ),
        t(
          'fields.reportPublicInvestmentSettlementDetails.extendedAdvanceFundsDisbursementBeforeTheSettlement'
        ),
        '',
        '',
        '',
        t(
          'fields.reportPublicInvestmentSettlementDetails.totalExtendedFundsDisbursementCurrentYear'
        ),
        t(
          'fields.reportPublicInvestmentSettlementDetails.extendedFundingPaymentDisbursementCurrentYear'
        ),
        t(
          'fields.reportPublicInvestmentSettlementDetails.extendedAdvanceFundsDisburementCurrentYear'
        ),
      ];
      const headerRow4 = [
        '1', // STT
        '2', // Tên công trình
        '3', // Địa điểm mở tài khoản
        '4', // Mã dự án đầu tư
        '5', // Tổng mức đầu tư
        '6', // Lũy kế vốn đã giải ngân từ K/c đến hết năm NS trước năm QT - Tổng số
        '7', // Số vốn tạm ứng theo chế độ chưa thu hồi của các năm trước nộp điều chỉnh giảm trong năm QT
        '8', // Thanh toán KLHT trong năm QT phần vốn tạm ứng theo chế độ chưa thu hồi từ K/C đến hết năm NS trước năm QT
        '9', // Kế hoạch và giải ngân vốn kế hoạch các năm trước được kéo dài thời gian thực hiện và giải ngân sang năm QT - KH vốn được kéo dài
        '10', // Giải ngân - Tổng số
        '11=12+13', // Giải ngân - Tổng số = Thanh toán KLHT + Vốn tạm ứng
        '12', // Thanh toán KLHT
        '13', // Vốn tạm ứng
        '14', // Vốn KH tiếp tục được phép kéo dài thời gian thực hiện và giải ngân sang năm sau năm QT (nếu có)
        '15=10-11-14', // Số vốn còn lại chưa giải ngân hủy bỏ (nếu có)
        '16', // Vốn KH năm 2023
        '17=18+19', // Giải ngân - Tổng số = Thanh toán KLHT + Vốn tạm ứng
        '18', // Thanh toán KLHT
        '19', // Vốn tạm ứng
        '20', // Vốn KH được phép kéo dài thời gian thực hiện và giải ngân sang năm sau năm QT (nếu có)
        '21=16-17-20', // Số vốn còn lại chưa giải ngân hủy bỏ (nếu có)
        '22=9+12+18', // Tổng cộng vốn đã thanh toán KLHT được QT trong năm QT
        '23=7-8-9+13+19', // Lũy kế vốn tạm ứng theo chế độ chưa thu hồi đến hết năm QT chuyển sang các năm sau
        '24=6-8+11+17', // Lũy kế số vốn đã giải ngân từ KC đến hết năm QT
      ];

      const mergeCells = [
        'A1:A3', // STT
        'B1:B3', // Tên công trình
        'C1:C3', // Địa điểm mở tài khoản
        'D1:D3', // Mã dự án đầu tư
        'E1:E3', // Tổng mức đầu tư
        'F1:G1', // Lũy kế vốn đã giải ngân từ K/c đến hết năm NS trước năm QT
        'F2:F3', // Tổng số
        'G2:G3', // Tr: vốn tạm ứng theo chế độ chưa thu hồi

        'H1:H3', // Số vốn tạm ứng... điều chỉnh giảm trong năm QT
        'I1:I3', // Thanh toán KLHT... từ K/C đến hết năm NS trước năm QT
        'J1:O1', // Kế hoạch và giải ngân vốn kế hoạch các năm trước...
        'J2:J3', // KH vốn được kéo dài
        'K2:M2', // Giải ngân
        'K3:K3', // Tổng số
        'L3:L3', // Thanh toán KLHT
        'M3:M3', // Vốn tạm ứng
        'N2:N3', // Vốn KH tiếp tục được phép kéo dài...
        'O2:O3', // Số vốn còn lại chưa giải ngân hủy bỏ...
        'P1:U1', // Kế hoạch và giải ngân vốn kế hoạch năm QT
        'P2:P3', // Vốn KH năm 2023
        'Q2:S2', // Giải ngân
        'Q3:Q3', // Tổng số
        'R3:R3', // Thanh toán KLHT
        'S3:S3', // Vốn tạm ứng
        'T2:T3', // Vốn KH được phép kéo dài...
        'U2:U3', // Số vốn còn lại chưa giải ngân hủy bỏ...
        'V1:V3', // Tổng cộng vốn đã thanh toán KLHT...
        'W1:W3', // Lũy kế vốn tạm ứng...
        'X1:X3', // Lũy kế số vốn đã giải ngân...
      ];

      createTreeListExportingEventMultiHeader(
        `${exportFileName}.xlsx`,
        'Main',
        treeListInstance,
        renderColumnValues,
        [headerRow1, headerRow2, headerRow3, headerRow4],
        mergeCells
      );
    }
  };

  useEffect(() => {
    if (setRef) {
      setRef(ref.current);
    }
  }, [setRef]);

  const handleRowUpdating = (e: RowUpdatingEvent<unknown, unknown>) => {
    const updatingRow: ReportPublicInvestmentSettlementDetail = {
      ...editableData.find(item => item.ordinalNumber === e.key)!,
      ...e.newData,
    };

    //tính các giá trị theo công thức
    //11=12+13
    updatingRow.totalExtendedFundsDisbursementBeforeTheSettlement =
      (updatingRow.extendedFundingPaymentDisbursementBeforeTheSettlement || 0) +
      (updatingRow.extendedAdvanceFundsDisbursementBeforeTheSettlement || 0);
    //15=10-11-14
    updatingRow.unusedFundsCancellation =
      (updatingRow.extendedFundingPaymentDisbursementBeforeTheSettlement || 0) -
      (updatingRow.totalExtendedFundsDisbursementBeforeTheSettlement || 0) -
      (updatingRow.furtherExtendedFundingPlanBeforeTheSettlement || 0);
    //17 = 18 + 19
    updatingRow.totalExtendedFundsDisbursementCurrentYear =
      (updatingRow.extendedFundingPaymentDisbursementCurrentYear || 0) +
      (updatingRow.extendedAdvanceFundsDisburementCurrentYear || 0);
    //21 = 16 - 17 - 20
    updatingRow.currentYearUnusedFundsCancellation =
      (updatingRow.fundingPlan || 0) -
      (updatingRow.totalExtendedFundsDisbursementCurrentYear || 0) -
      (updatingRow.furtherExtendedFundingPlanCurrentYear || 0);
    //22 = 9 + 12 + 18
    updatingRow.totalSettledFunding =
      (updatingRow.settledWorkloadPayment || 0) +
      (updatingRow.extendedFundingPaymentDisbursementBeforeTheSettlement || 0) +
      (updatingRow.extendedFundingPaymentDisbursementCurrentYear || 0);
    //23 = 7 - 8 - 9 + 13 + 19
    updatingRow.cumulativeUnrecoveredAdvance =
      (updatingRow.advanceFundsUnrecovered || 0) -
      (updatingRow.adjustedRecoveredAdvance || 0) -
      (updatingRow.settledWorkloadPayment || 0) +
      (updatingRow.extendedAdvanceFundsDisbursementBeforeTheSettlement || 0) +
      (updatingRow.extendedAdvanceFundsDisburementCurrentYear || 0);
    //24 = 6 - 8 + 11 + 17
    updatingRow.cumulativeDisbursedFunding =
      (updatingRow.totalFunding || 0) -
      (updatingRow.adjustedRecoveredAdvance || 0) +
      (updatingRow.totalExtendedFundsDisbursementBeforeTheSettlement || 0) +
      (updatingRow.totalExtendedFundsDisbursementCurrentYear || 0);

    const updatingRows = editableData.map(item =>
      item.ordinalNumber === e.key ? updatingRow : item
    );

    const newList = calculateForm?.(updatingRows) || [];

    const instance = e.component;
    void instance.getDataSource().store().update(e.key, updatingRow);

    void Promise.all(
      newList.map(u => instance.getDataSource().store().update(u.ordinalNumber, u))
    ).then(() => {
      void instance.refresh();
      // .then(() => setValue('reportPublicInvestmentSettlementDetails', newList)); // Cập nhật giao diện sau khi update
    });

    // calculateForm?.(updatingRows);
  };

  return (
    <DevexTreeList
      ref={ref}
      id={TABLES.REPORT_PUBLIC_INVESTMENT_SETTLEMENT_DETAIL}
      dataSource={editableData}
      keyExpr="ordinalNumber"
      parentIdExpr={'parentId'}
      hoverStateEnabled
      focusedRowEnabled={true}
      rootValue={-1}
      editing={{
        allowAdding: false,
        allowUpdating: rowData => {
          const data = rowData.row?.data as ReportPublicInvestmentSettlementDetail;
          return !data.child?.length;
        },
        allowDeleting: false,
        mode: 'cell',
      }}
      onExporting={onExporting}
      onRowUpdating={handleRowUpdating}
      hideSerialNumber
      paging={{ enabled: false }}
      pager={{ visible: false }}
      onRowPrepared={e => {
        if (e.rowType === 'data') {
          const data = e.data as ReportPublicInvestmentSettlementDetail;
          if (!data?.projectId) {
            e.rowElement.style.fontWeight = 'bold';
          }
        }
      }}
      wordWrapEnabled={true}
    >
      <Column
        dataField="indentifierNumber"
        caption={t('fields.reportPublicInvestmentSettlementDetails.ordinalNumber')}
        allowEditing={false}
      />
      <Column
        dataField="projectName"
        caption={t('fields.reportPublicInvestmentSettlementDetails.projectId')}
        allowEditing={false}
        width={300}
      />
      <Column
        dataField="accountOpeningLocation"
        caption={t('fields.reportPublicInvestmentSettlementDetails.accountOpeningLocation')}
        allowEditing={false}
        width={300}
      />
      <Column
        dataField="investmentProjectCode"
        caption={t('fields.reportPublicInvestmentSettlementDetails.investmentProjectCode')}
        allowEditing={false}
        width={300}
      />
      <Column
        dataField="totalInvestment"
        caption={t('fields.reportPublicInvestmentSettlementDetails.totalInvestment')}
        dataType="number"
        allowEditing={false}
        customizeText={customizeNumberCell()}
        width={300}
      />
      <Column
        caption="Lũy kế vốn đã giải ngân từ K/c đến hết năm NS trước năm QT"
        alignment="center"
      >
        <Column
          dataField="totalFunding"
          caption={t('fields.reportPublicInvestmentSettlementDetails.totalFunding')}
          dataType="number"
          allowEditing={false}
          customizeText={customizeNumberCell()}
          width={300}
        />
        <Column
          dataField="advanceFundsUnrecovered"
          caption={t('fields.reportPublicInvestmentSettlementDetails.advanceFundsUnrecovered')}
          dataType="number"
          allowEditing={false}
          customizeText={customizeNumberCell()}
          width={300}
        />
      </Column>
      <Column
        dataField="adjustedRecoveredAdvance"
        caption={t('fields.reportPublicInvestmentSettlementDetails.adjustedRecoveredAdvance')}
        dataType="number"
        customizeText={customizeNumberCell()}
        width={300}
      />
      <Column
        dataField="settledWorkloadPayment"
        caption={t('fields.reportPublicInvestmentSettlementDetails.settledWorkloadPayment')}
        dataType="number"
        allowEditing={false}
        customizeText={customizeNumberCell()}
        width={300}
      />
      <Column caption="Kế hoạch và giải ngân vốn kế hoạch các năm trước được kéo dài thời gian thực hiện và giải ngân sang năm QT">
        <Column
          dataField="extendedFundingPlanBeforeTheSettlement"
          caption={t(
            'fields.reportPublicInvestmentSettlementDetails.extendedFundingPlanBeforeTheSettlement'
          )}
          dataType="number"
          customizeText={customizeNumberCell()}
          width={300}
        />
        <Column caption="Giải ngân" alignment="center">
          <Column
            dataField="totalExtendedFundsDisbursementBeforeTheSettlement"
            caption={t(
              'fields.reportPublicInvestmentSettlementDetails.totalExtendedFundsDisbursementBeforeTheSettlement'
            )}
            dataType="number"
            allowEditing={false}
            customizeText={customizeNumberCell()}
            width={300}
          />
          <Column
            dataField="extendedFundingPaymentDisbursementBeforeTheSettlement"
            caption={t(
              'fields.reportPublicInvestmentSettlementDetails.extendedFundingPaymentDisbursementBeforeTheSettlement'
            )}
            dataType="number"
            customizeText={customizeNumberCell()}
            width={300}
          />
          <Column
            dataField="extendedAdvanceFundsDisbursementBeforeTheSettlement"
            caption={t(
              'fields.reportPublicInvestmentSettlementDetails.extendedAdvanceFundsDisbursementBeforeTheSettlement'
            )}
            dataType="number"
            customizeText={customizeNumberCell()}
            width={300}
          />
        </Column>
        <Column
          dataField="furtherExtendedFundingPlanBeforeTheSettlement"
          caption={t(
            'fields.reportPublicInvestmentSettlementDetails.furtherExtendedFundingPlanBeforeTheSettlement'
          )}
          dataType="number"
          customizeText={customizeNumberCell()}
          width={300}
        />
        <Column
          dataField="unusedFundsCancellation"
          caption={t('fields.reportPublicInvestmentSettlementDetails.unusedFundsCancellation')}
          dataType="number"
          allowEditing={false}
          customizeText={customizeNumberCell()}
          width={300}
        />
      </Column>
      <Column caption="Kế hoạch và giải ngân vốn kế hoạch năm QT" alignment="center">
        <Column
          dataField="fundingPlan"
          caption={t('fields.reportPublicInvestmentSettlementDetails.fundingPlan')}
          dataType="number"
          allowEditing={false}
          customizeText={customizeNumberCell()}
          width={300}
        />
        <Column caption="Giải ngân" alignment="center">
          <Column
            dataField="totalExtendedFundsDisbursementCurrentYear"
            caption={t(
              'fields.reportPublicInvestmentSettlementDetails.totalExtendedFundsDisbursementCurrentYear'
            )}
            dataType="number"
            allowEditing={false}
            customizeText={customizeNumberCell()}
            width={300}
          />
          <Column
            dataField="extendedFundingPaymentDisbursementCurrentYear"
            caption={t(
              'fields.reportPublicInvestmentSettlementDetails.extendedFundingPaymentDisbursementCurrentYear'
            )}
            dataType="number"
            allowEditing={false}
            customizeText={customizeNumberCell()}
            width={300}
          />
          <Column
            dataField="extendedAdvanceFundsDisburementCurrentYear"
            caption={t(
              'fields.reportPublicInvestmentSettlementDetails.extendedAdvanceFundsDisburementCurrentYear'
            )}
            dataType="number"
            allowEditing={false}
            customizeText={customizeNumberCell()}
            width={300}
          />
        </Column>
        <Column
          dataField="furtherExtendedFundingPlanCurrentYear"
          caption={t(
            'fields.reportPublicInvestmentSettlementDetails.furtherExtendedFundingPlanCurrentYear'
          )}
          dataType="number"
          customizeText={customizeNumberCell()}
          width={300}
        />
        <Column
          dataField="currentYearUnusedFundsCancellation"
          caption={t(
            'fields.reportPublicInvestmentSettlementDetails.currentYearUnusedFundsCancellation'
          )}
          dataType="number"
          allowEditing={false}
          customizeText={customizeNumberCell()}
          width={300}
        />
      </Column>
      <Column
        dataField="totalSettledFunding"
        caption={t('fields.reportPublicInvestmentSettlementDetails.totalSettledFunding')}
        dataType="number"
        allowEditing={false}
        customizeText={customizeNumberCell()}
        width={300}
      />
      <Column
        dataField="cumulativeUnrecoveredAdvance"
        caption={t('fields.reportPublicInvestmentSettlementDetails.cumulativeUnrecoveredAdvance')}
        dataType="number"
        allowEditing={false}
        customizeText={customizeNumberCell()}
        width={300}
      />
      <Column
        dataField="cumulativeDisbursedFunding"
        caption={t('fields.reportPublicInvestmentSettlementDetails.cumulativeDisbursedFunding')}
        dataType="number"
        allowEditing={false}
        customizeText={customizeNumberCell()}
        width={300}
      />
      <Summary>
        {/* TotalItem: Hiển thị tổng cuối bảng */}
        <TotalItem
          column="totalInvestment"
          summaryType="sum"
          customizeText={customizeNumberCell()}
          displayFormat="{0}"
        />
        <TotalItem
          column="totalFunding"
          summaryType="sum"
          customizeText={customizeNumberCell()}
          displayFormat="{0}"
        />
        <TotalItem
          column="advanceFundsUnrecovered"
          summaryType="sum"
          customizeText={customizeNumberCell()}
          displayFormat="{0}"
        />
        <TotalItem
          column="adjustedRecoveredAdvance"
          summaryType="sum"
          customizeText={customizeNumberCell()}
          displayFormat="{0}"
        />
        <TotalItem
          column="settledWorkloadPayment"
          summaryType="sum"
          customizeText={customizeNumberCell()}
          displayFormat="{0}"
        />
        <TotalItem
          column="extendedFundingPlanBeforeTheSettlement"
          summaryType="sum"
          customizeText={customizeNumberCell()}
          displayFormat="{0}"
        />
        <TotalItem
          column="totalExtendedFundsDisbursementBeforeTheSettlement"
          summaryType="sum"
          customizeText={customizeNumberCell()}
          displayFormat="{0}"
        />
        <TotalItem
          column="extendedFundingPaymentDisbursementBeforeTheSettlement"
          summaryType="sum"
          customizeText={customizeNumberCell()}
          displayFormat="{0}"
        />
        <TotalItem
          column="extendedAdvanceFundsDisbursementBeforeTheSettlement"
          summaryType="sum"
          customizeText={customizeNumberCell()}
          displayFormat="{0}"
        />
        <TotalItem
          column="furtherExtendedFundingPlanBeforeTheSettlement"
          summaryType="sum"
          customizeText={customizeNumberCell()}
          displayFormat="{0}"
        />
        <TotalItem
          column="unusedFundsCancellation"
          summaryType="sum"
          customizeText={customizeNumberCell()}
          displayFormat="{0}"
        />
        <TotalItem
          column="fundingPlan"
          summaryType="sum"
          customizeText={customizeNumberCell()}
          displayFormat="{0}"
        />
        <TotalItem
          column="totalExtendedFundsDisbursementCurrentYear"
          summaryType="sum"
          customizeText={customizeNumberCell()}
          displayFormat="{0}"
        />
        <TotalItem
          column="extendedFundingPaymentDisbursementCurrentYear"
          summaryType="sum"
          customizeText={customizeNumberCell()}
          displayFormat="{0}"
        />
        <TotalItem
          column="extendedAdvanceFundsDisburementCurrentYear"
          summaryType="sum"
          customizeText={customizeNumberCell()}
          displayFormat="{0}"
        />
        <TotalItem
          column="furtherExtendedFundingPlanCurrentYear"
          summaryType="sum"
          customizeText={customizeNumberCell()}
          displayFormat="{0}"
        />
        <TotalItem
          column="currentYearUnusedFundsCancellation"
          summaryType="sum"
          customizeText={customizeNumberCell()}
          displayFormat="{0}"
        />
        <TotalItem
          column="totalSettledFunding"
          summaryType="sum"
          customizeText={customizeNumberCell()}
          displayFormat="{0}"
        />
        <TotalItem
          column="cumulativeUnrecoveredAdvance"
          summaryType="sum"
          customizeText={customizeNumberCell()}
          displayFormat="{0}"
        />
        <TotalItem
          column="cumulativeDisbursedFunding"
          summaryType="sum"
          customizeText={customizeNumberCell()}
          displayFormat="{0}"
        />
      </Summary>
    </DevexTreeList>
  );
};
