import { requiredTextWithNamespace } from '@/lib/i18nUtils';
import { z } from 'zod';

export const requireBankText = requiredTextWithNamespace('bank');

export const bankSchema = z.object({
  storeId: z.number().nullable(),
  branchId: z.number().nullable(),
  isActive: z.boolean().nullable().optional(),
  id: z.number(),
  code: z.string().nullable().optional(),
  name: z.string().nullable().optional(),
  note: z.string().nullable().optional(),
});

export type Bank = z.infer<typeof bankSchema>;

export const defaultValuesBank: Bank = {
  storeId: null, // Mã của hàng
  branchId: null, // Mã chi nhanh
  isActive: true, // Hoạt động
  id: 0, // Khóa chính
  code: '', // Mã ngân hàng
  name: '', // Tên ngân hàng
  note: '', // <PERSON><PERSON> chú,
};
