import { ResizableHandle, ResizablePanel, ResizablePanelGroup } from '@/components/ui/resizable';
import { useNav } from '@/hooks/use-nav';
import { NavProvider } from '@/provider';
import { PrivateRoute } from '@/router/private-route';
import { Footer } from './footer';
import { Header } from './header';
import { SideBar } from './sidebar';
import { LazyChatButton } from '@/components/chat-ai';

const LayoutContent = () => {
  const { expand, unExpand, resizableSize, setResizableSize, isSmall } = useNav();

  return (
    <ResizablePanelGroup direction="horizontal">
      <ResizablePanel
        collapsedSize={resizableSize}
        maxSize={17}
        defaultSize={resizableSize}
        minSize={isSmall ? 0 : 2}
        onResize={size => {
          Math.floor(size) > 5 ? expand() : unExpand();
          setResizableSize(size);
        }}
        collapsible
      >
        <aside className="xs:hidden z-10 shadow-sm">
          <SideBar />
        </aside>
      </ResizablePanel>
      <ResizableHandle className="border-none bg-transparent text-transparent" />
      <ResizablePanel>
        <aside className="w-full overflow-hidden bg-white" id="app-container">
          <Header />
          <main id="main-content" className="px-6 py-3">
            <PrivateRoute />
          </main>
        </aside>
      </ResizablePanel>
    </ResizablePanelGroup>
  );
};

const Layout = () => {
  return (
    <section className="h-screen">
      <div
        className="flex h-[calc(100vh)] w-full bg-slate-100"
        id="app-content"
        // 32.8px là height của cái băng rôn lincense
        // style={{ height: 'calc(100% - var(--toastify-toast-max-height) - 32.8px)' }}
      >
        <NavProvider>
          <LayoutContent />
        </NavProvider>
        <LazyChatButton />

        <div className="absolute bottom-0 w-full">
          <Footer />
        </div>
      </div>
    </section>
  );
};

export default Layout;
