import { requiredTextWithNamespace } from '@/lib/i18nUtils';
import { z } from 'zod';

export const requireDirectiveImplementationText =
  requiredTextWithNamespace('directiveImplementation');

export const directiveImplementationReportDetailSchema = z.object({
  ordinalNumber: z.number().nullable(),
  typeId: z.number().nullable(),
  typename: z.null().nullable(),
  name: z.string().nullable(),
  progress: z.string().nullable(),
});

export const directiveImplementationSchema = z.object({
  storeId: z.number().nullable(),
  branchId: z.number().nullable(),
  isActive: z.boolean().nullable().optional(),
  code: z.string().nullable().optional(),
  name: z.string().nullable().optional(),
  note: z.string().nullable().optional(),
  userCreatedId: z.number().nullable(),
  directiveImplementationReportTime: z.date().nullable(),
  fromDate: z.date().nullable(),
  toDate: z.date().nullable(),
  id: z.number(),
  content: z.string().nullable().optional(),
  formDocumentManagerId: z.number().nullable(),
  // directiveImplementationReportDetails: z
  //   .array(directiveImplementationReportDetailSchema)
  //   .nullable(),
});

export type DirectiveImplementation = z.infer<typeof directiveImplementationSchema>;
export type DirectiveImplementationReportDetail = z.infer<
  typeof directiveImplementationReportDetailSchema
>;

export const defaultValuesDirectiveImplementation: DirectiveImplementation = {
  storeId: null, // mã cửa hàng
  branchId: null, // mã chi nhánh
  isActive: true, // Hoạt động
  code: '', // Mã
  name: '', // Tên báo cáo
  note: '', // Ghi chú
  userCreatedId: null, // Người lập
  directiveImplementationReportTime: new Date(), // Ngày lập
  fromDate: new Date(), // Từ ngày
  toDate: new Date(), // Đến ngày
  id: 0, // Khóa chính
  content: '', // Nội dung
  formDocumentManagerId: null, // Biếu mẫu,
  // directiveImplementationReportDetails: [], // Chi tiết báo cáo
};
