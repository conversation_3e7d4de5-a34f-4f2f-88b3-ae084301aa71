import { BasicDialog } from '@/components/basic-dialog';
import { DataTable, DataTableRowActions, EditableInputCell } from '@/components/data-table';
import { ImportExcelConfigForm } from '@/components/import-excel-config-form';
import { PageLayout } from '@/components/page-layout';
import { FormField, FormLabel } from '@/components/ui/form';
import { InputNumber } from '@/components/ui/input';
import { downloadTemplateLabel, enterLabel, PROFESSIONS, selectLabel, TABLES } from '@/constant';
import { useBoolean } from '@/hooks';
import { getRandomNumber } from '@/lib/number';
import {
  defaultValuesProjectCostEstimationDevice,
  Project,
  ProjectCostEstimationDevice,
  ProjectTabChildrenProps,
} from '@/types';
import { CellContext, ColumnDef, Table } from '@tanstack/react-table';
import { Button, DateBox, TextArea, TextBox } from 'devextreme-react';
import { SyntheticEvent, useEffect, useMemo } from 'react';
import { useFormContext, useWatch } from 'react-hook-form';

const defaultRow = defaultValuesProjectCostEstimationDevice;

// VatTaxCell component

export const ProjectCostEstimationDeviceForm = ({
  role,
  loading,
  onBackToList,
  onCreateNew,
  onSubmit,
  t,
  isPmDirector,
}: ProjectTabChildrenProps) => {
  const { state: isImportFormOpen, toggle: toggleImportForm } = useBoolean(false);
  const { control, setValue, getValues } = useFormContext<Project>();
  const [editableData, projectId] = useWatch({
    control,
    name: ['projectCostEstimationDeviceDetails', 'id'],
  });

  useEffect(() => {
    const oldCostEstimationDeviceTotalPreTax = getValues('costEstimationDeviceTotalPreTax');
    const oldCostEstimationDeviceTotalVat = getValues('costEstimationDeviceTotalVat');
    const oldCostEstimationDeviceTotalPostTax = getValues('costEstimationDeviceTotalPostTax');

    const newCostEstimationDeviceTotalPreTax = editableData?.reduce(
      (sum, item) => sum + (item.preTaxValue || 0),
      0
    );
    const newCostEstimationDeviceTotalVat = editableData?.reduce(
      (sum, item) => sum + (item.vatTax || 0),
      0
    );
    const newCostEstimationDeviceTotalPostTax = editableData?.reduce(
      (sum, item) => sum + (item.postTaxValue || 0),
      0
    );

    if (
      oldCostEstimationDeviceTotalPreTax === newCostEstimationDeviceTotalPreTax &&
      oldCostEstimationDeviceTotalVat === newCostEstimationDeviceTotalVat &&
      oldCostEstimationDeviceTotalPostTax === newCostEstimationDeviceTotalPostTax
    ) {
      return;
    }

    setValue('costEstimationDeviceTotalPreTax', newCostEstimationDeviceTotalPreTax);
    setValue('costEstimationDeviceTotalVat', newCostEstimationDeviceTotalVat);
    setValue('costEstimationDeviceTotalPostTax', newCostEstimationDeviceTotalPostTax);
  }, [editableData, setValue, getValues]);

  const handleOnValueChange = useMemo(
    () => (props: CellContext<ProjectCostEstimationDevice, unknown>, value: string | number) => {
      const row = { ...props.row.original, [props.column.id]: Number(value) };

      const preTaxValue = (row.price || 0) * (row.quantity || 0);
      const vatTax = (preTaxValue * (row.vat || 0)) / 100;
      const postTaxValue = preTaxValue + vatTax;

      const newRow = {
        ...row,
        preTaxValue,
        vatTax,
        postTaxValue,
      };

      if (props) {
        props.table.options.meta?.updateRowValues(newRow, props.row.index);
      }
    },
    []
  );

  const columns: ColumnDef<ProjectCostEstimationDevice>[] = useMemo(
    () => [
      {
        id: 'deviceName',
        accessorKey: 'deviceName',
        header: t('fields.projectCostEstimationDeviceDetails.deviceName'),
        cell: props => <EditableInputCell {...props} readOnly={!isPmDirector} />,
      },
      {
        id: 'note',
        accessorKey: 'note',
        header: t('fields.projectCostEstimationDeviceDetails.note'),
        cell: (props: CellContext<ProjectCostEstimationDevice, unknown>) => (
          <TextArea
            value={props.getValue() as string}
            onValueChange={e => {
              props.table.options.meta?.updateData(props.row.index, props.column.id, e);
            }}
            className="text-area-editable-cell overflow-y-auto"
          />
        ),
      },
      {
        id: 'unitName',
        accessorKey: 'unitName',
        header: t('fields.projectCostEstimationDeviceDetails.unitName'),
        cell: props => <EditableInputCell {...props} readOnly={!isPmDirector} />,
      },

      {
        id: 'quantity',
        accessorKey: 'quantity',
        header: t('fields.projectCostEstimationDeviceDetails.quantity'),
        cell: props => (
          <EditableInputCell
            {...props}
            readOnly={!isPmDirector}
            type="number"
            hideDecimal
            onValueChange={value => handleOnValueChange(props, value)}
          />
        ),
      },

      {
        id: 'price',
        accessorKey: 'price',
        header: t('fields.projectCostEstimationDeviceDetails.price'),
        cell: props => (
          <EditableInputCell
            {...props}
            readOnly={!isPmDirector}
            type="number"
            hideDecimal
            onValueChange={value => handleOnValueChange(props, value)}
          />
        ),
      },

      {
        id: 'preTaxValue',
        accessorKey: 'preTaxValue',
        header: t('fields.projectCostEstimationDeviceDetails.preTaxValue'),
        cell: props => <EditableInputCell {...props} readOnly type="number" hideDecimal />,
      },

      {
        id: 'vat',
        accessorKey: 'vat',
        header: t('fields.projectCostEstimationDeviceDetails.vat'),
        cell: props => (
          <EditableInputCell
            {...props}
            readOnly={!isPmDirector}
            type="number"
            hideDecimal
            onValueChange={value => handleOnValueChange(props, value)}
          />
        ),
      },
      {
        id: 'vatTax',
        accessorKey: 'vatTax',
        header: t('fields.projectCostEstimationDeviceDetails.vatTax'),
        cell: props => <EditableInputCell {...props} readOnly type="number" hideDecimal />,
      },
      {
        id: 'postTaxValue',
        accessorKey: 'postTaxValue',
        header: t('fields.projectCostEstimationDeviceDetails.postTaxValue'),
        cell: props => <EditableInputCell {...props} type="number" readOnly isMoney />,
      },
      {
        id: 'removeRow',
        header: ' ',
        size: 10,
        cell: props => {
          return (
            <DataTableRowActions
              onDelete={() => {
                // updateSummary(
                //   props.row.original.costItemCostItemTypeId,
                //   null,
                //   props.table.options.data
                // );
                props.table.options.meta?.removeRowByIndex(props.row.index);
              }}
              canDelete={role?.isCreate || role?.isUpdate}
            />
          );
        },
      },
    ],
    [isPmDirector, role?.isCreate, role?.isUpdate, t, handleOnValueChange]
  );

  const columnsForImportConfig = columns.map(column => {
    return {
      field: column.id,
      header: column.header as string,
    };
  });

  const handleAddNewRow = (table: Table<ProjectCostEstimationDevice>) => {
    const newRow: ProjectCostEstimationDevice = {
      ...defaultRow,
      projectId,
      id: -getRandomNumber(),
    };
    table.options.meta?.addNewRow(newRow);
  };

  return (
    <PageLayout
      onSaveChange={e => {
        const target = e.event?.currentTarget as unknown as SyntheticEvent<HTMLElement>;
        onSubmit(target);
      }}
      canSaveChange={role?.isUpdate}
      isSaving={loading}
      onCancel={onBackToList}
      customElementLeft={
        <>
          <Button
            text={t('content.createNew', { ns: 'common' })}
            className="uppercase"
            stylingMode="outlined"
            type="default"
            icon="plus"
            onClick={onCreateNew}
          />
        </>
      }
      contentClassName="!h-[calc(100vh-220px)]"
    >
      <div className="grid grid-cols-1 gap-x-8 gap-y-4 xl:grid-cols-24">
        <div className="col-span-1 space-y-4 xl:col-span-8">
          <div className="flex items-center">
            <FormLabel
              className="hidden w-[60px] md:block"
              name="costEstimationDeviceDocumentCode"
              htmlFor="costEstimationDeviceDocumentCode"
            >
              {t('fields.documentCode')}
            </FormLabel>
            <FormField
              className="min-w-0 flex-1"
              name="costEstimationDeviceDocumentCode"
              label={t('fields.documentCode')}
            >
              <TextBox
                readOnly={!isPmDirector}
                placeholder={`${enterLabel} ${t('fields.documentCode')}`}
              />
            </FormField>
          </div>
          <div className="flex w-full items-center xl:w-2/3">
            <FormLabel
              className="hidden w-[60px] md:block"
              name="costEstimationDeviceSigningDate"
              htmlFor="costEstimationDeviceSigningDate"
            >
              {t('fields.signingDate')}
            </FormLabel>
            <FormField
              className="min-w-0 flex-1"
              name="costEstimationDeviceSigningDate"
              type="date"
              label={t('fields.signingDate')}
            >
              <DateBox
                placeholder={`${selectLabel} ${t('fields.signingDate')}`}
                readOnly={!isPmDirector}
                pickerType="calendar"
                focusStateEnabled={false}
              />
            </FormField>
          </div>
          <div className="flex w-full items-center xl:w-2/3">
            <FormLabel
              className="hidden w-[60px] md:block"
              name="costEstimationDeviceSigner"
              htmlFor="costEstimationDeviceSigner"
            >
              {t('fields.signerId')}
            </FormLabel>
            <FormField
              className="min-w-0 flex-1"
              name="costEstimationDeviceSigner"
              label={t('fields.signerId')}
            >
              <TextBox
                readOnly={!isPmDirector}
                placeholder={`${enterLabel} ${t('fields.signerId')}`}
              />
            </FormField>
          </div>
        </div>
        <div className="col-span-1 space-y-4 xl:col-span-8">
          <div className="flex items-center">
            <FormLabel
              className="hidden w-[60px] md:block xl:w-[90px]"
              name="costEstimationDeviceTotalPreTax"
              htmlFor="costEstimationDeviceTotalPreTax"
            >
              {t('fields.costEstimationDeviceTotalPreTax')}
            </FormLabel>
            <FormField
              className="min-w-0 flex-1"
              name="costEstimationDeviceTotalPreTax"
              label={t('fields.costEstimationDeviceTotalPreTax')}
            >
              <InputNumber
                readOnly
                placeholder={`${enterLabel} ${t('fields.costEstimationDeviceTotalPreTax')}`}
              />
            </FormField>
          </div>
          <div className="flex items-center">
            <FormLabel
              className="hidden w-[60px] md:block xl:w-[90px]"
              name="costEstimationDeviceTotalVat"
              htmlFor="costEstimationDeviceTotalVat"
            >
              {t('fields.costEstimationDeviceTotalVat')}
            </FormLabel>
            <FormField
              className="min-w-0 flex-1"
              name="costEstimationDeviceTotalVat"
              label={t('fields.costEstimationDeviceTotalVat')}
            >
              <InputNumber
                readOnly
                placeholder={`${enterLabel} ${t('fields.costEstimationDeviceTotalVat')}`}
              />
            </FormField>
          </div>
          <div className="flex items-center">
            <FormLabel
              className="hidden w-[60px] md:block xl:w-[90px]"
              name="costEstimationDeviceTotalPostTax"
              htmlFor="costEstimationDeviceTotalPostTax"
            >
              {t('fields.costEstimationDeviceTotalPostTax')}
            </FormLabel>
            <FormField
              className="min-w-0 flex-1"
              name="costEstimationDeviceTotalPostTax"
              label={t('fields.costEstimationDeviceTotalPostTax')}
            >
              <InputNumber
                readOnly
                placeholder={`${enterLabel} ${t('fields.costEstimationDeviceTotalPostTax')}`}
              />
            </FormField>
          </div>
        </div>
        <div className="col-span-1 xl:col-span-24">
          <DataTable
            role={role}
            editableData={editableData || []}
            tableId={TABLES.PREPARATION_DETAIL}
            syncQueryParams={false}
            initialState={{
              columnVisibility: {
                costItemId: true,
                symbol: true,
                percentageRate: true,
                calculationMethod: true,
                preTaxValue: true,
                vat: true,
                vatTax: true,
                postTaxValue: true,
              },
            }}
            setEditableData={editedData => {
              setValue('projectCostEstimationDeviceDetails', editedData);
            }}
            onAddButtonClick={isPmDirector ? handleAddNewRow : undefined}
            customToolbar={() => {
              return (
                <>
                  <Button
                    stylingMode="text"
                    icon="upload"
                    text="Import Excel"
                    type="default"
                    onClick={toggleImportForm}
                  />
                  <Button
                    stylingMode="text"
                    icon="download"
                    text={downloadTemplateLabel}
                    type="default"
                    onClick={() => {
                      window.open(`/templates/project/mau_import_du_toan_thiet_bi.xlsx`);
                    }}
                  />
                </>
              );
            }}
            columns={columns}
          />
        </div>
      </div>
      <BasicDialog
        open={isImportFormOpen}
        title="Import Excel"
        toggle={toggleImportForm}
        className="max-w-[100vw] md:max-w-[90vw]"
      >
        <ImportExcelConfigForm<ProjectCostEstimationDevice>
          onApply={data => {
            const existingData = getValues('projectCostEstimationDeviceDetails');
            const newData = [
              ...data.map(item => ({ ...item, projectId: projectId })),
              ...(existingData || []),
            ];
            setValue('projectCostEstimationDeviceDetails', newData);

            toggleImportForm();
          }}
          importModel="project"
          onClose={toggleImportForm}
          professionType={PROFESSIONS.PROJECT_COST_ESTIMATION_DEVICE}
          professionColumns={columnsForImportConfig}
          onImported={() => {}}
          additionalFormValues={[{ key: 'refId', value: projectId?.toString() || '' }]}
          path="import-list-cost-estimation-device-detail"
        />
      </BasicDialog>
    </PageLayout>
  );
};
