import { PROFESSIONS_PATH } from '@/constant';

export function getPathById(id: number, code?: string): string | null {
  const profession = Object.values(PROFESSIONS_PATH).find(item => item.ID === id);

  if (!profession) {
    return null;
  }

  // Nếu PATH là function và có code
  if (typeof profession.PATH === 'function') {
    if (code) {
      return profession.PATH(code);
    } else {
      // Trả về null hoặc thông báo lỗi nếu thiếu code
      console.warn(`PATH với ID ${id} cần tham số code`);
      return null;
    }
  }

  // Nếu PATH là string
  if (typeof profession.PATH === 'string') {
    return profession.PATH;
  }

  return null;
}
