import React, { Suspense, useState, useEffect, memo } from 'react';
import { IUserPermission } from '@/types';
import { Spinner } from '@/components/ui/spinner';

// Lazy load the table component
const WeeklyProjectSchedulePlanEditableTableLazy = React.lazy(() =>
  import('./weekly-project-schedule-plan-editable-table').then(module => ({
    default: module.WeeklyProjectSchedulePlanEditableTable,
  }))
);

type LazyLoadedTableProps = {
  role?: IUserPermission;
  calculateForm?: () => void;
};

export const LazyLoadedTable: React.FC<LazyLoadedTableProps> = memo(
  ({ role, calculateForm }) => {
    const [isVisible, setIsVisible] = useState(false);

    // Delay loading the component until after initial render
    useEffect(() => {
      const timer = setTimeout(() => {
        setIsVisible(true);
      }, 100); // Small delay to allow the page to render first

      return () => clearTimeout(timer);
    }, []);

    if (!isVisible) {
      return (
        <div className="flex h-40 w-full items-center justify-center">
          <Spinner size="lg" />
          <span className="ml-2">Đang tải dữ liệu...</span>
        </div>
      );
    }

    return (
      <Suspense
        fallback={
          <div className="flex h-40 w-full items-center justify-center">
            <Spinner size="lg" />
            <span className="ml-2">Đang tải bảng dữ liệu...</span>
          </div>
        }
      >
        <WeeklyProjectSchedulePlanEditableTableLazy role={role} calculateForm={calculateForm} />
      </Suspense>
    );
  },
  (prevProps, nextProps) => {
    // Only re-render if role or calculateForm changes
    return prevProps.role === nextProps.role && prevProps.calculateForm === nextProps.calculateForm;
  }
);
