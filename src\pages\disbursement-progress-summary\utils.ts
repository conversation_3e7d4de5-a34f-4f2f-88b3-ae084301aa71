import axiosInstance, { request } from '@/axios-instance';
import {
  DisbursementProgressSummaryDetail,
  DisbursementProgressSummaryDetailByRoom,
} from '@/types';
import { useMutation } from '@tanstack/react-query';
import { useCallback } from 'react';
import { useTranslation } from 'react-i18next';

export function useTWithDefaultParams(namespace: string, defaultParams: Record<string, any>) {
  const { t } = useTranslation(namespace);

  const tWithDefaults = useCallback(
    (key: string, options?: Record<string, any>): string => {
      const result = t(key, { ...defaultParams, ...options });
      return result as string;
    },
    [defaultParams, t]
  );

  return { t: tWithDefaults };
}

type DisbursementProgressSummaryReport = {
  listDisbursementProgressSummaryReportDto: DisbursementProgressSummaryDetail[];
  listDisbursementProgressSummaryByRoomReportDto: DisbursementProgressSummaryDetailByRoom[];
};

export const GetDisbursementProgressSummaryReport = ({
  onSuccess,
}: {
  onSuccess: (data: DisbursementProgressSummaryReport) => void;
}) => {
  const { mutate: getReport, isPending: isLoading } = useMutation({
    mutationKey: ['DISBURSEMENT_PROGRESS_SUMMARY_GET_REPORT'],
    mutationFn: ({
      year,
      disbursementProgressSummaryId,
    }: {
      year: Date;
      disbursementProgressSummaryId: number;
    }) => {
      return request<DisbursementProgressSummaryReport>(
        axiosInstance.post('/disbursement-progress-summary/get-report', {
          filterColumn: [],
          pageIndex: 1,
          pageSize: -1,
          sortColumn: 'Id',
          sortOrder: 0,
          isPage: false,
          objParam: {
            year: year.toISOString(),
            disbursementProgressSummaryId: disbursementProgressSummaryId,
          },
        })
      );
    },
    onSuccess: onSuccess,
  });
  return { getReport, isLoading };
};
