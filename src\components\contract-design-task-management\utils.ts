import { toLocaleDate } from '@/lib/date';
import { ContractTaskManagement } from '@/types';

export const getOpinionDate = <T extends { isApprove?: boolean | null; opinionTime?: Date | null }>(
  item: T
) => {
  return item.isApprove !== null
    ? toLocaleDate(item.opinionTime || null)
    : toLocaleDate(new Date());
};

export const getApproveDate = <T extends { isApprove?: boolean | null; approveTime?: Date | null }>(
  item: T
) => {
  return item.isApprove !== null
    ? toLocaleDate(item.approveTime || null)
    : toLocaleDate(new Date());
};

export const getFormattedDate = (date?: Date | null): string => {
  if (!date) return '';
  return date?.toLocaleTimeString() + ' ' + date?.toLocaleDateString();
};

export const getFormattedDateWithDash = (date?: Date | null): string => {
  const formattedDate = getFormattedDate(date);
  if (!formattedDate) return '';
  return formattedDate + ' - ';
};

export const getApproveTimeText = (date?: Date | null, name?: string | null) => {
  if (name && date) return `${name} - ${getFormattedDate(date)}`;
  if (name) return name;
  if (date) return getFormattedDate(date);
  return '';
};

export const getOpinionTimeText = (date?: Date | null, time?: number) => {
  const content = `Nội dung thay đổi lần: ${time}`;
  const formattedDate = getFormattedDate(date);
  if (!formattedDate) return content;
  return `${formattedDate} - ${content}`;
};

export const getNotifyContent = ({
  projectName,
  contractNumber,
  contractName,
  opinion,
}: {
  projectName: string | null | undefined;
  contractNumber: string | null | undefined;
  contractName: string | null | undefined;
  opinion: string | null | undefined;
}) => {
  return `Dự án: ${projectName || ''}
Hợp đồng: ${contractNumber || ''}-${contractName || ''}
Ý kiến: ${opinion || ''}
    `;
};

export type ContractTaskManagementResponse = ContractTaskManagement & {
  departmentProjectHeadId: number;
  departmentFinanceHeadId: number;
  departmentPlanningHeadId: number;
};
