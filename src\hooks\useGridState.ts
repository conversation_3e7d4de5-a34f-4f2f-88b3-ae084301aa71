import { useState, useEffect } from 'react';
import { getData } from '@/lib/indexedDB';

// Đ<PERSON>nh nghĩa một kiểu cho tham số options để code rõ ràng hơn
type GridStateOptions = {
  selectedRowKeys?: any[];
};

/**
 * Custom Hook để quản lý việc tải trạng thái của DevExtreme DataGrid từ IndexedDB.
 * @param storageKey - Khóa định danh cho trạng thái của grid trong IndexedDB.
 * @returns Một object chứa hàm `loadState` và trạng thái `isLoadingState`.
 */
export function useGridState(storageKey: string, options?: GridStateOptions) {
  // 1. Đóng gói các state cần thiết vào bên trong hook
  const { selectedRowKeys } = options || {}; // Lấy selectedRowKeys từ options
  const [initialState, setInitialState] = useState<any>(null);
  const [isLoadingState, setIsLoadingState] = useState(true);

  // 2. Đưa logic trong useEffect vào hook
  useEffect(() => {
    // Chỉ chạy khi storageKey có giá trị
    if (!storageKey) {
      setIsLoadingState(false);
      setInitialState({});
      return;
    }

    const loadStateFromDb = async () => {
      try {
        const state = await getData<any>(storageKey);
        if (state) {
          // Gắn lại selectedRowKeys nếu nó được truyền vào
          if (selectedRowKeys) {
            state.selectedRowKeys = selectedRowKeys;
          }

          setInitialState(state);
        } else {
          // Quan trọng: Nếu không có state, trả về object rỗng để grid không bị lỗi
          setInitialState({});
        }
      } catch (error) {
        console.error(`Thất bại tải grid state cho key "${storageKey}" từ IndexedDB:`, error);
        setInitialState({}); // Đặt state rỗng khi có lỗi
      } finally {
        setIsLoadingState(false);
      }
    };

    void loadStateFromDb();
  }, [storageKey, selectedRowKeys]); // Hook sẽ chạy lại nếu storageKey thay đổi

  // 3. Trả về những gì component cần sử dụng
  const loadState = (): any => {
    return initialState;
  };

  return { loadState, isLoadingState };
}
