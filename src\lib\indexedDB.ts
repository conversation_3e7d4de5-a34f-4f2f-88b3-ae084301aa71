import { DB_VERSION, INDEXEDDB_DB_NAME, STORE_NAME } from '@/constant';

let dbPromise: Promise<IDBDatabase> | null = null;

/**
 * Khởi tạo và mở kết nối tới IndexedDB.
 * Việc tạo Object Store chỉ xảy ra trong sự kiện `onupgradeneeded`.
 */
export function initDB(): Promise<IDBDatabase> {
  return new Promise((resolve, reject) => {
    const request = indexedDB.open(INDEXEDDB_DB_NAME, DB_VERSION);

    request.onupgradeneeded = event => {
      const db = (event.target as IDBOpenDBRequest).result;
      if (!db.objectStoreNames.contains(STORE_NAME)) {
        db.createObjectStore(STORE_NAME);
      }
    };

    request.onsuccess = event => {
      resolve((event.target as IDBOpenDBRequest).result);
    };

    request.onerror = event => {
      console.error('IndexedDB error:', (event.target as IDBOpenDBRequest).error);
      reject('Error opening IndexedDB');
    };
  });
}

/**
 * <PERSON><PERSON><PERSON> về instance của DB, khởi tạo nếu cần.
 * <PERSON><PERSON><PERSON> là hàm "singleton" đảm bảo chỉ có một promise kết nối.
 */
export function getDbPromise(): Promise<IDBDatabase> {
  if (!dbPromise) {
    dbPromise = initDB();
  }
  return dbPromise;
}

/**
 * Lưu dữ liệu (trạng thái grid) vào IndexedDB.
 * @param key - Khóa để định danh (ví dụ: storageKey của grid).
 * @param value - Dữ liệu cần lưu.
 */
export async function saveData(key: string, value: any): Promise<void> {
  const db = await getDbPromise();
  return new Promise((resolve, reject) => {
    const transaction = db.transaction(STORE_NAME, 'readwrite');
    const store = transaction.objectStore(STORE_NAME);
    store.put(value, key);

    transaction.oncomplete = () => {
      resolve();
    };

    transaction.onerror = () => {
      console.error('Error saving data to IndexedDB:', transaction.error);
      reject(transaction.error);
    };
  });
}

/**
 * Lấy dữ liệu (trạng thái grid) từ IndexedDB.
 * @param key - Khóa để định danh (ví dụ: storageKey của grid).
 * @returns Dữ liệu đã lưu hoặc undefined nếu không tìm thấy.
 */
export async function getData<T>(key: string): Promise<T | undefined> {
  const db = await getDbPromise();
  return new Promise((resolve, reject) => {
    const transaction = db.transaction(STORE_NAME, 'readonly');
    const store = transaction.objectStore(STORE_NAME);
    const request = store.get(key);

    request.onsuccess = () => {
      resolve(request.result as T);
    };

    request.onerror = () => {
      console.error('Error getting data from IndexedDB:', request.error);
      reject(request.error);
    };
  });
}

/**
 * Khởi động kết nối IndexedDB
 */
export async function warmupConnection(): Promise<void> {
  await getDbPromise();
}
