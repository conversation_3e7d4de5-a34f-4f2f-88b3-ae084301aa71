/**
 * T<PERSON>h tổng giá trị của một thuộc tính (key) được chỉ định trong một mảng các đối tượng.
 * Hàm sẽ chỉ tính tổng các giá trị là số hợp lệ, bỏ qua các giá trị không phải số (null, undefined, string, NaN,...).
 *
 * @param {Array<Object>} array Mảng các đối tượng cần tính tổng.
 * @param {string} propertyKey Tên thuộc tính (key) có giá trị cần tính tổng.
 * @returns {number} Tổng giá trị của thuộc tính được chỉ định. Trả về 0 nếu mảng rỗng hoặc có lỗi đầu vào.
 */
export function sumProperty(array: Array<object>, propertyKey: string): number {
  // --- <PERSON>ể<PERSON> tra đầu vào ---
  if (!Array.isArray(array)) {
    console.error('Lỗi: Đầu vào đầu tiên phải là một mảng.');
    return 0;
  }
  if (!propertyKey || typeof propertyKey !== 'string') {
    console.error(
      'Lỗi: Đầu vào thứ hai (propertyKey) phải là một chuỗi tên thuộc tính không rỗng.'
    );
    return 0;
  }
  if (array.length === 0) {
    return 0; // Trả về 0 nếu mảng rỗng
  }

  // --- Sử dụng reduce để tính tổng ---
  const totalSum = array.reduce((sum, currentItem) => {
    // Kiểm tra xem đối tượng hiện tại có phải là object không (để tránh lỗi khi truy cập key)
    if (
      currentItem &&
      typeof currentItem === 'object' &&
      Object.prototype.hasOwnProperty.call(currentItem, propertyKey)
    ) {
      const value = (currentItem as Record<string, any>)[propertyKey];

      // Chỉ cộng vào tổng nếu giá trị là một số hợp lệ (không phải NaN)
      if (typeof value === 'number' && !isNaN(value)) {
        return sum + value;
      }
      // Optional: Log nếu giá trị bị bỏ qua không phải là null/undefined
      // else if (value !== null && value !== undefined) {
      //   console.warn(`Giá trị không hợp lệ tại key "${propertyKey}" bị bỏ qua:`, value);
      // }
    }
    // Nếu không phải số hợp lệ hoặc key không tồn tại, giữ nguyên tổng hiện tại
    return sum;
  }, 0); // Giá trị khởi tạo cho tổng (sum) là 0

  return totalSum;
}
