// import { But<PERSON> } from '@/components/ui/button';
import { DialogFooter } from '@/components/ui/dialog';
import { Form, FormCombobox, FormField, FormLabel } from '@/components/ui/form';
import {
  COMPONENT,
  MUTATE,
  QUERIES,
  closeLabel,
  enterLabel,
  save<PERSON>abel,
  select<PERSON>abel,
} from '@/constant';
import { useForm<PERSON>and<PERSON> } from '@/hooks';
import { useEntity } from '@/hooks/use-entity';
import { createMutationSuccessFn } from '@/lib/i18nUtils';
import { createPostMutateFn, createPutMutateFn, createQueryByIdFn } from '@/services';
import { FormInsideModalProps } from '@/types';
import { BoardOfDirector, boardOfDirectorSchema } from '@/types/board-of-director';
import { zodResolver } from '@hookform/resolvers/zod';
import { useQueryClient } from '@tanstack/react-query';
import { But<PERSON>, TextBox } from 'devextreme-react';
import { useTranslation } from 'react-i18next';

// id
// code
// name
// note
// is_active
const defaultBoardOfDirectorValues = {
  id: 0,
  code: '',
  name: '',
  note: '',
  isActive: true,
  storeId: null,
  branchId: null,
  departmentHeadId: 0,
  departmentDeputyId: null,
  departmentDeputyTwoId: null,
  departmentDeputyThreeId: null,
  consultingContractOfficerId: null,
};

const onMutationSuccess = createMutationSuccessFn('boardOfDirector');

export const BoardOfDirectorForm = ({
  role,
  editId,
  toggle,
}: FormInsideModalProps<BoardOfDirector>) => {
  const { t } = useTranslation('boardOfDirector');
  const toggleForm = () => {
    if (toggle) toggle();
  };

  const { mutateQueryItem } = useEntity<BoardOfDirector>({
    queryKey: [QUERIES.BOARD_OF_DIRECTOR],
    model: 'board-of-director',
  });

  const queryClient = useQueryClient();

  const { handleSubmit, loading, methods } = useFormHandler<BoardOfDirector>({
    queryKey: [MUTATE.BOARD_OF_DIRECTOR, editId],
    mutateKey: [MUTATE.BOARD_OF_DIRECTOR],
    invalidateKey: [QUERIES.BOARD_OF_DIRECTOR],
    queryId: editId,
    readFn: createQueryByIdFn<BoardOfDirector>('board-of-director'),
    createFn: createPostMutateFn<BoardOfDirector>('board-of-director'),
    updateFn: createPutMutateFn<BoardOfDirector>('board-of-director'),
    formatPayloadFn: values => {
      return values;
    },
    formatResponseFn: response => {
      mutateQueryItem(response);
      return response;
    },
    onCreateSuccess: (data, variables) => {
      mutateQueryItem({ ...variables, id: data });
      onMutationSuccess(data);
      void queryClient.invalidateQueries({ queryKey: [QUERIES.BOARD_OF_DIRECTOR, COMPONENT] });
      toggleForm();
    },
    onUpdateSuccess: data => {
      onMutationSuccess(data);
      void queryClient.invalidateQueries({ queryKey: [QUERIES.BOARD_OF_DIRECTOR, COMPONENT] });
      toggleForm();
    },
    formOptions: {
      resolver: zodResolver(boardOfDirectorSchema),
      defaultValues: defaultBoardOfDirectorValues,
    },
  });

  return (
    <Form {...methods}>
      <form className="p-1" autoComplete="off" onSubmit={handleSubmit}>
        <div className="space-y-4">
          {/* code */}
          <div className="flex items-center">
            <FormLabel name="code" className="hidden w-[118px] md:block">
              {t('fields.code')}
            </FormLabel>
            <FormField
              isRequired
              label={t('fields.code')}
              name="code"
              className="min-w-0 flex-1 md:max-w-[250px]"
            >
              <TextBox placeholder={`${enterLabel} ${t('fields.code')}`} />
            </FormField>
          </div>

          {/* name */}
          <div className="flex items-center">
            <FormLabel name="name" className="hidden w-[118px] md:block">
              {t('fields.name')}
            </FormLabel>
            <FormField
              isRequired
              label={t('fields.name')}
              name="name"
              className="min-w-0 flex-1 md:w-[500px]"
            >
              <TextBox placeholder={`${enterLabel} ${t('fields.name')}`} />
            </FormField>
          </div>

          {/* Giám đốc */}
          <div className="flex items-center">
            <FormLabel name="departmentHeadId" className="hidden w-[118px] md:block">
              {t('fields.departmentHeadId')}
            </FormLabel>
            <FormField
              isRequired
              label={t('fields.departmentHeadId')}
              name="departmentHeadId"
              className="min-w-0 flex-1 md:w-[500px]"
            >
              <FormCombobox
                queryKey={[QUERIES.USERS]}
                model="user"
                placeholder={`${selectLabel} ${t('fields.departmentHeadId')}`}
                defaultText={methods.getValues('departmentHeadName')}
              />
            </FormField>
          </div>
          {/* Phó giám đốc 1*/}
          <div className="flex items-center">
            <FormLabel name="departmentDeputyId" className="hidden w-[118px] md:block">
              {t('fields.departmentDeputyId')}
            </FormLabel>
            <FormField
              label={t('fields.departmentDeputyId')}
              name="departmentDeputyId"
              className="min-w-0 flex-1 md:w-[500px]"
            >
              <FormCombobox
                queryKey={[QUERIES.USERS]}
                model="user"
                placeholder={`${selectLabel} ${t('fields.departmentDeputyId')}`}
                defaultText={methods.getValues('departmentDeputyName')}
              />
            </FormField>
          </div>
          {/* Phó giám đốc 2*/}
          <div className="flex items-center">
            <FormLabel name="departmentDeputyTwoId" className="hidden w-[118px] md:block">
              {t('fields.departmentDeputyTwoId')}
            </FormLabel>
            <FormField
              label={t('fields.departmentDeputyTwoId')}
              name="departmentDeputyTwoId"
              className="min-w-0 flex-1 md:w-[500px]"
            >
              <FormCombobox
                queryKey={[QUERIES.USERS]}
                model="user"
                placeholder={`${selectLabel} ${t('fields.departmentDeputyTwoId')}`}
                defaultText={methods.getValues('departmentDeputyName')}
              />
            </FormField>
          </div>
          {/* Phó giám đốc 3*/}
          <div className="flex items-center">
            <FormLabel name="departmentDeputyThreeId" className="hidden w-[118px] md:block">
              {t('fields.departmentDeputyThreeId')}
            </FormLabel>
            <FormField
              label={t('fields.departmentDeputyThreeId')}
              name="departmentDeputyThreeId"
              className="min-w-0 flex-1 md:w-[500px]"
            >
              <FormCombobox
                queryKey={[QUERIES.USERS]}
                model="user"
                placeholder={`${selectLabel} ${t('fields.departmentDeputyThreeId')}`}
                defaultText={methods.getValues('departmentDeputyName')}
              />
            </FormField>
          </div>
          {/* note */}
          <div className="flex items-center">
            <FormLabel name="note" className="hidden w-[118px] md:block">
              {t('fields.note')}
            </FormLabel>
            <FormField label={t('fields.note')} name="note" className="min-w-0 flex-1 md:w-[500px]">
              <TextBox placeholder={`${enterLabel} ${t('fields.note')}`} />
            </FormField>
          </div>
        </div>

        <DialogFooter className="mt-8 flex flex-row-reverse gap-x-2 bg-white py-1">
          <Button
            stylingMode="contained"
            text={saveLabel}
            icon="save"
            type="success"
            disabled={loading || (editId ? !role?.isUpdate : !role?.isCreate)}
            useSubmitBehavior
          />
          <Button
            stylingMode="outlined"
            text={closeLabel}
            icon="close"
            disabled={loading || (editId ? !role?.isUpdate : !role?.isCreate)}
            onClick={toggleForm}
            type="default"
          />
        </DialogFooter>
      </form>
    </Form>
  );
};
