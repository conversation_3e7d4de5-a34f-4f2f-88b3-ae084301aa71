import { ErrorMessage } from '@/components/ui/error-message';
import { getDataLabel, QUERIES, TABLES } from '@/constant';
import {
  defaultValuesReportAnnex3a,
  GetReportAnnex3aPaymentReceipt,
  IUserPermission,
  ReportAnnex3a,
  ReportAnnex3aDetail,
  ReportAnnex3aOverview,
} from '@/types';
import { useFormContext, useWatch } from 'react-hook-form';

import { CellContext, ColumnDef } from '@tanstack/react-table';

import {
  DataTable,
  DataTableRowActions,
  EditableDropdownCell,
  EditableInputCell,
} from '@/components/data-table';
import { translationWithNamespace } from '@/lib/i18nUtils';
import notification from '@/lib/notifications';
import { convertMoneny, getRandomNumber } from '@/lib/number';
import { getSumsFromArray } from '@/lib/utils';
import { getRequest, Model } from '@/services';
import { Button } from 'devextreme-react';
import { useCallback, useMemo } from 'react';

const [defaultRow] = defaultValuesReportAnnex3a.reportAnnex3aOverviews;

type ReportAnnex3aOverviewEditableTableProps = {
  role?: IUserPermission;
  calculateForm?: () => void;
  isForFinance?: boolean;
};

const t = translationWithNamespace('reportAnnex3A');

export const ReportAnnex3aOverviewEditableTable = ({
  role,
  calculateForm,
  isForFinance,
}: ReportAnnex3aOverviewEditableTableProps) => {
  // const isMobile = useMediaQuery('(max-width: 768px)');
  const {
    setValue,
    control,
    formState: { errors },
  } = useFormContext<ReportAnnex3a>();

  let model: Model = 'report-annex-3a';
  if (isForFinance) {
    model = 'report-annex-3a-finance';
  }

  const [editableData, reportAnnex3aDetails, acceptance] = useWatch({
    control,
    name: ['reportAnnex3aOverviews', 'reportAnnex3aDetails', 'completionAcceptanceId'],
  });

  const calculateRow = useCallback(
    (
      row: ReportAnnex3aOverview,
      column: string,
      value: number
    ): Record<string, string | number | null | undefined> => {
      const newRow = { ...row };
      let _contractValue = newRow.contractValue; /// 1.
      let _advancePaymentExecution = newRow.advancePaymentExecution; //// 3.
      let _cumulativePaymentForCompletedVolume = newRow.cumulativePaymentForCompletedVolume;
      const _advancePaymentRecovery = Number(newRow.advancePaymentRecovery);
      const _valuePaymentForCompletedVolume = Number(newRow.valuePaymentForCompletedVolume);
      if (column === 'contractValue') {
        _contractValue = value;
      }

      if (column === 'advancePaymentExecution') {
        _advancePaymentExecution = value;
        _cumulativePaymentForCompletedVolume =
          _advancePaymentExecution +
          Number(_advancePaymentRecovery) +
          Number(_valuePaymentForCompletedVolume);
      }

      return {
        ...newRow,
        contractValue: Number(_contractValue),
        advancePaymentExecution: Number(_advancePaymentExecution),
        cumulativePaymentForCompletedVolume: Number(_cumulativePaymentForCompletedVolume),
      };
    },
    []
  );
  const reportAnnex3aOverviewEditableColumns: ColumnDef<ReportAnnex3aOverview>[] = useMemo(
    () => [
      {
        id: 'contractorId', // Nhà thầu
        accessorKey: 'contractorId',
        header: t('fields.reportAnnex3aOverviews.contractorId'),
        cell: (props: CellContext<ReportAnnex3aOverview, unknown>) => (
          <EditableDropdownCell
            {...props}
            model="contractor"
            queryKey={[QUERIES.CONTRACTOR]}
            defaultText={props.row.original.contractorName}
          />
        ),
      },

      {
        id: 'contractValue', // 1. Giá trị hợp đồng (giá trị dự toán được duyệt trong trường hợp thực hiện không thông qua hợp đồng)
        accessorKey: 'contractValue',
        header: t('fields.reportAnnex3aOverviews.contractValue'),
        cell: props => <EditableInputCell {...props} type="number" readOnly={false} />,
      },

      {
        id: 'advancePaymentValueForThePeriod', // 2. Giá trị tạm ứng còn lại chưa thu hồi đến cuối kỳ trước
        accessorKey: 'advancePaymentValueForThePeriod',
        header: t('fields.reportAnnex3aOverviews.advancePaymentValueForThePeriod'),
        cell: props => (
          <EditableInputCell
            {...props}
            type="number"
            onValueChange={value => {
              const original = props.row.original;
              const row = {
                ...original,
                ...calculateRow(original, props.column.id, Number(value)),
              };
              props.table.options.meta?.updateRowValues(row, props.row.index);
            }}
          />
        ),
      },

      {
        id: 'advancePaymentExecution', // 3. Số tiền đã thanh toán khối lượng hoàn thành kỳ trước
        accessorKey: 'advancePaymentExecution',
        header: t('fields.reportAnnex3aOverviews.advancePaymentExecution'),
        cell: props => (
          <EditableInputCell
            {...props}
            type="number"
            onValueChange={value => {
              const original = props.row.original;
              const row = {
                ...original,
                ...calculateRow(original, props.column.id, Number(value)),
              };
              props.table.options.meta?.updateRowValues(row, props.row.index);
            }}
          />
        ),
      },

      {
        id: 'completedVolumeThisPeriod', // 4. Lũy kế giá trị khối lượng thực hiện đến cuối kỳ này
        accessorKey: 'completedVolumeThisPeriod',
        header: t('fields.reportAnnex3aOverviews.completedVolumeThisPeriod'),
        cell: props => <EditableInputCell {...props} type="number" readOnly={false} />,
      },

      {
        id: 'advancePaymentRecovery', // 5. Thanh toán để thu hồi tạm ứng
        accessorKey: 'advancePaymentRecovery',
        header: t('fields.reportAnnex3aOverviews.advancePaymentRecovery'),
        cell: props => (
          <EditableInputCell
            {...props}
            type="number"
            onValueChange={value => {
              const original = props.row.original;
              const row = {
                ...original,
                ...calculateRow(original, props.column.id, Number(value)),
              };
              props.table.options.meta?.updateRowValues(row, props.row.index);
            }}
          />
        ),
      },

      {
        id: 'valuePaymentForCompletedVolume', // 6. Giá trị đề nghị giải ngân kỳ này - Thanh toán khối lượng hoàn thành
        accessorKey: 'valuePaymentForCompletedVolume',
        header: t('fields.reportAnnex3aOverviews.valuePaymentForCompletedVolume'),
        cell: props => <EditableInputCell {...props} type="number" readOnly={false} />,
      },

      {
        id: 'valueAdvancePayment', // 6. Giá trị đề nghị giải ngân kỳ này - tạm ứng
        accessorKey: 'valueAdvancePayment',
        header: t('fields.reportAnnex3aOverviews.valueAdvancePayment'),
        cell: props => <EditableInputCell {...props} type="number" readOnly={true} />,
      },

      {
        id: 'cumulativePaymentForCompletedVolume', // 7. Luỹ kế giá trị giải ngân, trong đó - Thanh toán khối lượng hoàn thành
        accessorKey: 'cumulativePaymentForCompletedVolume',
        header: t('fields.reportAnnex3aOverviews.cumulativePaymentForCompletedVolume'),
        cell: props => <EditableInputCell {...props} type="number" readOnly={false} />,
      },

      {
        id: 'cumulativeAdvancePayment', // 7. Luỹ kế giá trị giải ngân, trong đó - tạm ứng
        accessorKey: 'cumulativeAdvancePayment',
        header: t('fields.reportAnnex3aOverviews.cumulativeAdvancePayment'),
        cell: props => <EditableInputCell {...props} type="number" readOnly={false} />,
      },
      {
        id: 'removeRow',
        header: '',
        size: 10,
        cell: props => {
          return (
            <DataTableRowActions
              onDelete={() => {
                props.table.options.meta?.removeRowByIndex(props.row.index);
              }}
              canDelete={role?.isCreate || role?.isUpdate}
            />
          );
        },
      },
    ],
    [calculateRow, role?.isCreate, role?.isUpdate]
  );
  // const { data: ReportAnnex3aPayment } = useQuery({
  //   queryKey: [QUERIES.REPORT_ANNEX_3A_PAYMENT_RECEIPT, acceptance],
  //   queryFn: () => {
  //     return getRequest<GetReportAnnex3aPaymentReceipt[]>(
  //       `/${model}/get-report-annex-3-a-payment-receipt/${Number(acceptance)}`
  //     );
  //   },
  //   enabled: !isNaN(Number(acceptance)),
  // });

  // useEffect(() => {
  //   if (!ReportAnnex3aPayment) return;
  //   /// chỗ này có sửa lại để cho phép nhập phiếu (đổi điều kiện từ phủ định)
  //   if (!editableData?.length) {
  //     const temEditableData = editableData.map(item => {
  //       const objectPayment = ReportAnnex3aPayment.filter(
  //         a => a.contractorId == item.contractorId
  //       )[0];

  //       const advancePaymentValueForThePeriod = Number.isNaN(objectPayment?.advance)
  //         ? 0
  //         : objectPayment?.advance;
  //       const advancePaymentExecution = Number.isNaN(objectPayment?.payment)
  //         ? 0
  //         : objectPayment?.payment;

  //       const _advancePaymentRecovery =
  //         (Number(advancePaymentValueForThePeriod) / Number(item.contractValue)) *
  //         Number(item.completedVolumeThisPeriod) *
  //         0.8; /// 5. Thanh toán để thu hồi tạm ứng

  //       // const [valueAdvancePayment] = getSumsFromArray(editableData, ['contractValue']);
  //       const _valuePaymentForCompletedVolume =
  //         Number(item.completedVolumeThisPeriod) * 0.95 - _advancePaymentRecovery; // 6. Giá trị đề nghị giải ngân kỳ này - Thanh toán khối lượng hoàn thành

  //       const _cumulativePaymentForCompletedVolume =
  //         Number(advancePaymentExecution) +
  //         _advancePaymentRecovery +
  //         _valuePaymentForCompletedVolume;
  //       const _cumulativeAdvancePayment =
  //         Number(advancePaymentValueForThePeriod) - _advancePaymentRecovery < 0
  //           ? 0
  //           : Number(advancePaymentValueForThePeriod) - _advancePaymentRecovery;
  //       return {
  //         ...item,
  //         id: -getRandomNumber(),
  //         advancePaymentValueForThePeriod: Number.isNaN(advancePaymentValueForThePeriod)
  //           ? 0
  //           : advancePaymentValueForThePeriod,
  //         advancePaymentExecution: Number.isNaN(advancePaymentExecution)
  //           ? 0
  //           : advancePaymentExecution,
  //         advancePaymentRecovery: Number.isNaN(_advancePaymentRecovery)
  //           ? 0
  //           : _advancePaymentRecovery,
  //         valuePaymentForCompletedVolume: Number.isNaN(_valuePaymentForCompletedVolume)
  //           ? 0
  //           : _valuePaymentForCompletedVolume,
  //         cumulativePaymentForCompletedVolume: Number.isNaN(_cumulativePaymentForCompletedVolume)
  //           ? 0
  //           : _cumulativePaymentForCompletedVolume,
  //         cumulativeAdvancePayment: Number.isNaN(_cumulativeAdvancePayment)
  //           ? 0
  //           : _cumulativeAdvancePayment,
  //       };
  //     });

  //     const [
  //       contractValue,
  //       advancePaymentValueForThePeriod,
  //       advancePaymentExecution,
  //       completedVolumeThisPeriod,
  //       advancePaymentRecovery,
  //       valuePaymentForCompletedVolume,
  //       valueAdvancePayment,
  //       cumulativePaymentForCompletedVolume,
  //       cumulativeAdvancePayment,
  //     ] = getSumsFromArray(temEditableData, [
  //       'contractValue',
  //       'advancePaymentValueForThePeriod',
  //       'advancePaymentExecution',
  //       'completedVolumeThisPeriod',
  //       'advancePaymentRecovery',
  //       'valuePaymentForCompletedVolume',
  //       'valueAdvancePayment',
  //       'cumulativePaymentForCompletedVolume',
  //       'cumulativeAdvancePayment',
  //     ]);

  //     setValue('reportAnnex3aOverviews', temEditableData);
  //     setValue('sumContractValue', contractValue);
  //     setValue('sumAdvancePaymentValueForThePeriod', advancePaymentValueForThePeriod);
  //     setValue('sumAdvancePaymentExecution', advancePaymentExecution);
  //     setValue('sumCompletedVolumeThisPeriod', completedVolumeThisPeriod);
  //     setValue('sumAdvancePaymentRecovery', advancePaymentRecovery);
  //     setValue('sumDisbursementRequestValue', valuePaymentForCompletedVolume + valueAdvancePayment);
  //     setValue(
  //       'sumCumulativeDisbursedValue',
  //       cumulativePaymentForCompletedVolume + cumulativeAdvancePayment
  //     );
  //   }
  // }, [ReportAnnex3aPayment]);
  return (
    <div>
      <DataTable
        tableId={TABLES.REPORT_ANNEX_3A_OVERVIEW}
        sortColumn="id"
        role={role}
        editableData={editableData}
        setEditableData={editedData => {
          setValue('reportAnnex3aOverviews', editedData);
          calculateForm?.();
        }}
        onAddButtonClick={table => {
          const newRow = { ...defaultRow, id: -getRandomNumber() };
          table.options.meta?.addNewRow(newRow);
        }}
        syncQueryParams={false}
        columns={reportAnnex3aOverviewEditableColumns}
        customToolbar={() => {
          return (
            <>
              <Button
                icon="search"
                type="default"
                onClick={() => {
                  if (!acceptance) {
                    notification.warning(t('page.notification.warning.completionAcceptanceId'));
                    return;
                  }
                  getRequest<GetReportAnnex3aPaymentReceipt[]>(
                    `/${model}/get-report-annex-3-a-payment-receipt/${Number(acceptance)}`
                  )
                    .then(response => {
                      if (!response) return;

                      // if (!editableData?.length) {
                      const details = reportAnnex3aDetails;
                      const groupedData = details.reduce<
                        Record<
                          string,
                          ReportAnnex3aDetail & Record<string, number | string | null | undefined>
                        >
                      >((acc, item) => {
                        const key = item.constructionTaskCode!;

                        if (!acc[key]) {
                          acc[key] = {
                            contractedOrEstimatedPaymentPrice: 0,
                            contractedorestimatedquantity: 0,
                            cumulativePreviousPeriod: 0,
                            currentPeriodExecution: 0,
                            contractedOrEstimatedQuantity: 0,
                            id: -getRandomNumber(),
                            reportAnnex3aId: 0,
                            note: '',
                            contractorId: item.contractorId,
                            constructionTaskId: item.constructionTaskId,
                            constructionTaskCode: item.constructionTaskCode,
                            constructionTaskName: item.constructionTaskName,
                            unitId: item.unitId,
                            unitName: item.unitName,
                          };
                        }

                        // Thêm các trường động theo contractorId
                        const contractorKey = item.contractorId;
                        acc[key][`contractedOrEstimatedPaymentPrice`] =
                          item.contractedOrEstimatedPaymentPrice;
                        acc[key][`contractedOrEstimatedQuantity_${contractorKey}`] =
                          item.contractedOrEstimatedQuantity;
                        acc[key][`currentPeriodExecution_${contractorKey}`] =
                          item.currentPeriodExecution;
                        acc[key][`cumulativePreviousPeriod_${contractorKey}`] =
                          item.cumulativePreviousPeriod;
                        acc[key][`accumulatedUpToTheEndOfThisPeriod_${contractorKey}`] =
                          Number(item.currentPeriodExecution) +
                          Number(item.cumulativePreviousPeriod);

                        acc[key][`contractedOrEstimatedQuantityTotalAmount_${contractorKey}`] =
                          Number(item.contractedOrEstimatedQuantity) *
                          Number(item.contractedOrEstimatedPaymentPrice);
                        acc[key][`currentPeriodExecutionTotalAmount_${contractorKey}`] =
                          Number(item.currentPeriodExecution) *
                          Number(item.contractedOrEstimatedPaymentPrice);
                        acc[key][`cumulativePreviousPeriodTotalAmount_${contractorKey}`] =
                          Number(item.cumulativePreviousPeriod) *
                          Number(item.contractedOrEstimatedPaymentPrice);
                        acc[key][`accumulatedUpToTheEndOfThisPeriodTotalAmount_${contractorKey}`] =
                          (Number(item.currentPeriodExecution) +
                            Number(item.cumulativePreviousPeriod)) *
                          Number(item.contractedOrEstimatedPaymentPrice);

                        return acc;
                      }, {});
                      /// chỗ này có sửa lại để cho phép nhập phiếu
                      ///start fix bug
                      const dataTem = Object.values(groupedData)?.flatMap(item => {
                        return Object.keys(item)
                          .filter(key => key.startsWith('contractedOrEstimatedQuantity_'))
                          .map(key => {
                            const contractorId = key.split('_')[1]; // Lấy ID từ key
                            return {
                              unitId: item.unitId,
                              unitName: item.unitName,
                              contractorId: parseInt(contractorId), // Chuyển về số
                              contractedOrEstimatedPaymentPrice:
                                item[`contractedOrEstimatedPaymentPrice`],
                              contractedOrEstimatedQuantity: Number(
                                item[`contractedOrEstimatedQuantity_${contractorId}`]
                              ),
                              currentPeriodExecution: Number(
                                item[`currentPeriodExecution_${contractorId}`]
                              ),
                              cumulativePreviousPeriod:
                                item[`cumulativePreviousPeriod_${contractorId}`],
                              contractedOrEstimatedQuantityTotalAmount: Number(
                                item[`contractedOrEstimatedQuantityTotalAmount_${contractorId}`]
                              ),
                              currentPeriodExecutionTotalAmount: Number(
                                Number(item[`contractedOrEstimatedPaymentPrice`]) *
                                  Number(item[`currentPeriodExecution_${contractorId}`])
                              ),
                              cumulativePreviousPeriodTotalAmount: Number(
                                item[`cumulativePreviousPeriodTotalAmount_${contractorId}`]
                              ),
                            };
                          });
                      });

                      const totals: Record<number, Record<string, number>> = {};

                      dataTem?.forEach(entry => {
                        const {
                          contractorId,
                          contractedOrEstimatedQuantityTotalAmount,
                          currentPeriodExecutionTotalAmount,
                        } = entry;
                        if (!totals[contractorId]) {
                          totals[contractorId] = {
                            id: 0,
                            reportAnnex3aId: 0,
                            contractorId,
                            contractValue: 0, // 1. Giá trị hợp đồng (giá trị dự toán được duyệt trong trường hợp thực hiện không thông qua hợp đồng)
                            advancePaymentValueForThePeriod: 0, // 2. Giá trị tạm ứng còn lại chưa thu hồi đến cuối kỳ trước
                            advancePaymentExecution: 0, // 3. Số tiền đã thanh toán khối lượng hoàn thành kỳ trước
                            completedVolumeThisPeriod: 0, // 4. Lũy kế giá trị khối lượng thực hiện đến cuối kỳ này
                            advancePaymentRecovery: 0, // 5. Thanh toán để thu hồi tạm ứng
                            valuePaymentForCompletedVolume: 0, // 6. Giá trị đề nghị giải ngân kỳ này - Thanh toán khối lượng hoàn thành
                            valueAdvancePayment: 0, // 6. Giá trị đề nghị giải ngân kỳ này - tạm ứng
                            cumulativePaymentForCompletedVolume: 0, // 7. Luỹ kế giá trị giải ngân, trong đó - Thanh toán khối lượng hoàn thành
                            cumulativeAdvancePayment: 0, // 7. Luỹ kế giá trị giải ngân, trong đó - tạm ứng
                          };
                        }
                        totals[contractorId].contractValue +=
                          contractedOrEstimatedQuantityTotalAmount;
                        totals[contractorId].completedVolumeThisPeriod +=
                          currentPeriodExecutionTotalAmount;
                      });
                      // Bước 3: Chuyển kết quả về dạng mảng
                      const resultOverviews = Object.values(totals);
                      const temEditableData = resultOverviews.map(item => {
                        const objectPayment = response.filter(
                          a => a.contractorId == item.contractorId
                        )[0];
                        const completedVolumeThisPeriod = convertMoneny(
                          item.completedVolumeThisPeriod ?? 0
                        );
                        const advancePaymentValueForThePeriod = Number.isNaN(objectPayment?.advance)
                          ? 0
                          : convertMoneny(objectPayment?.advance ?? 0);
                        const advancePaymentExecution = Number.isNaN(objectPayment?.payment)
                          ? 0
                          : convertMoneny(objectPayment?.payment ?? 0);

                        const _advancePaymentRecovery = convertMoneny(
                          (item.completedVolumeThisPeriod / item.contractValue) *
                            (objectPayment.advanceBase ?? 0) *
                            0.8
                        ); /// 5. Thanh toán để thu hồi tạm ứng

                        // const [valueAdvancePayment] = getSumsFromArray(editableData, ['contractValue']);
                        const _valuePaymentForCompletedVolume = convertMoneny(
                          Number(item.completedVolumeThisPeriod) * 0.95 - _advancePaymentRecovery
                        ); // 6. Giá trị đề nghị giải ngân kỳ này - Thanh toán khối lượng hoàn thành

                        const _cumulativePaymentForCompletedVolume = convertMoneny(
                          Number(advancePaymentExecution) +
                            Number(_advancePaymentRecovery ?? 0) +
                            Number(_valuePaymentForCompletedVolume ?? 0)
                        );
                        const _cumulativeAdvancePayment = convertMoneny(
                          Number(advancePaymentValueForThePeriod) - _advancePaymentRecovery < 0
                            ? 0
                            : Number(advancePaymentValueForThePeriod) - _advancePaymentRecovery
                        );
                        return {
                          ...item,
                          id: -getRandomNumber(),
                          contractorId: item.contractorId,
                          reportAnnex3aId: item.reportAnnex3aId,
                          contractValue: convertMoneny(item.contractValue),
                          completedVolumeThisPeriod: completedVolumeThisPeriod,
                          valueAdvancePayment: 0,
                          advancePaymentValueForThePeriod: Number.isNaN(
                            advancePaymentValueForThePeriod
                          )
                            ? 0
                            : advancePaymentValueForThePeriod,
                          advancePaymentExecution: Number.isNaN(advancePaymentExecution)
                            ? 0
                            : advancePaymentExecution,
                          advancePaymentRecovery: Number.isNaN(_advancePaymentRecovery)
                            ? 0
                            : _advancePaymentRecovery,
                          valuePaymentForCompletedVolume: Number.isNaN(
                            _valuePaymentForCompletedVolume
                          )
                            ? 0
                            : _valuePaymentForCompletedVolume,
                          cumulativePaymentForCompletedVolume: Number.isNaN(
                            _cumulativePaymentForCompletedVolume
                          )
                            ? 0
                            : _cumulativePaymentForCompletedVolume,
                          cumulativeAdvancePayment: Number.isNaN(_cumulativeAdvancePayment)
                            ? 0
                            : _cumulativeAdvancePayment,
                        };
                      });

                      const [
                        contractValue,
                        advancePaymentValueForThePeriod,
                        advancePaymentExecution,
                        completedVolumeThisPeriod,
                        advancePaymentRecovery,
                        valuePaymentForCompletedVolume,
                        valueAdvancePayment,
                        cumulativePaymentForCompletedVolume,
                        cumulativeAdvancePayment,
                      ] = getSumsFromArray(temEditableData, [
                        'contractValue',
                        'advancePaymentValueForThePeriod',
                        'advancePaymentExecution',
                        'completedVolumeThisPeriod',
                        'advancePaymentRecovery',
                        'valuePaymentForCompletedVolume',
                        'valueAdvancePayment',
                        'cumulativePaymentForCompletedVolume',
                        'cumulativeAdvancePayment',
                      ]);

                      setValue('reportAnnex3aOverviews', temEditableData);

                      setValue('sumContractValue', contractValue);
                      setValue(
                        'sumAdvancePaymentValueForThePeriod',
                        advancePaymentValueForThePeriod
                      );
                      setValue('sumAdvancePaymentExecution', advancePaymentExecution);
                      setValue('sumCompletedVolumeThisPeriod', completedVolumeThisPeriod);
                      setValue('sumAdvancePaymentRecovery', advancePaymentRecovery);
                      setValue(
                        'sumDisbursementRequestValue',
                        valuePaymentForCompletedVolume + valueAdvancePayment
                      );
                      setValue(
                        'sumCumulativeDisbursedValue',
                        cumulativePaymentForCompletedVolume + cumulativeAdvancePayment
                      );
                      // }
                    })
                    .catch(error => {
                      console.error('error:', error);
                    });
                }}
                text={getDataLabel}
              />
              {errors.reportAnnex3aOverviews?.message && (
                <ErrorMessage message={errors.reportAnnex3aOverviews?.message} />
              )}
            </>
          );
        }}
      />
    </div>
  );
};
