import { requiredTextWithNamespace } from '@/lib/i18nUtils';
import { z } from 'zod';

export const requireWeeklyProjectSchedulePlanText = requiredTextWithNamespace(
  'weeklyProjectSchedulePlan'
);

// Tạo các trường weekXX và landWeekXX tự động
const weekFields: Record<string, z.ZodString> = {};
for (let i = 1; i <= 52; i++) {
  const weekNum = String(i).padStart(2, '0');
  weekFields[`week${weekNum}`] = z.string();
  weekFields[`landWeek${weekNum}`] = z.string();
}

export const weeklyProjectSchedulePlanDetailSchema = z.object({
  id: z.number(),
  projectId: z
    .number({
      required_error: requireWeeklyProjectSchedulePlanText(
        'weeklyProjectSchedulePlanDetailFields.projectId',
        'select'
      ),
      invalid_type_error: requireWeeklyProjectSchedulePlanText(
        'weeklyProjectSchedulePlanDetailFields.projectId',
        'select'
      ),
    })
    .min(
      1,
      requireWeeklyProjectSchedulePlanText(
        'weeklyProjectSchedulePlanDetailFields.projectId',
        'select'
      )
    ),
  projectName: z.string().nullable().optional(),
  issuesAndRecommendations: z.string(),
  ...weekFields,
});

export const weeklyProjectSchedulePlanSchema = z.object({
  branchId: z.number().nullable(),
  storeId: z.number(),
  ids: z.number().nullable(),
  sort: z.string().nullable(),
  code: z.string(),
  id: z.number(),

  userCreatedId: z.number().nullable(),
  weeklyProjectSchedulePlanTime: z.date().nullable().optional(),
  budgetYear: z.date().nullable().optional(),
  totalNumberProject: z.number().nullable(),
  note: z.string().nullable().optional(),

  weeklyProjectSchedulePlanDetails: z.array(weeklyProjectSchedulePlanDetailSchema),
});

export type WeeklyProjectSchedulePlan = z.infer<typeof weeklyProjectSchedulePlanSchema>;
export type WeeklyProjectSchedulePlanDetail = z.infer<typeof weeklyProjectSchedulePlanDetailSchema>;

// Tạo giá trị mặc định cho các trường week camelCase
const defaultWeekFields: Record<string, string> = {};
for (let i = 1; i <= 52; i++) {
  const weekNum = String(i).padStart(2, '0');
  defaultWeekFields[`week${weekNum}`] = '';
  defaultWeekFields[`landWeek${weekNum}`] = '';
}

// Tạo giá trị mặc định riêng cho Detail
export const defaultValuesWeeklyProjectSchedulePlanDetail: WeeklyProjectSchedulePlanDetail = {
  // Giả sử id và projectId mặc định là 0 hoặc null tùy logic
  id: 0,
  projectId: 0,
  issuesAndRecommendations: '', // Thêm trường còn thiếu
  ...defaultWeekFields, // Thêm các trường week mặc định
};

export const defaultValuesWeeklyProjectSchedulePlan: WeeklyProjectSchedulePlan = {
  branchId: null,
  storeId: 0,
  ids: null,
  sort: '',
  code: '',
  id: 0,
  userCreatedId: null, // Người lập
  weeklyProjectSchedulePlanTime: new Date(), // Ngày lập
  budgetYear: new Date(), // Năm ngân sách
  totalNumberProject: null, // Tổng số dự án
  note: '', // note,
  // Sử dụng giá trị mặc định mới cho Detail
  weeklyProjectSchedulePlanDetails: [defaultValuesWeeklyProjectSchedulePlanDetail],
};
