import { zod<PERSON><PERSON>olver } from '@hookform/resolvers/zod';
import { SyntheticEvent, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useParams } from 'react-router-dom';

import { DeleteConfirmDialog } from '@/components/confirm-dialog';
import { PageLayout } from '@/components/page-layout';
import { PeriodFilter } from '@/components/period-filter-form';
import { Form, FormCombobox, FormField, FormLabel } from '@/components/ui/form';
import { InputNumber } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  enterLabel,
  MUTATE,
  PATHS,
  PERMISSIONS,
  PROFESSIONS,
  QUERIES,
  selectLabel,
} from '@/constant';
import { useAuth, useDataTable, useFormHandler, useFormOperation, usePermission } from '@/hooks';
import { useFormNavigate } from '@/hooks/use-form-navigate';
import { toDateType, toLocaleDate } from '@/lib/date';
import { createMutationSuccessFn } from '@/lib/i18nUtils';
import {
  createDeleteMutateFn,
  createPostMutateFn,
  createPutMutateFn,
  createQueryByIdFn,
} from '@/services';
import { DateBox, TextBox } from 'devextreme-react';
import Button from 'devextreme-react/button';
import { getSumsFromArray } from '@/lib/utils';
import {
  AdjustedCapitalIncreasePlan,
  adjustedCapitalIncreasePlanSchema,
  defaultValuesAdjustedCapitalIncreasePlan,
} from '@/types/adjusted-capital-increase-plan';
import { AdjustedCapitalIncreasePlanEditableTable } from './adjusted-capital-increase-plan-editable-table';
import { CapitalIncreasePlanSelectTable } from './capital-increase-plan-select-table';
import { RecordEditableTable } from '@/components/records-attachment';
import { RowSelectionState } from '@tanstack/react-table';
import { CapitalIncreasePlan } from '@/types';

const onAdjustedCapitalIncreasePlanMutationSuccess = createMutationSuccessFn(
  'adjustedCapitalIncreasePlan'
);

export const AdjustedCapitalIncreasePlanForm = () => {
  const { id: editId } = useParams();

  const { t } = useTranslation('adjustedCapitalIncreasePlan');

  const role = usePermission(PERMISSIONS.ADJUSTED_CAPITAL_INCREASE_PLAN);
  const { user } = useAuth();

  const { goBackToList, goToUpdate, goToNew } = useFormNavigate(
    PATHS.ADJUSTED_CAPITAL_INCREASE_PLAN
  );

  const defaultValues = useMemo(
    () => ({
      ...defaultValuesAdjustedCapitalIncreasePlan,
      userCreatedId: user?.userId,
    }),
    [user?.userId]
  );

  const { handleSubmit, loading, methods } = useFormHandler<AdjustedCapitalIncreasePlan>({
    queryKey: [MUTATE.ADJUSTED_CAPITAL_INCREASE_PLAN, editId],
    mutateKey: [MUTATE.ADJUSTED_CAPITAL_INCREASE_PLAN],
    queryId: Number(editId) || 0,
    invalidateKey: [QUERIES.ADJUSTED_CAPITAL_INCREASE_PLAN],
    readFn: createQueryByIdFn<AdjustedCapitalIncreasePlan>('adjusted-capital-increase-plan'),
    createFn: createPostMutateFn<AdjustedCapitalIncreasePlan>('adjusted-capital-increase-plan'),
    updateFn: createPutMutateFn<AdjustedCapitalIncreasePlan>('adjusted-capital-increase-plan'),
    formatPayloadFn: data => ({
      ...data,
      adjustedCapitalIncreasePlanTime: toLocaleDate(data.adjustedCapitalIncreasePlanTime!),
      approvalDate: toLocaleDate(data.approvalDate!),
      budgetYear: toLocaleDate(data.budgetYear!),
    }),
    formatResponseFn: data => ({
      ...data,
      adjustedCapitalIncreasePlanTime: toDateType(data.adjustedCapitalIncreasePlanTime!),
      approvalDate: toDateType(data.approvalDate!),
      budgetYear: toDateType(data.budgetYear!),
      adjustedCapitalIncreasePlanDetails: data.adjustedCapitalIncreasePlanDetails.map(item => ({
        ...item,
      })),
    }),
    onCreateSuccess: data => {
      onAdjustedCapitalIncreasePlanMutationSuccess(data);
      goToUpdate(data);
    },
    onUpdateSuccess: onAdjustedCapitalIncreasePlanMutationSuccess,
    formOptions: {
      resolver: zodResolver(adjustedCapitalIncreasePlanSchema),
      defaultValues,
    },
  });

  const [userCreatedName, budgetFundName, capitalIncreasePlanCode] = methods.getValues([
    'userCreatedName',
    'budgetFundName',
    'capitalIncreasePlanCode',
  ]);

  const {
    isDeleting,
    deleteTarget,
    selectTargetToDelete,
    toggleConfirmDeleteDialog,
    isConfirmDeleteDialogOpen,
  } = useDataTable<AdjustedCapitalIncreasePlan, PeriodFilter>({
    deleteFn: createDeleteMutateFn('adjusted-capital-increase-plan'),
    deleteKey: [MUTATE.ADJUSTED_CAPITAL_INCREASE_PLAN],
  });

  const { reset, onTimeChange } = useFormOperation<AdjustedCapitalIncreasePlan>({
    model: 'adjusted-capital-increase-plan',
    fieldTime: 'adjustedCapitalIncreasePlanTime',
    createCodeKey: [QUERIES.ADJUSTED_CAPITAL_INCREASE_PLAN],
    formMethods: methods,
  });

  const onCreateNew = () => {
    goToNew();
    methods.reset(defaultValues);
    reset();
  };

  const onDelete = () => {
    selectTargetToDelete(methods.getValues());
  };
  const onFormCalculate = <T,>(name?: keyof T, value?: number) => {
    const values = { ...methods.getValues(), ...(name ? { [name]: value } : {}) };
    const { adjustedCapitalIncreasePlanDetails: editableDataDetails } = values;

    const [totalAmount] = getSumsFromArray(editableDataDetails, ['totalAmount']);
    // methods.reset({ ...values, totalAmount });
    methods.setValue('totalAmount', totalAmount);
  };

  const [rowSelection, setRowSelection] = useState<RowSelectionState>({});

  const [budgetYear, approvalNumber, approvalDate] = methods.watch([
    'budgetYear',
    'approvalNumber',
    'approvalDate',
  ]);

  return (
    <>
      <Form {...methods}>
        <form autoComplete="off">
          <PageLayout
            onSaveChange={e => {
              handleSubmit(e.event?.currentTarget as unknown as SyntheticEvent<HTMLElement>);
            }}
            header={editId !== 'new' ? t('page.form.edit') : t('page.form.addNew')}
            canSaveChange={!isNaN(Number(editId)) ? role?.isUpdate : role?.isCreate}
            isSaving={loading}
            onCancel={goBackToList}
            onDelete={editId !== 'new' ? onDelete : undefined}
            customElementLeft={
              <>
                <Button
                  text={t('content.createNew', { ns: 'common' })}
                  className="uppercase"
                  stylingMode="outlined"
                  type="default"
                  icon="plus"
                  onClick={onCreateNew}
                />
              </>
            }
            customElementRight={
              <>
                <CapitalIncreasePlanSelectTable />
              </>
            }
          >
            <div className="grid max-w-screen-2xl grid-cols-1 gap-x-8 gap-y-4 lg:grid-cols-4">
              {/* Cột 1 */}
              <div className="col-span-1 lg:col-span-3 2xl:col-span-2">
                <div className="grid grid-cols-1 gap-x-8 gap-y-4 lg:grid-cols-2">
                  {/* Nguồn ngân sách */}
                  <div className="col-span-1 flex items-center lg:col-span-2">
                    <FormLabel
                      name="budgetFundId"
                      htmlFor="budgetFundId"
                      className="hidden w-[100px] md:block"
                    >
                      {t('fields.budgetFundId')}
                    </FormLabel>
                    <FormField
                      id="budgetFundId"
                      name="budgetFundId"
                      className="min-w-0 flex-1 md:w-[250px]"
                      label={t('fields.budgetFundId')}
                    >
                      <FormCombobox
                        defaultText={budgetFundName}
                        placeholder={`${selectLabel} ${t('fields.budgetFundId')}`}
                        model="budget-fund"
                        queryKey={[QUERIES.BUDGET_FUND]}
                        disabled
                      />
                    </FormField>
                  </div>
                  {/* Ngày quyết định */}
                  <div className="col-span-1 flex items-center 2xl:hidden">
                    <FormLabel
                      name="approvalDate"
                      htmlFor="approvalDate"
                      className="hidden w-[100px]  md:block"
                    >
                      {t('fields.approvalDate')}
                    </FormLabel>
                    <FormField
                      id="approvalDate"
                      name="approvalDate"
                      className="min-w-0 flex-1 "
                      type="date"
                      label={t('fields.approvalDate')}
                    >
                      <DateBox
                        placeholder={`${selectLabel} ${t('fields.approvalDate')}`}
                        pickerType="calendar"
                        focusStateEnabled={false}
                      />
                    </FormField>
                  </div>
                  {/* Số quyết định */}
                  <div className="col-span-1 flex items-center">
                    <FormLabel htmlFor="approvalNumber" className="hidden w-[100px] md:block">
                      {t('fields.approvalNumber')}
                    </FormLabel>
                    <FormField
                      id="approvalNumber"
                      name="approvalNumber"
                      className="min-w-0 flex-1 "
                      label={t('fields.approvalNumber')}
                    >
                      <TextBox placeholder={`${enterLabel} ${t('fields.approvalNumber')}`} />
                    </FormField>
                  </div>

                  {/* Năm ngân sách */}
                  <div className="col-span-1 flex items-center">
                    <FormLabel htmlFor="budgetYear" className="hidden w-[100px] md:block">
                      {t('fields.budgetYear')}
                    </FormLabel>
                    <FormField
                      id="budgetYear"
                      name="budgetYear"
                      className="min-w-0 flex-1"
                      type="date"
                      label={t('fields.budgetYear')}
                    >
                      <DateBox
                        placeholder={`${selectLabel} ${t('fields.budgetYear')}`}
                        calendarOptions={{
                          maxZoomLevel: 'decade',
                          minZoomLevel: 'decade',
                        }}
                        displayFormat={'year'}
                        pickerType="calendar"
                        focusStateEnabled={false}
                        readOnly
                      />
                    </FormField>
                  </div>
                  {/* Tổng tiền */}
                  <div className="col-span-1 flex items-center 2xl:hidden">
                    <FormLabel
                      name="totalAmount"
                      htmlFor="totalAmount"
                      className="hidden w-[100px] md:block"
                    >
                      {t('fields.totalAmount')}
                    </FormLabel>
                    <FormField
                      id="totalAmount"
                      name="totalAmount"
                      className="w-full flex-1"
                      type="number"
                      label={t('fields.totalAmount')}
                    >
                      <InputNumber
                        placeholder={`${enterLabel} ${t('fields.totalAmount')}`}
                        readOnly={true}
                      />
                    </FormField>
                  </div>

                  {/* Ghi chú */}
                  <div className="col-span-1 hidden items-center lg:col-span-2 lg:flex">
                    <FormLabel htmlFor="note" className="hidden w-[100px] md:block">
                      {t('fields.note')}
                    </FormLabel>
                    <FormField
                      id="note"
                      name="note"
                      className="min-w-0 flex-1 md:w-[250px]"
                      label={t('fields.note')}
                    >
                      <TextBox placeholder={`${enterLabel} ${t('fields.note')}`} />
                    </FormField>
                  </div>
                </div>
              </div>

              {/* Cột 2 */}
              <div className="col-span-1 hidden 2xl:block">
                <div className="grid grid-cols-1 gap-x-8 gap-y-4">
                  {/* Từ kế hoạch giao vốn */}
                  <div className="col-span-1 flex items-center">
                    <FormLabel
                      name="capitalIncreasePlanId"
                      htmlFor="capitalIncreasePlanId"
                      className="hidden w-[100px] md:block"
                    >
                      {t('fields.capitalIncreasePlanId')}
                    </FormLabel>
                    <FormField
                      id="capitalIncreasePlanId"
                      name="capitalIncreasePlanId"
                      className="min-w-0 flex-1"
                      type="number"
                      label={t('fields.capitalIncreasePlanId')}
                    >
                      <FormCombobox<CapitalIncreasePlan>
                        defaultText={capitalIncreasePlanCode}
                        placeholder={`${selectLabel} ${t('fields.capitalIncreasePlanId')}`}
                        model="capital-increase-plan"
                        queryKey={[QUERIES.CAPITAL_INCREASE_PLAN]}
                        showFields={['approvalNumber']}
                        disabled
                      />
                    </FormField>
                  </div>
                  {/* Ngày quyết định */}
                  <div className="col-span-1 flex items-center">
                    <FormLabel
                      name="approvalDate"
                      htmlFor="approvalDate"
                      className="hidden w-[100px] md:block"
                    >
                      {t('fields.approvalDate')}
                    </FormLabel>
                    <FormField
                      id="approvalDate"
                      name="approvalDate"
                      className="min-w-0  flex-1"
                      type="date"
                      label={t('fields.approvalDate')}
                    >
                      <DateBox
                        placeholder={`${selectLabel} ${t('fields.approvalDate')}`}
                        pickerType="calendar"
                        focusStateEnabled={false}
                      />
                    </FormField>
                  </div>

                  {/* Tổng tiền */}
                  <div className="col-span-1 flex items-center">
                    <FormLabel
                      name="totalAmount"
                      htmlFor="totalAmount"
                      className="hidden w-[100px] md:block"
                    >
                      {t('fields.totalAmount')}
                    </FormLabel>
                    <FormField
                      id="totalAmount"
                      name="totalAmount"
                      className="min-w-0 flex-1"
                      type="number"
                      label={t('fields.totalAmount')}
                    >
                      <InputNumber
                        placeholder={`${enterLabel} ${t('fields.totalAmount')}`}
                        readOnly={true}
                      />
                    </FormField>
                  </div>
                </div>
              </div>

              {/* Cột 3 */}
              <div className="col-span-1">
                <div className="grid grid-cols-1 gap-x-8 gap-y-4">
                  {/* Ngày lập */}
                  <div className="flex items-center">
                    <FormLabel
                      name="adjustedCapitalIncreasePlanTime"
                      htmlFor="adjustedCapitalIncreasePlanTime"
                      className="hidden   w-[100px] md:block lg:w-[60px]"
                    >
                      {t('fields.adjustedCapitalIncreasePlanTime')}
                    </FormLabel>
                    <FormField
                      id="adjustedCapitalIncreasePlanTime"
                      name="adjustedCapitalIncreasePlanTime"
                      className="min-w-0 flex-1"
                      type="date"
                      onChange={e => {
                        onTimeChange(e.target.value);
                      }}
                      label={t('fields.adjustedCapitalIncreasePlanTime')}
                    >
                      <DateBox
                        placeholder={`${selectLabel} ${t('fields.adjustedCapitalIncreasePlanTime')}`}
                        pickerType="calendar"
                        focusStateEnabled={false}
                      />
                    </FormField>
                  </div>

                  {/* Mã phiếu */}
                  <div className="flex items-center">
                    <FormLabel
                      name="code"
                      htmlFor="code"
                      className="hidden w-[100px] md:block lg:w-[60px]"
                    >
                      {t('fields.code')}
                    </FormLabel>
                    <FormField
                      id="code"
                      name="code"
                      className="min-w-0 flex-1"
                      label={t('fields.code')}
                    >
                      <TextBox placeholder={`${enterLabel} ${t('fields.code')}`} readOnly={true} />
                    </FormField>
                  </div>

                  {/* Người lập */}
                  <div className="flex items-center">
                    <FormLabel
                      name="userCreatedId"
                      htmlFor="userCreatedId"
                      className="hidden w-[100px] md:block lg:w-[60px]"
                    >
                      {t('fields.userCreatedId')}
                    </FormLabel>
                    <FormField
                      id="userCreatedId"
                      name="userCreatedId"
                      className="min-w-0 flex-1"
                      label={t('fields.userCreatedId')}
                    >
                      <FormCombobox
                        defaultText={userCreatedName}
                        placeholder={`${selectLabel} ${t('fields.userCreatedId')}`}
                        model="user"
                        queryKey={[QUERIES.USERS]}
                        disabled
                      />
                    </FormField>
                  </div>
                </div>
              </div>

              {/* Ghi chú */}
              <div className="col-span-1 flex items-center lg:hidden">
                <FormLabel htmlFor="note" className="hidden w-[100px] md:block">
                  {t('fields.note')}
                </FormLabel>
                <FormField
                  id="note"
                  name="note"
                  className="min-w-0 flex-1 md:w-[250px]"
                  label={t('fields.note')}
                >
                  <TextBox placeholder={`${enterLabel} ${t('fields.note')}`} />
                </FormField>
              </div>
            </div>
            <div className="mt-8">
              <Tabs defaultValue="detail">
                <div className="w-full">
                  <TabsList>
                    <TabsTrigger value="detail">{t('page.tabs.detail')}</TabsTrigger>
                    <TabsTrigger value="attachment">{t('page.tabs.attachment')}</TabsTrigger>
                  </TabsList>
                </div>
                <TabsContent value="detail" className="mt-4">
                  <AdjustedCapitalIncreasePlanEditableTable
                    role={role}
                    calculateForm={onFormCalculate}
                    budgetYear={budgetYear?.getFullYear()?.toString() || ''}
                    approvalNumber={approvalNumber}
                    approvalDate={approvalDate?.toLocaleDateString('vi-VN') || ''}
                  />
                </TabsContent>
                <TabsContent value="attachment" className="mt-4">
                  <RecordEditableTable
                    role={role}
                    rowSelection={rowSelection}
                    setRowSelection={setRowSelection}
                    folder="adjusted-capital-increase-plan"
                    profession={PROFESSIONS.ADJUSTED_CAPITAL_INCREASE_PLAN}
                  />
                </TabsContent>
              </Tabs>
            </div>
          </PageLayout>
        </form>
      </Form>
      <DeleteConfirmDialog
        model="adjusted-capital-increase-plan"
        name={methods.getValues('note')!}
        open={isConfirmDeleteDialogOpen}
        toggle={toggleConfirmDeleteDialog}
        isDeleting={isDeleting}
        onConfirm={() => {
          deleteTarget();
          setTimeout(() => onCreateNew(), 0);
        }}
      />
    </>
  );
};
