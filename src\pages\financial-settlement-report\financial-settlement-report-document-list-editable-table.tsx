import { AGENCY_TYPE, QUERIES, TABLES } from '@/constant';
import { Agency, FinancialSettlementReport } from '@/types';
import { useFormContext, useWatch } from 'react-hook-form';

import { useEntity } from '@/hooks';
import { translationWithNamespace } from '@/lib/i18nUtils';
// import { useMemo } from 'react';
import { DevexDataGrid } from '@/components/devex-data-grid';
import { displayExpr } from '@/lib/utils';
import { Column, Lookup } from 'devextreme-react/data-grid';

const t = translationWithNamespace('financialSettlementReport');

export const FinancialSettlementReportDocumentListEditableTable = () => {
  const {
    // setValue,
    control,
  } = useFormContext<FinancialSettlementReport>();

  const [editableData] = useWatch({
    control,
    name: ['financialSettlementReportDocumentLists'],
  });
  const { list: documentGroups } = useEntity({
    queryKey: [QUERIES.DOCUMENT_GROUP],
    model: 'document-group',
  });
  // const { list: documentTypes } = useEntity({
  //   queryKey: [QUERIES.DOCUMENT_TYPE],
  //   model: 'document-type',
  // });
  const { list: allAgencies } = useEntity<Agency>({ queryKey: [QUERIES.AGENCY], model: 'agency' });
  const agencies = allAgencies.filter(item => item.agencyType === AGENCY_TYPE.ISSUE);

  // const buildTreeListData = (data: FinancialSettlementReportDocumentList[]) => {
  //   const result: {
  //     id: string;
  //     parentId: string | null;
  //     name: string | null;
  //     agencyId: number | null;
  //     noDoc: string | null;
  //     dateCreate: Date | null;
  //     notes: string | null;
  //   }[] = [];
  //   const added = new Set();

  //   data.forEach((row, index) => {
  //     const groupId = `G-${row.groupDocId}`;
  //     const parentId = `TP-${row.typeDocParentId}`;
  //     const typeId = `T-${row.typeDocId}`;
  //     const contentId = `C-${index}`;

  //     if (!added.has(groupId)) {
  //       result.push({
  //         id: groupId,
  //         parentId: null,
  //         name: row.groupDocName!,
  //         agencyId: null,
  //         noDoc: '',
  //         dateCreate: null,
  //         notes: null,
  //       });
  //       added.add(groupId);
  //     }
  //     if (!added.has(parentId) && groupId) {
  //       result.push({
  //         id: parentId,
  //         parentId: groupId,
  //         name: row.typeDocParentName!,
  //         agencyId: null,
  //         noDoc: '',
  //         dateCreate: null,
  //         notes: null,
  //       });
  //       added.add(parentId);
  //     }

  //     if (!added.has(typeId)) {
  //       result.push({
  //         id: typeId,
  //         parentId: parentId,
  //         name: row.typeDocName!,
  //         agencyId: null,
  //         noDoc: '',
  //         dateCreate: null,
  //         notes: null,
  //       });
  //       added.add(typeId);
  //     }

  //     result.push({
  //       id: contentId,
  //       parentId: typeId,
  //       name: row.content!,
  //       agencyId: row.agencyId!,
  //       noDoc: row.noDoc!,
  //       dateCreate: row.dateCreate!,
  //       notes: row.notes!,
  //     });
  //   });
  //   return result;
  // };

  const items = editableData; //buildTreeListData(editableData || []);

  return (
    <div>
      <DevexDataGrid
        id={TABLES.FINANCIAL_SETTLEMENT_REPORT_DOCUMENT_LIST}
        // sortColumn="id"
        // role={role}
        dataSource={items}
        keyExpr="id"
        // parentIdExpr={'parentId'}
        // columnFixing={{ enabled: false }}
        editing={{
          allowAdding: true,
          allowUpdating: true,
          allowDeleting: true,
          useIcons: true,
          mode: 'cell',
        }}
      >
        {/* <Editing allowUpdating={false} allowDeleting={false} useIcons /> */}
        {/* <Column
          dataField="typeDocId"
          caption={t('fields.financialSettlementReportDocumentLists.typeDocId')}
          // groupIndex={0}
        >
          <FieldLookup
            dataSource={documentTypes}
            displayExpr={displayExpr(['name'])}
            valueExpr={'id'}
          />
        </Column>
        */}
        <Column
          dataField="content"
          caption={t('fields.financialSettlementReportDocumentLists.content')}
        />
        <Column
          dataField="groupDocId"
          caption={t('fields.financialSettlementReportDocumentLists.groupDocId')}
          groupIndex={0}
        >
          <Lookup
            dataSource={documentGroups}
            displayExpr={displayExpr(['name'])}
            valueExpr={'id'}
          />
        </Column>
        <Column caption={t('fields.member4')} alignment="center">
          <Column
            dataField="noDoc"
            caption={t('fields.financialSettlementReportDocumentLists.noDoc')}
          />{' '}
          <Column
            dataField="dateCreate"
            caption={t('fields.financialSettlementReportDocumentLists.dateCreate')}
          />
        </Column>

        <Column
          dataField="agencyId"
          caption={t('fields.financialSettlementReportDocumentLists.agencyId')}
        >
          <Lookup dataSource={agencies} displayExpr={displayExpr(['name'])} valueExpr={'id'} />
        </Column>
        <Column
          dataField="notes"
          caption={t('fields.financialSettlementReportDocumentLists.notes')}
          alignment="center"
        />
      </DevexDataGrid>
    </div>
  );
};
