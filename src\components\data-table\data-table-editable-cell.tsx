/* eslint-disable @typescript-eslint/no-unused-vars */
import { DEFAULT_DECIMAL, MUTATE } from '@/constant/const';
import { useMutate } from '@/hooks';
import { s3DowndloadFileHandler, uploadFileHandler } from '@/lib/file';
import { cn } from '@/lib/utils';
import {
  getDownloadOnetimeToken,
  getPresignedUrlFromS3,
  s3DowndloadFile,
  uploadMulti,
  uploadMultipartPresigned,
  uploadToS3
} from '@/services';
import { ListComponentResponse, RecordAttachmentFile } from '@/types';
import { CellContext } from '@tanstack/react-table';
import { DateBox, TextArea } from 'devextreme-react';
import { dxDateBoxOptions } from 'devextreme/ui/date_box';
import { Download, Paperclip } from 'lucide-react';
import React, { ComponentProps, useEffect, useRef, useState } from 'react';
import { NumericFormat } from 'react-number-format';
import { Button } from '../ui/button';
import { FormCombobox, FormComboboxProps } from '../ui/form';
import { Input } from '../ui/input';

/**
 * Editable date-picker cell
 */
const EditableDatePickerCell = <TData,>({
  getValue,
  row: { index },
  column: { id },
  table,

  // unused props
  cell,
  renderValue,
  // defaultValue,
  ...props
}: CellContext<TData, unknown> & dxDateBoxOptions & { className?: string }) => {
  // const { t } = useTranslation();

  return (
    // <FormDatePicker
    //   {...props}
    //   value={getValue()}
    //   className={cn('rounded-none border-none', props.className)}
    //   // placeholder={`${selectLabel} ${t('content.data')}`}
    //   onChange={value => {
    //     table.options.meta?.updateData(index, id, value);
    //   }}
    // />
    <DateBox
      className="before:!border-b-0 after:!border-b-0"
      inputAttr={{
        ...props,
        class: cn(props.className),
      }}
      value={getValue<Date>()}
      onValueChanged={e => {
        const value =
          e.value instanceof Date ? e.value : e.value ? new Date(e.value as string) : new Date();
        table.options.meta?.updateData(index, id, value);
      }}
      pickerType="calendar"
      focusStateEnabled={false}
      {...props}
    />
  );
};

interface EditableInputCellProps extends ComponentProps<typeof Input> {
  decimal?: number;
  onValueChange?: (value: number | string) => void;
}
interface EditableInputCelllProps extends ComponentProps<typeof Input> {
  decimal?: number;
  onValueChange?: (value: number | string) => void;
  isMoney?: boolean;
  hideDecimal?: boolean;
}
/**
 * Editable input cell
 */
// const EditableInputCellOld = <TData,>({
//   getValue,
//   row: { index },
//   column: { id },
//   table,
//   type,

//   decimal = DEFAULT_DECIMAL,

//   onValueChange,
//   // unused props
//   cell,
//   renderValue,
//   ...props
// }: CellContext<TData, unknown> & EditableInputCellProps) => {
//   // const { t } = useTranslation();

//   const initialValue = getValue() ? String(getValue()) : '0';

//   // We need to keep and update the state of the cell normally
//   const [value, setValue] = useState<string>(initialValue);
//   const [number, setNumber] = useState<string>();

//   /**
//    * Ref to properly handle the case when user changes the index of selection cursor
//    */
//   const selectionStartRef = useRef<number | null>(null);
//   const commasNumbers = useRef<number | null>(null);

//   const isNumber = type === 'number';

//   const setState = useCallback(
//     (value: string) => {
//       let text = value;
//       let number = value;

//       if (isNumber) {
//         number = normalizeNumeric(value);
//         setNumber(number);
//         text = realNumberDecimalFormat(number, decimal);
//         // number with type text -> 10000 -> 10,000
//       }

//       setValue(text);

//       return [text, number];
//     },
//     [decimal, isNumber]
//   );

//   // If the initialValue is changed external, sync it up with our state
//   useEffect(() => {
//     if (initialValue !== undefined || initialValue !== null) {
//       setState(String(initialValue));
//     }
//   }, [initialValue, setState]);

//   // When the input is blurred, we'll call our table meta's updateData function
//   const onBlur = () => {
//     table.options.meta?.updateData(index, id, isNumber ? Number(number) : value);
//   };

//   const onChange: ChangeEventHandler<HTMLInputElement> = event => {
//     const { value, selectionStart } = event.target;
//     const [text, number] = setState(value);

//     const valueChange = isNumber ? number : text;

//     onValueChange?.(valueChange);

//     let currentSelectionStart = selectionStart || 0;

//     const prevCommasNumbers = commasNumbers.current;
//     const currentCommasNumbers = getCommasNumbers(text);

//     if (prevCommasNumbers! < currentCommasNumbers) {
//       currentSelectionStart += 1;
//     }

//     selectionStartRef.current = currentSelectionStart;
//     commasNumbers.current = currentCommasNumbers;

//     setTimeout(() => {
//       event.target.setSelectionRange(currentSelectionStart, currentSelectionStart);
//     }, 0);
//   };

//   return (
//     <Input
//       value={value}
//       onBlur={onBlur}
//       onChange={onChange}
//       // placeholder={`${enterLabel.toLocaleLowerCase()} ${t('content.data').toLocaleLowerCase()}`}
//       className={cn('rounded-none border-none', props.className, isNumber ? 'text-right' : '')}
//       {...props}
//     />
//   );
// };

/**
 * Editable input number cell
 */

const EditableInputCell = <TData,>({
  getValue,
  row: { index },
  column: { id },
  table,
  type,
  isMoney = false,
  hideDecimal = false,
  decimal = DEFAULT_DECIMAL,
  onValueChange,
  // unused props
  cell,
  renderValue,
  ...props
}: CellContext<TData, unknown> & EditableInputCelllProps) => {
  // const { t } = useTranslation();
  const isNumber = type === 'number';

  const initialValue = getValue() ? String(getValue()) : isNumber ? '0' : '';
  // We need to keep and update the state of the cell normally
  const [value, setValue] = useState<number | string>(initialValue);

  /**
   * Ref to properly handle the case when user changes the index of selection cursor
   */
  const setState = React.useCallback(
    (value: number | string) => {
      let processedValue = value;

      // Xử lý khi isMoney = true
      if ((isMoney || decimal === 0) && isNumber && typeof value === 'number') {
        // Chuyển số thành chuỗi để kiểm tra phần thập phân
        const valueStr = String(value);
        const dotIndex = valueStr.indexOf('.');

        // Nếu có phần thập phân
        if (dotIndex !== -1) {
          const firstDecimal = valueStr.charAt(dotIndex + 1);

          // Nếu chữ số đầu tiên sau dấu thập phân >= 5, làm tròn lên
          if (parseInt(firstDecimal) >= 5) {
            processedValue = Math.ceil(value);
          } else {
            // Ngược lại, làm tròn xuống (bỏ phần thập phân)
            processedValue = Math.floor(value);
          }
        }
      }

      setValue(processedValue);
      return processedValue;
    },
    [isMoney]
  );

  // If the initialValue is changed external, sync it up with our state
  useEffect(() => {
    if (initialValue !== undefined && initialValue !== null) {
      setState(isNumber ? Number(initialValue) : initialValue);
    }
  }, [initialValue, setState]);

  // When the input is blurred, we'll call our table meta's updateData function
  const onBlur = () => {
    table.options.meta?.updateData(index, id, isNumber ? Number(value) : value);
  };

  // Hàm xử lý sự kiện từ NumberBox
  const onChange = (value: number | undefined | string) => {
    if (value === undefined) {
      return;
    }

    // Chuyển đổi giá trị thành số nếu cần
    const newValue = setState(isNumber ? Number(value) : value);

    // Gọi callback onValueChange để kích hoạt tính toán
    onValueChange?.(newValue);
  };

  // Hàm xử lý sự kiện từ Input (React.ChangeEvent<HTMLInputElement>)
  const handleInputChange: React.ChangeEventHandler<HTMLInputElement> = event => {
    onChange(event.target.value);
  };

  return (
    <>
      {isNumber ? (
        <NumericFormat
          value={value}
          onBlur={event => {
            onBlur();
            props.onBlur?.(event);
          }}
          onFocus={props.onFocus}
          readOnly={props.readOnly}
          disabled={props.disabled}
          thousandSeparator="."
          decimalSeparator=","
          decimalScale={isMoney || hideDecimal ? 0 : decimal}
          className={cn(
            'read-only:bg-primary-foreground flex h-8 w-full rounded-none border border-none border-input bg-background px-3 py-1 text-right text-xs shadow-sm transition-colors file:border-0 file:bg-transparent file:text-xs file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed'
          )}
          fixedDecimalScale={true}
          onValueChange={({ value }) => {
            onChange(value);
          }}
        />
      ) : (
        <Input
          value={value}
          onBlur={event => {
            onBlur();
            props.onBlur?.(event);
          }}
          onFocus={props.onFocus}
          onChange={handleInputChange}
          className={cn('rounded-none border-none', props.className)}
          {...props}
        />
      )}
    </>
  );
};

/**
 * Editable EditableTextArea
 */
export const EditableTextArea = <TData,>({
  className,
  ...props
}: CellContext<TData, unknown> & EditableInputCellProps) => {
  return (
    <TextArea
      onValueChange={e => {
        props.table.options.meta?.updateData(props.row.index, props.column.id, e);
      }}
      autoResizeEnabled={true}
      className={cn('text-area-editable-cell max-h-[100px] overflow-y-auto', className)}
      disabled={props.disabled}
      readOnly={props.readOnly}
      value={props.getValue() as string}
    />
  );
};

/**
 * Editable dropdown cell
 */
const EditableDropdownCell = <TData, TRecord extends ListComponentResponse>({
  getValue,
  row: { index },
  column: { id },
  table,

  // unused props
  cell,
  renderValue,
  ...props
}: CellContext<TData, unknown> & FormComboboxProps<TRecord>) => {
  // const { t } = useTranslation();
  return (
    <FormCombobox<TRecord>
      {...props}
      isTable
      placeholder=""
      value={getValue<number>()}
      // placeholder={`${selectLabel.toLocaleLowerCase()} ${t('content.data').toLocaleLowerCase()}`}
      className={cn('rounded-none border-none disabled:opacity-100', props.className)}
      onChange={value => {
        table.options.meta?.updateData(index, id, value);
        if (props.onChange) {
          props.onChange(value);
        }
      }}
      isWrap={true}
    />
  );
};

const EditableFileCell = <TData extends RecordAttachmentFile>(
  props: CellContext<TData, unknown> & { folder?: string }
) => {
  const fileInputRef = useRef<HTMLInputElement>(null);

  const { mutateAsync: getDownloadOnetimeTokenAsync } = useMutate({
    mutationKey: [MUTATE.GET_DOWNLOAD_ONE_TIME_TOKEN],
    mutationFn: getDownloadOnetimeToken,
  });

  const { mutateAsync: s3DownloadFileAsync } = useMutate({
    mutationKey: [MUTATE.DOWNLOAD_FROM_S3],
    mutationFn: s3DowndloadFile,
  });

  const handleDownload = () => {
    const { original } = props.row;
    s3DowndloadFileHandler(getDownloadOnetimeTokenAsync, s3DownloadFileAsync, original);
  };

  const handleReplaceClick = () => {
    fileInputRef.current?.click();
  };
  /// comment code fix error upload large file
  // const { mutateAsync: uploadMultiAsync } = useMutate({
  //   mutationKey: [MUTATE.UPLOAD_FILE],
  //   mutationFn: uploadMulti,
  // });

  // const { mutateAsync: getPresignedUrlAsync } = useMutate({
  //   mutationKey: [MUTATE.GET_PRESIGNED_URL],
  //   mutationFn: getPresignedUrlFromS3,
  // });

  // const { mutateAsync: uplpadToS3Async } = useMutate({
  //   mutationKey: [MUTATE.UPLOAD_FILE_S3],
  //   mutationFn: uploadToS3,
  // });

  const handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const newFile = event.target.files?.[0];
    if (newFile) {
      const formData = new FormData();
      formData.append('File', newFile);
      // formData.append('Type', 'xlsx');
      if (props.folder) {
        formData.append('Folder', props.folder);
      }
      const objFile =  await uploadMultipartPresigned(newFile, props.folder ?? 'upload');
      if (objFile !== undefined){
        props.table.options.meta?.updateRowValues(
          {
            ...props.row.original,
            name: objFile.name,
            folder: objFile.src,
            fileName: objFile.fileName,
            host: objFile.host,
            size: objFile.size,
          },
          props.row.index
        );
      }
      
      /// comment code fix error upload large file
      // uploadFileHandler(
      //   getPresignedUrlAsync,
      //   uplpadToS3Async,
      //   uploadMultiAsync,
      //   newFile,
      //   props.folder ?? 'upload',
      //   props,
      //   formData
      // );
    }
  };

  return (
    <div className="flex items-center justify-between gap-2">
      <div className="flex items-center gap-x-1">
        <Button variant="ghost" size="icon" onClick={handleReplaceClick}>
          <Paperclip className="h-4 w-4" />
        </Button>
        <span className="truncate text-t12">{props.getValue<string>()}</span>
      </div>
      <Button variant="ghost" size="icon" onClick={handleDownload}>
        <Download className="h-4 w-4" />
      </Button>
      <input
        type="file"
        ref={fileInputRef}
        style={{ display: 'none' }}
        onChange={handleFileChange}
      />
    </div>
  );
};

/**
 * Editable download cell
 */
const EditableDownFileCell = <TData extends RecordAttachmentFile>(
  props: CellContext<TData, unknown> & { folder?: string }
) => {
  // const fileInputRef = useRef<HTMLInputElement>(null);

  const { mutateAsync: getDownloadOnetimeTokenAsync } = useMutate({
    mutationKey: [MUTATE.GET_DOWNLOAD_ONE_TIME_TOKEN],
    mutationFn: getDownloadOnetimeToken,
  });

  const { mutateAsync: s3DownloadFileAsync } = useMutate({
    mutationKey: [MUTATE.DOWNLOAD_FROM_S3],
    mutationFn: s3DowndloadFile,
  });

  const handleDownload = () => {
    const { original } = props.row;
    s3DowndloadFileHandler(getDownloadOnetimeTokenAsync, s3DownloadFileAsync, original);
  };

  // const handleReplaceClick = () => {
  //   fileInputRef.current?.click();
  // };

  const { mutateAsync: uploadMultiAsync } = useMutate({
    mutationKey: [MUTATE.UPLOAD_FILE],
    mutationFn: uploadMulti,
  });

  const { mutateAsync: getPresignedUrlAsync } = useMutate({
    mutationKey: [MUTATE.GET_PRESIGNED_URL],
    mutationFn: getPresignedUrlFromS3,
  });

  const { mutateAsync: uplpadToS3Async } = useMutate({
    mutationKey: [MUTATE.UPLOAD_FILE_S3],
    mutationFn: uploadToS3,
  });

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newFile = event.target.files?.[0];
    if (newFile) {
      const formData = new FormData();
      formData.append('File', newFile);
      // formData.append('Type', 'xlsx');
      if (props.folder) {
        formData.append('Folder', props.folder);
      }

      uploadFileHandler(
        getPresignedUrlAsync,
        uplpadToS3Async,
        uploadMultiAsync,
        newFile,
        props.folder ?? 'upload',
        props,
        formData
      );
    }
  };

  return (
    <div className="flex items-center justify-between gap-2">
      <div className="flex items-center gap-x-1">
        <span className="ml-4 truncate text-t12">{props.getValue<string>()}</span>
      </div>
      {props.folder && (
        <Button variant="ghost" size="icon" onClick={handleDownload}>
          <Download className="h-4 w-4" />
        </Button>
      )}
      <input
        type="text"
        // ref={fileInputRef}
        style={{ display: 'none' }}
        onChange={handleFileChange}
      />
    </div>
  );
};

export {
  EditableDatePickerCell,
  EditableDownFileCell,
  EditableDropdownCell,
  EditableFileCell,
  EditableInputCell
};

