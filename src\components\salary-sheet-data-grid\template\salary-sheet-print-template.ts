export const salarySheetPrintTemplate = `
<style>
/* <PERSON><PERSON>n chỉnh phần tiêu đề trên cùng */
.header-container { display: flex; justify-content: space-between; margin-bottom: 20px; }
.header-left { width: fit-content; }
.header-right { width: fit-content; }
.header-left h3,
.header-right h3 { margin: 0; }
.header-left { text-transform: uppercase; }
.header-right { text-transform: uppercase; }

/* Tiêu đề bảng lương ở giữa trang */
.main-title { text-align: center; margin-bottom: 30px; }
.main-title h2 { margin: 0; text-transform: uppercase; font-weight: bold; }
.main-title h4 { margin: 5px 0 0 0; font-weight: bold; }

/* Phần chữ ký */
.signature-section { width: 100%; display: flex; justify-content: space-between; text-align: center; }
.signature-box { width: 30%; }

/* Canh giữa chữ ký và tên */
.signature-box p { margin: 80px 0 0 0; /* <PERSON><PERSON><PERSON> khoảng trống để ký tên trước khi ghi họ tên */ }

.date-section { width: 100%; display: flex; justify-content: space-between; text-align: center; }
.date-box { width: 30%; }
.date-place { text-align: right; font-style: italic; }
.summary-place { text-align: left; font-weight: bold; font-style: italic; }
hr.header-line { border: 0; height: 0.005rem; background: #333; width: 180px }
</style>
<div class="print-content" style="font-family: &quot;Times New Roman&quot;; serif;">
  <!-- PHẦN HEADER (TRÁI & PHẢI) -->
  <div class="header-container">
    <div class="header-left">
      <div style="width: 250px; text-align: center;">
        <h3 style="font-weight: normal;">UBND HUYỆN HÓC MÔN</h3>
        <h3 style="font-weight: bold;">BAN QUẢN LÝ DỰ ÁN</h3>
        <h3 style="font-weight: bold;">ĐẦU TƯ XÂY DỰNG KHU VỰC</h3>
        <hr style="margin-left:auto; margin-right: auto;"class="header-line">
      </div>
    </div>
    <div class="header-right">
      <div style="width: 350px; text-align: center; margin-left: auto; margin-right: 0">
        <h3 style="font-weight: bold;">CỘNG HÒA XÃ HỘI CHỦ NGHĨA VIỆT NAM</h3>
        <h3 style="font-weight: bold; font-size: 12px">Độc lập - Tự do - Hạnh phúc</h3>
        <hr style="margin-left:auto; margin-right: auto;"class="header-line">
      </div>
    </div>
  </div>

  <!-- TIÊU ĐỀ CHÍNH -->
  <div class="main-title">
    <h2>{{ headerTitle }}</h2>
    <h4>Tháng {{ applicableMonth or '....' }} năm {{ applicableYear or '....' }}</h4>
  </div>
  <table border="1" style="width: 100%">
  {# Render Table Header #}
  {% if headerRows.length > 0 %}
  <thead>
    {% for row in headerRows %}
      <tr>
        {% for cell in row %}
          {% if cell.colspan > 0 %}
            <th
              {% if cell.colspan > 1 %}colspan="{{ cell.colspan }}"{% endif %}
              {% if cell.rowspan > 1 %}rowspan="{{ cell.rowspan }}"{% endif %}
            >
              {{ cell.caption }}
            </th>
          {% endif %}
        {% endfor %}
      </tr>
    {% endfor %}
  </thead>
  {% endif %}

  {# Render Table Body #}
  {% if processedRows.length > 0 %}
  <tbody>
    {% for row in processedRows %} {# Loop through preprocessed rows #}
    {% set rowIndex = loop.index %} {# Get the current row index #}
      <tr>
        {% for cell in row.cells %} {# Loop through the ordered visible cells for this row #}
          <td style="text-align: {{ cell.column.alignment or 'left' }}; padding: 5px;"> {# Use preprocessed cell data #}
            {% if cell.column.caption === 'STT'  %}
              {{ rowIndex }}
            {% else %}
              {{ cell.text }}
            {% endif %}
          </td>
        {% endfor %}
      </tr>
    {% endfor %}
  </tbody>
  {% else %}
   <tbody>
     <tr>
       <td style="text-align: center;" colspan="{{ visibleLeafColumns.length or 1 }}">Không có dữ liệu</td>
     </tr>
   </tbody>
  {% endif %}

  {# Render Table Footer #}
  {% if visibleLeafColumns.length > 0 %}
  <tfoot>
    <tr>
      {% for columnDef in visibleLeafColumns %} {# Use visibleLeafColumns for footer structure #}
        <td style="text-align: right; font-weight: bold; padding: 5px;"> {# Example styling for summary #}
            {{ columnDef.totalSummaryValue }}
        </td>
      {% endfor %}
    </tr>
  </tfoot>
  {% endif %}
  </table>
  <!-- Dòng tổng cộng -->
  <div class="summary-place">
    <p style="margin-bottom: 0px;">Số tiền viết bằng chữ: {{ summaryText }} đồng</p>
  </div>
  <!-- Ngày tháng và chữ ký -->
  <div class="date-section">
    <div class="date-box"></div>
	<div class="date-box"></div>
	<div class="date-box" style="font-style: italic;">Hóc Môn, ngày {{ now().getDate() }} tháng {{ now().getMonth() + 1 }} năm {{ now().getFullYear() }}</div>
  </div>
  <div class="signature-section">
    <div class="signature-box">
      <strong>NGƯỜI LẬP BẢNG</strong>
      <p>LƯƠNG CẨM TÚ</p>
    </div>
    <div class="signature-box">
      <strong>KẾ TOÁN TRƯỞNG</strong>
      <p>PHAN THỊ BÍCH LOAN</p>
    </div>
    <div class="signature-box">
      <strong>GIÁM ĐỐC</strong>
      <p>TRẦN VĂN SỸ</p>
    </div>
  </div>
</div>
`;
