import { useMemo } from 'react';
import { useLocation } from 'react-router-dom';

export const useBasePath = () => {
  const location = useLocation();
  const basePath = useMemo(() => {
    const paths = location.pathname.split('/');
    if (paths[paths.length - 1] === 'new' || Number(paths[paths.length - 1])) {
      return paths.slice(0, -1).join('/');
    }
    return location.pathname;
  }, [location]);
  return basePath;
};
