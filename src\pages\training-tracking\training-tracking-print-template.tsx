export const trainingTrackingPrintTemplate = `<style>
/* <PERSON>ăn chỉnh phần tiêu đề trên cùng */
.header-container { display: flex; justify-content: space-between; margin-bottom: 20px; }
.header-left { width: fit-content; }
.header-right { width: fit-content; }
.header-left h3,
.header-right h3 { margin: 0; }
.header-left { text-transform: uppercase; }
.header-right { text-transform: uppercase; }

/* Tiêu đề ở giữa trang */
.main-title { text-align: center; margin-bottom: 30px; }
.main-title h2 { margin: 0; text-transform: uppercase; font-weight: bold; }
.main-title h4 { margin: 5px 0 0 0; font-weight: bold; }

/* Phần chữ ký */
.signature-section { width: 100%; display: flex; justify-content: space-between; text-align: center; }
.signature-box { width: 30%; }

/* Canh giữa chữ ký và tên */
.signature-box p { margin: 80px 0 0 0; /* <PERSON><PERSON><PERSON> khoảng trống để ký tên trước khi ghi họ tên */ }

.date-section { width: 100%; display: flex; justify-content: space-between; text-align: center; }
.date-box { width: 30%; }
.date-place { text-align: right; font-style: italic; }
.summary-place { text-align: left; font-weight: bold; }
hr.header-line { border: 0; height: 0.005rem; background: #333; width: 180px }
</style>
<div class="print-content" style="font-family: &quot;Times New Roman&quot;; serif;">

  <!-- TIÊU ĐỀ CHÍNH -->
  <div class="main-title">
    <h2>LỚP BỒI DƯỠNG NĂM {{context.year}}</h2>
  </div>
  <table border="1" style="width: 100%; margin-right: auto; margin-left: auto; border-collapse: collapse; border: 1px solid black;">
  <thead>
      <tr>
        <th style="width: 5%">STT</th>
        <th style="width: 10%">Ngày mở lớp</th>
        <th style="width: 20%">Cơ sở đào tạo</th>
        <th colspan="2" style="width: 20%">Tên lớp học</th>
        <th style="width: 5%">Số lượng</th>

        <th style="width: 20%">Tên cán bộ tham dự</th>
        <th style="width: 20%">Ghi chú</th>
      </tr>
  </thead>
  {# Render Table Body #}
  {% if context.trainingTrackingDetails.length > 0 %}
  <tbody>
    {% for item in context.trainingTrackingDetails %}
      <tr>
        <td style="text-align: center;">{{ loop.index }}</td>
        <td style="padding-left: 10px; padding-right: 10px">{{ item.classStartDate }}</td>
        <td style="padding-left: 10px; padding-right: 10px">{{ item.trainingInstitutionName }}</td>
        <td colspan="2" style="padding-left: 10px; padding-right: 10px">{{ item.roomName }}</td>
        <td style="text-align: center;">{{ item.quantityMember }}</td>

        <td style="padding-left: 10px; padding-right: 10px">
          {% if item.memberNames %}
            {{ item.memberNames }}
          {% endif %}
        </td>

        <td style="padding-left: 10px; padding-right: 10px">{{ item.note }}</td>
      </tr>
    {% endfor %}
  </tbody>
  {% else %}
   <tbody>
     <tr>
       <td colspan="6" style="text-align: center;">Không có dữ liệu</td>
     </tr>
   </tbody>
  {% endif %}
  </table>
  <!-- Dòng tổng cộng -->
  <div class="summary-place">
    {% if context.note.length > 0 %}
      <p>Ghi chú:</p>
      {% for note in context.notes %}
        <p style="margin-bottom: 0px; text-indent:18.35pt;">{{ note }}</p>
      {% endfor %}
    {% endif %}
  </div>
  <div class="signature-section" style="padding-top: 15px">
    <div class="signature-box">
      <strong></strong>
      <p></p>
    </div>
    <div class="signature-box">
      <strong></strong>
      <p></p>
    </div>
   
  </div>
</div>`;
