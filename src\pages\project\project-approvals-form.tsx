import { BasicDialog } from '@/components/basic-dialog';
import {
  DataTable,
  DataTableRowActions,
  EditableDatePickerCell,
  EditableDropdownCell,
  EditableInputCell,
  EditableTextArea,
} from '@/components/data-table';
import { ImportExcelConfigForm } from '@/components/import-excel-config-form';
import { PageLayout } from '@/components/page-layout';
import { AGENCY_TYPE, PROFESSIONS, QUERIES, TABLES } from '@/constant';
import { useBoolean, useEntity } from '@/hooks';
import {
  Agency,
  defaultValuesProjectApproval,
  Project,
  ProjectApproval,
  ProjectTabChildrenProps,
} from '@/types';
import { CellContext, ColumnDef, Table } from '@tanstack/react-table';
import { Button } from 'devextreme-react';
import { SyntheticEvent, useMemo } from 'react';
import { useFormContext, useWatch } from 'react-hook-form';
import { getRandomNumber } from '@/lib/number';

const defaultRow = defaultValuesProjectApproval;

export const ProjectApprovalsForm = ({
  role,
  loading,
  onBackToList,
  onCreateNew,
  onSubmit,
  t,
}: ProjectTabChildrenProps) => {
  const { state: isImportFormOpen, toggle: toggleImportForm } = useBoolean(false);
  const { control, setValue } = useFormContext<Project>();
  const [editableData, projectId] = useWatch({
    control,
    name: ['projectApprovals', 'id'],
  });

  const { fetch: fetchCostItems } = useEntity({
    queryKey: [QUERIES.COST_ITEM],
    model: 'cost-item',
  });

  const columns: ColumnDef<ProjectApproval>[] = useMemo(
    () => [
      {
        id: 'approvalDate',
        accessorKey: 'approvalDate',
        header: t('fields.projectApprovals.approvalDate'),
        cell: (props: CellContext<ProjectApproval, unknown>) => (
          <EditableDatePickerCell {...props} />
        ),
      },
      {
        id: 'approvalNumber',
        accessorKey: 'approvalNumber',
        header: t('fields.projectApprovals.approvalNumber'),
        cell: props => <EditableInputCell {...props} type="text" />,
      },
      {
        id: 'agencyId',
        accessorKey: 'agencyId',
        header: t('fields.projectApprovals.agencyId'),
        cell: props => (
          <EditableDropdownCell<ProjectApproval, Agency>
            {...props}
            model="agency"
            queryKey={[QUERIES.AGENCY]}
            filter={item => item.agencyType === AGENCY_TYPE.ISSUE}
          />
        ),
      },
      {
        id: 'approvalContent',
        accessorKey: 'approvalContent',
        header: t('fields.projectApprovals.approvalContent'),
        cell: props => <EditableTextArea {...props} />,
      },
      {
        id: 'removeRow',
        header: ' ',
        size: 10,
        cell: props => {
          return (
            <DataTableRowActions
              onDelete={() => {
                props.table.options.meta?.removeRowByIndex(props.row.index);
              }}
              canDelete={role?.isCreate || role?.isUpdate}
            />
          );
        },
      },
    ],
    [t, role?.isCreate, role?.isUpdate]
  );

  const columnsForImportConfig = columns.map(column => {
    return {
      field: column.id,
      header: column.header as string,
    };
  });

  const handleAddNewRow = (table: Table<ProjectApproval>) => {
    const newRow = { ...defaultRow, projectId, id: -getRandomNumber() };
    table.options.meta?.addNewRow(newRow);
  };

  return (
    <PageLayout
      onSaveChange={e => {
        const target = e.event?.currentTarget as unknown as SyntheticEvent<HTMLElement>;
        onSubmit(target);
      }}
      canSaveChange={role?.isUpdate}
      isSaving={loading}
      onCancel={onBackToList}
      customElementLeft={
        <>
          <Button
            text={t('content.createNew', { ns: 'common' })}
            className="uppercase"
            stylingMode="outlined"
            type="default"
            icon="plus"
            onClick={onCreateNew}
          />
        </>
      }
      contentClassName="!h-[calc(100vh-220px)]"
    >
      <div className="grid grid-cols-1 gap-x-8 gap-y-4 xl:grid-cols-24">
        <p className="col-span-1 font-semibold xl:col-span-24">Thiết lập căn cứ quyết định</p>
        <div className="col-span-1 xl:col-span-24">
          <DataTable
            role={role}
            showPagination={false}
            editableData={editableData || []}
            tableId={TABLES.PROJECT_APPROVALS}
            syncQueryParams={false}
            initialState={{
              columnVisibility: {
                costItemId: true,
                symbol: true,
                percentageRate: true,
                calculationMethod: true,
                preTaxValue: true,
                vat: true,
                vatTax: true,
                postTaxValue: true,
              },
            }}
            setEditableData={editedData => {
              setValue('projectApprovals', editedData);
            }}
            columns={columns}
            onAddButtonClick={handleAddNewRow}
          />
        </div>
      </div>
      <BasicDialog
        open={isImportFormOpen}
        title="Import Excel"
        toggle={toggleImportForm}
        className="max-w-[100vw] md:max-w-[90vw]"
      >
        <ImportExcelConfigForm<ProjectApproval>
          onApply={data => {
            setValue(
              'projectApprovals',
              data.map(item => {
                return {
                  ...defaultRow,
                  ...item,
                  projectId,
                };
              })
            );
            toggleImportForm();
          }}
          importModel="project"
          onClose={toggleImportForm}
          professionType={PROFESSIONS.PROJECT}
          professionColumns={columnsForImportConfig}
          onImported={() => {
            fetchCostItems({});
          }}
          additionalFormValues={[{ key: 'refId', value: projectId?.toString() || '' }]}
        />
      </BasicDialog>
    </PageLayout>
  );
};
