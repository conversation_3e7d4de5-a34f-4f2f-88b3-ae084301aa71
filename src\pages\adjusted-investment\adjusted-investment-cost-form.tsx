import { BasicDialog } from '@/components/basic-dialog';
import {
  DataTable,
  DataTableRowActions,
  EditableDropdownCell,
  EditableInputCell,
} from '@/components/data-table';
import { customizeNumberCell } from '@/components/devex-data-grid';
import { ImportExcelConfigForm } from '@/components/import-excel-config-form';
import { FormCombobox, FormField, FormLabel } from '@/components/ui/form';
import { InputNumber } from '@/components/ui/input';
import {
  downloadTemplateLabel,
  enterLabel,
  MUTATE,
  PERMISSIONS,
  PROFESSIONS,
  QUERIES,
  selectLabel,
  TABLES,
} from '@/constant';
import { useAuth, useBoolean, useEntity, usePermission } from '@/hooks';
import { getRandomNumber } from '@/lib/number';
import { displayExpr } from '@/lib/utils';
import { createQueryByIdFn } from '@/services';
import {
  AdjustedInvestment,
  AdjustedInvestmentDetail,
  AdjustedInvestmentDetailSummary,
  CostItem,
  CostItemType,
  defaultValuesAdjustedInvestment,
  Project,
} from '@/types';
import { useQuery } from '@tanstack/react-query';
import { CellContext, ColumnDef, RowSelectionState } from '@tanstack/react-table';
import { Button, DataGrid, DateBox, SelectBox, TextBox } from 'devextreme-react';
import { Column, Lookup, Summary, TotalItem } from 'devextreme-react/data-grid';
import { Dispatch, SetStateAction, useEffect, useMemo, useState } from 'react';
import { useFormContext, useWatch } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { calculatePostTaxValues, calculateTaxValues } from '../project';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { RecordEditableTable } from '@/components/records-attachment';

const [defaultRow] = defaultValuesAdjustedInvestment.adjustedInvestmentDetails;

export const AdjustedInvestmentCostForm = ({
  onTimeChange,
  setIsLoadingProject,
}: {
  onTimeChange: (value: unknown) => void;
  setIsLoadingProject: Dispatch<SetStateAction<boolean>>;
}) => {
  const role = usePermission(PERMISSIONS.ADJUSTED_INVESTMENT);
  const { t } = useTranslation('adjustedInvestment');

  const { projects } = useAuth();

  const { state: isImportFormOpen, toggle: toggleImportForm } = useBoolean(false);
  const { control, setValue, getValues } = useFormContext<AdjustedInvestment>();

  const [editableData, selectedProjectId, id] = useWatch({
    control,
    name: ['adjustedInvestmentDetails', 'projectId', 'id'],
  });

  const [editableSummaries, setEditableSummaries] = useState<AdjustedInvestmentDetailSummary[]>([]);

  const { fetch: fetchCostItems, list: costItems } = useEntity<CostItem>({
    queryKey: [QUERIES.COST_ITEM],
    model: 'cost-item',
  });

  const { list: costItemTypes, isFetched: costItemTypesFetched } = useEntity<CostItemType>({
    queryKey: [QUERIES.COST_ITEM_TYPE],
    model: 'cost-item-type',
  });

  // Sticky state pattern để giữ giá trị khi chuyển tab
  const [costItemTypesIsEditVat, setCostItemTypesIsEditVat] = useState<number[]>([]);

  // Tính toán giá trị mới từ costItemTypes
  const newCostItemTypesIsEditVat = useMemo(() => {
    if (costItemTypes && Array.isArray(costItemTypes)) {
      return costItemTypes.filter(item => item.isEditVat).map(item => item.id);
    }
    return null;
  }, [costItemTypes, costItemTypesFetched]);

  // Chỉ cập nhật state khi có giá trị mới thực sự
  useEffect(() => {
    if (newCostItemTypesIsEditVat !== null) {
      setCostItemTypesIsEditVat(newCostItemTypesIsEditVat);
    }
  }, [newCostItemTypesIsEditVat]);

  const [userCreatedName, approvalProcessName] = getValues([
    'userCreatedName',
    'approvalProcessName',
  ]);
  const AdjustedInvestmentVatTaxCell = ({
    ...props
  }: CellContext<AdjustedInvestmentDetail, unknown> & {
    isPmDirector?: boolean;
    isEditVat: boolean;
    updateSummary?: (
      costItemTypeId: number | null | undefined,
      row: AdjustedInvestmentDetail | null,
      data: AdjustedInvestmentDetail[]
    ) => void;
  }) => {
    const [isUserInput, setIsUserInput] = useState(false);

    return (
      <EditableInputCell
        type="number"
        isMoney
        readOnly={!props.isEditVat}
        onFocus={() => setIsUserInput(true)}
        onBlur={() => setIsUserInput(false)}
        onValueChange={value => {
          if (!isUserInput) return; // Chỉ thực hiện tính toán khi người dùng đang nhập

          const vatTax = Number(value);
          const { original } = props.row;
          if (!original) return;

          const currentPreTaxValue = original.preTaxValue ?? 0;

          // Tính toán vat từ vatTax và preTaxValue
          const postTaxValue = calculatePostTaxValues(vatTax, currentPreTaxValue);

          const updatedRow = {
            ...original,
            ...postTaxValue,
          };

          props.table.options.meta?.updateRowValues(updatedRow, props.row.index);
        }}
        {...props}
      />
    );
  };

  //update bảng tổng
  useEffect(() => {
    if (editableSummaries.length === 0 && costItemTypes) {
      const summary: AdjustedInvestmentDetailSummary[] = costItemTypes
        .map(costItemType => {
          return {
            id: -getRandomNumber(),
            costItemTypeId: costItemType.id,
            costItemValue: 0,
            sort: costItemType.sort,
          };
        })
        .sort((a, b) => (a?.sort ?? 0) - (b?.sort ?? 0));
      setEditableSummaries(summary);
    }

    const data = editableData;
    const costItemMap = new Map<number, number>(); //costItemType:Value

    data.forEach(row => {
      const costItem = costItems.find(item => item.id === row.costItemId);
      if (!costItem) return;
      const rowCostItemTypeId = costItem.costItemTypeId;
      if (rowCostItemTypeId === null || rowCostItemTypeId === undefined) {
        return;
      }
      const value = row.postTaxValue ?? 0;
      costItemMap.set(rowCostItemTypeId, (costItemMap.get(rowCostItemTypeId) ?? 0) + value);
    }, {});

    setEditableSummaries(old =>
      old.map(item => {
        return {
          ...item,
          costItemValue: item.costItemTypeId ? costItemMap.get(item.costItemTypeId) ?? 0 : 0,
        };
      })
    );
  }, [editableData, costItemTypes, costItems, setEditableSummaries, editableSummaries.length]);

  //update Tổng mức đầu tư điều chỉnh
  useEffect(() => {
    const totalCost = editableSummaries.reduce((sum, item) => sum + (item.costItemValue || 0), 0);
    setValue('totalPostTaxValue', totalCost);
  }, [editableSummaries, setValue]);

  const columns: ColumnDef<AdjustedInvestmentDetail>[] = useMemo(
    () => [
      {
        id: 'costItemId',
        accessorKey: 'costItemId',
        header: t('fields.adjustedInvestmentDetails.costItemId'),
        cell: props => {
          return (
            <EditableDropdownCell<AdjustedInvestmentDetail, CostItem>
              {...props}
              model="cost-item"
              defaultText={props.row.original.costItemName}
              queryKey={[QUERIES.COST_ITEM]}
              onSelectItem={item => {
                if (!item) return;

                const data = props.table.options.data;
                data[props.row.index] = {
                  ...props.row.original,
                  [props.column.id]: item.id,
                  costItemCostItemTypeId: item.costItemTypeId,
                };

                if (data[data.length - 1].costItemId !== 0) {
                  data.push({ ...defaultRow, id: -getRandomNumber() });
                }

                setValue('adjustedInvestmentDetails', data);
              }}
            />
          );
        },
      },
      {
        id: 'symbol',
        accessorKey: 'symbol',
        header: t('fields.adjustedInvestmentDetails.symbol'),
        cell: EditableInputCell,
      },
      {
        id: 'percentageRate',
        accessorKey: 'percentageRate',
        header: t('fields.adjustedInvestmentDetails.percentageRate'),
        cell: props => <EditableInputCell {...props} type="text" />,
      },
      {
        id: 'calculationMethod',
        accessorKey: 'calculationMethod',
        header: t('fields.adjustedInvestmentDetails.calculationMethod'),
        cell: EditableInputCell,
      },
      {
        id: 'preTaxValue',
        accessorKey: 'preTaxValue',
        header: t('fields.adjustedInvestmentDetails.preTaxValue'),
        cell: props => (
          <EditableInputCell
            {...props}
            type="number"
            isMoney
            onValueChange={value => {
              const preTaxValue = Number(value);
              const { original } = props.row;
              if (!original) return;

              const calculatedTaxValues = calculateTaxValues(preTaxValue, original.vat ?? 8);
              const updatedRow = {
                ...props.row.original,
                ...calculatedTaxValues,
              };
              props.table.options.meta?.updateRowValues(updatedRow, props.row.index);

              // updateSummary(calculatedTaxValues.postTaxValue, props);
            }}
          />
        ),
      },
      {
        id: 'vat',
        accessorKey: 'vat',
        header: t('fields.adjustedInvestmentDetails.vat'),
        cell: props => (
          <EditableInputCell
            {...props}
            type="number"
            hideDecimal
            onValueChange={value => {
              const vat = Number(value);
              const { original } = props.row;
              if (!original) return;

              const calculatedTaxValues = calculateTaxValues(original.preTaxValue || 0, vat ?? 8);
              const updatedRow = {
                ...props.row.original,
                ...calculatedTaxValues,
              };
              props.table.options.meta?.updateRowValues(updatedRow, props.row.index);

              // updateSummary(props);
            }}
          />
        ),
      },
      {
        id: 'vatTax',
        accessorKey: 'vatTax',
        header: t('fields.adjustedInvestmentDetails.vatTax'),
        cell: props => {
          const { original } = props.row;
          let isEditVat = false;
          if (original.costItemCostItemTypeId) {
            isEditVat = costItemTypesIsEditVat.includes(original.costItemCostItemTypeId);
          }
          return <AdjustedInvestmentVatTaxCell {...props} isEditVat={isEditVat} />;
        },
        // cell: props => <EditableInputCell {...props} type="number" disabled hideDecimal />,
      },
      {
        id: 'postTaxValue',
        accessorKey: 'postTaxValue',
        header: t('fields.adjustedInvestmentDetails.postTaxValue'),
        cell: props => <EditableInputCell {...props} type="number" disabled hideDecimal />,
      },
      {
        id: 'removeRow',
        header: ' ',
        size: 10,
        cell: props => {
          return (
            <DataTableRowActions
              onDelete={() => {
                props.table.options.meta?.removeRowByIndex(props.row.index);
              }}
              canDelete={role?.isCreate || role?.isUpdate}
            />
          );
        },
      },
    ],
    [role?.isCreate, role?.isUpdate, setValue, t, costItemTypesIsEditVat]
  );

  const columnsForImportConfig = columns.map(column => {
    return {
      field: column.id,
      header: column.header as string,
    };
  });

  const [hasSelectedProject, setHasSelectedProject] = useState(false);

  const { isLoading: isLoadingProject, data } = useQuery({
    queryKey: [MUTATE.PROJECT, selectedProjectId],
    queryFn: () => createQueryByIdFn<Project>('project')(selectedProjectId),
    enabled: !!selectedProjectId && hasSelectedProject,
  });

  //set data
  useEffect(() => {
    if (!isLoadingProject && data && hasSelectedProject) {
      const project = data;
      setValue('documentCode', project.adjustedInvestmentDocumentCode || '');
      setValue('signingDate', project.adjustedInvestmentSigningDate || new Date());
      setValue('signerId', project.adjustedInvestmentSignerId || '');
      setValue('note', project.note || '');
      setValue('adjustedInvestmentDetails', [
        ...project.projectInvestmentDetails.map(item => ({
          id: item.id,
          adjustedInvestmentId: 0,
          costItemId: item.costItemId ?? 0,
          costItemName: item.costItemName,
          symbol: item.symbol,
          percentageRate: item.percentageRate,
          calculationMethod: item.calculationMethod,
          preTaxValue: item.preTaxValue ?? 0,
          vatTax: item.vatTax ?? 0,
          postTaxValue: item.postTaxValue ?? 0,
          vat: item.vat ?? 0,
        })),
        {
          ...defaultRow,
          id: -getRandomNumber(),
        },
      ]);
    }
  }, [setValue, data, isLoadingProject, hasSelectedProject]);

  //set isLoadingProject
  useEffect(() => {
    setIsLoadingProject(isLoadingProject);
  }, [isLoadingProject, setIsLoadingProject]);
  const [rowSelection, setRowSelection] = useState<RowSelectionState>({});

  return (
    <>
      <div className="grid grid-cols-1 gap-x-8 gap-y-4 lg:grid-cols-24">
        {/* dữ liệu nhập */}
        <div className="col-span-1 flex flex-col gap-x-8 gap-y-4 lg:col-span-18 2xl:col-span-12">
          {/* Dự án */}
          <div className="flex items-center">
            <FormLabel name="projectId" htmlFor="projectId" className="hidden w-[100px] md:block">
              {t('fields.projectId')}
            </FormLabel>
            <FormField
              id="projectId"
              name="projectId"
              className="min-w-0 flex-1 md:w-[250px]"
              label={t('fields.projectId')}
            >
              <SelectBox
                items={projects}
                searchExpr={['name', 'code']}
                valueExpr="id"
                onFocusIn={e => {
                  const input = e.element.querySelector(
                    'input.dx-texteditor-input'
                  ) as HTMLInputElement;
                  if (input) input.select();
                }}
                searchEnabled
                searchMode="contains"
                displayExpr={displayExpr(['name'])}
                showClearButton
                onSelectionChanged={() => setHasSelectedProject(true)}
                focusStateEnabled={false}
              />
            </FormField>
          </div>

          {/* chia thành 2 cột nhỏ */}
          <div className="grid grid-cols-1 gap-x-8 gap-y-4 lg:grid-cols-12">
            {/* cột 1 nhỏ*/}
            <div className="col-span-1 flex flex-col gap-x-8 gap-y-4 lg:col-span-6">
              {/* Số văn bản adjustedCostEstimationDocumentCode */}
              <div className="flex items-center">
                <FormLabel
                  name="documentCode"
                  htmlFor="documentCode"
                  className="hidden w-[100px] md:block"
                >
                  {t('fields.documentCode')}
                </FormLabel>
                <FormField
                  id="documentCode"
                  name="documentCode"
                  className="min-w-0 flex-1 md:w-[250px]"
                  label={t('fields.documentCode')}
                >
                  <TextBox placeholder={`${enterLabel} ${t('fields.documentCode')}`} />
                </FormField>
              </div>

              {/* Ngày ký adjustedInvestmentSigningDate */}
              <div className="flex items-center">
                <FormLabel
                  name="signingDate"
                  htmlFor="signingDate"
                  className="hidden w-[100px] md:block"
                >
                  {t('fields.signingDate')}
                </FormLabel>
                <FormField
                  id="signingDate"
                  name="signingDate"
                  className="min-w-0 flex-1 md:w-[250px]"
                  type="date"
                  label={t('fields.signingDate')}
                >
                  <DateBox
                    placeholder={`${selectLabel} ${t('fields.signingDate')}`}
                    pickerType="calendar"
                    focusStateEnabled={false}
                  />
                </FormField>
              </div>

              {/* Người ký adjustedInvestmentSignerId*/}
              <div className="flex items-center">
                <FormLabel name="signerId" htmlFor="signerId" className="hidden w-[100px] md:block">
                  {t('fields.signerId')}
                </FormLabel>
                <FormField
                  id="signerId"
                  name="signerId"
                  className="min-w-0 flex-1 md:w-[250px]"
                  label={t('fields.signerId')}
                >
                  <TextBox placeholder={`${enterLabel} ${t('fields.signerId')}`} />
                </FormField>
              </div>

              {/* Tổng mức đầu tư điều chỉnh */}
              <div className="flex items-center">
                <FormLabel
                  name="totalPostTaxValue"
                  htmlFor="totalPostTaxValue"
                  className="hidden w-[100px] md:block"
                  title={t('fields.totalPostTaxValue')}
                >
                  {t('fields.totalPostTaxValueShort')}
                </FormLabel>
                <FormField
                  id="totalPostTaxValue"
                  name="totalPostTaxValue"
                  className="min-w-0 flex-1 md:w-[250px]"
                  type="number"
                  label={t('fields.totalPostTaxValueShort')}
                >
                  <InputNumber
                    isMoney
                    placeholder={`${enterLabel} ${t('fields.totalPostTaxValue')}`}
                    readOnly
                  />
                </FormField>
              </div>
            </div>
            {/* cột 2 nhỏ */}
            <div className="col-span-1 flex flex-col gap-x-8 gap-y-4 lg:col-span-6">
              {/* Ngày lập */}
              <div className="flex items-center">
                <FormLabel
                  name="adjustedInvestmentTime"
                  htmlFor="adjustedInvestmentTime"
                  className="hidden w-[100px] md:block"
                >
                  {t('fields.adjustedInvestmentTime')}
                </FormLabel>
                <FormField
                  id="adjustedInvestmentTime"
                  name="adjustedInvestmentTime"
                  className="min-w-0 flex-1 md:w-[250px]"
                  type="date"
                  label={t('fields.adjustedInvestmentTime')}
                >
                  <DateBox
                    placeholder={`${selectLabel} ${t('fields.adjustedInvestmentTime')}`}
                    onChange={onTimeChange}
                    pickerType="calendar"
                    focusStateEnabled={false}
                  />
                </FormField>
              </div>

              {/* Người lập */}
              <div className="flex items-center">
                <FormLabel
                  name="userCreatedId"
                  htmlFor="userCreatedId"
                  className="hidden w-[100px] md:block"
                >
                  {t('fields.userCreatedId')}
                </FormLabel>
                <FormField
                  id="userCreatedId"
                  name="userCreatedId"
                  className="min-w-0 flex-1 md:w-[250px]"
                  label={t('fields.userCreatedId')}
                >
                  <FormCombobox
                    placeholder={`${selectLabel} ${t('fields.userCreatedId')}`}
                    defaultText={userCreatedName}
                    model="user"
                    queryKey={[QUERIES.USERS]}
                    disabled
                  />
                </FormField>
              </div>

              {/* Trạng thái projectStatusId */}
              <div className="flex items-center">
                <FormLabel name="statusId" htmlFor="statusId" className="hidden w-[100px] md:block">
                  {t('fields.statusId')}
                </FormLabel>
                <FormField
                  id="statusId"
                  name="statusId"
                  className="min-w-0 flex-1 md:w-[250px]"
                  label={t('fields.statusId')}
                >
                  <FormCombobox
                    placeholder={`${selectLabel} ${t('fields.statusId')}`}
                    model="status"
                    queryKey={[QUERIES.STATUS]}
                  />
                </FormField>
              </div>

              {/* Quy trình duyệt */}
              <div className=" hidden  items-center">
                <FormLabel
                  name="approvalProcessId"
                  htmlFor="approvalProcessId"
                  className="hidden w-[100px] md:block"
                >
                  {t('fields.approvalProcessId')}
                </FormLabel>
                <FormField
                  id="approvalProcessId"
                  name="approvalProcessId"
                  className="min-w-0 flex-1 md:w-[250px]"
                  label={t('fields.approvalProcessId')}
                >
                  <FormCombobox
                    placeholder={`${selectLabel} ${t('fields.approvalProcessId')}`}
                    defaultText={approvalProcessName}
                    model="approval-process"
                    queryKey={[QUERIES.APPROVAL_PROCESS]}
                  />
                </FormField>
              </div>
            </div>
          </div>

          {/* Ghi chú note */}
          <div className="flex items-center">
            <FormLabel name="note" htmlFor="note" className="hidden w-[100px] md:block">
              {t('fields.note')}
            </FormLabel>
            <FormField
              id="note"
              name="note"
              className="min-w-0 flex-1 md:w-[250px]"
              label={t('fields.note')}
            >
              <TextBox placeholder={`${enterLabel} ${t('fields.note')}`} />
            </FormField>
          </div>
        </div>
        {/* dữ liệu nhập + bảng tổng*/}
        <div className="col-span-1 flex flex-col gap-x-8 gap-y-4 lg:col-span-18 2xl:col-span-12">
          <DataGrid
            dataSource={editableSummaries}
            columnAutoWidth
            allowColumnResizing
            allowColumnReordering
            showBorders
            showColumnLines
            showRowLines
            width={'auto'}
            id={TABLES.PREPARATION_DETAIL_SUMMARY}
            className="column-header-wrap"
          >
            <Column dataField="sort" dataType="number" caption="STT" width={50} />
            <Column
              dataField="costItemTypeId"
              caption={t('fields.adjustedInvestmentDetailsSummaries.costItemTypeId')}
            >
              <Lookup
                dataSource={costItemTypes}
                valueExpr={'id'}
                displayExpr={displayExpr(['name'])}
              />
            </Column>
            <Column
              dataField="costItemValue"
              caption={t('fields.adjustedInvestmentDetailsSummaries.costItemValue')}
              dataType="number"
              alignment="right"
              customizeText={customizeNumberCell(0)}
            />
            <Summary>
              <TotalItem
                column="costItemValue"
                summaryType="sum"
                customizeText={customizeNumberCell(0)}
                displayFormat="{0}"
              />
            </Summary>
          </DataGrid>
        </div>
        {/* bảng chi tiết */}
        <div className="col-span-1 mt-8 flex flex-col gap-x-8 gap-y-4 lg:col-span-24">
          <Tabs defaultValue="detail">
            <div className="w-full">
              <TabsList>
                <TabsTrigger value="detail">{t('page.tabs.detail')}</TabsTrigger>
                <TabsTrigger value="attachment">{t('page.tabs.attachment')}</TabsTrigger>
              </TabsList>
            </div>
            <TabsContent value="detail" className="mt-4">
              <DataTable
                role={role}
                editableData={editableData}
                tableId={TABLES.PREPARATION_DETAIL}
                syncQueryParams={false}
                initialState={{
                  columnVisibility: {
                    costItemId: true,
                    symbol: true,
                    percentageRate: true,
                    calculationMethod: true,
                    preTaxValue: true,
                    vat: true,
                    vatTax: true,
                    postTaxValue: true,
                  },
                }}
                setEditableData={editedData => {
                  setValue('adjustedInvestmentDetails', editedData);
                }}
                onAddButtonClick={table => {
                  const newRow = {
                    ...defaultRow,
                    id: -getRandomNumber(),
                  };
                  table.options.meta?.addNewRow(newRow);
                }}
                customToolbar={() => {
                  return (
                    <>
                      <Button
                        stylingMode="text"
                        icon="upload"
                        text="Import Excel"
                        type="default"
                        onClick={toggleImportForm}
                      />
                      <Button
                        stylingMode="text"
                        icon="download"
                        text={downloadTemplateLabel}
                        type="default"
                        onClick={() => {
                          window.open(`/templates/project/mau-import-du-an.xlsx`);
                        }}
                      />
                    </>
                  );
                }}
                columns={columns}
              />
            </TabsContent>
            <TabsContent value="attachment" className="mt-4">
              <RecordEditableTable
                role={role}
                rowSelection={rowSelection}
                setRowSelection={setRowSelection}
                folder="adjusted-investment"
                profession={PROFESSIONS.ADJUSTED_INVESTMENT}
              />
            </TabsContent>
          </Tabs>
        </div>
      </div>
      <BasicDialog
        open={isImportFormOpen}
        title="Import Excel"
        toggle={toggleImportForm}
        className="max-w-[100vw] md:max-w-[90vw]"
      >
        <ImportExcelConfigForm<AdjustedInvestmentDetail>
          onApply={data => {
            setValue(
              'adjustedInvestmentDetails',
              data.map(item => ({
                ...defaultRow,
                ...item,
                ...calculateTaxValues(item.preTaxValue || 0, item.vat || 8),
                adjustedInvestmentId: id,
              }))
            );
            toggleImportForm();
          }}
          importModel="adjusted-investment"
          onClose={toggleImportForm}
          professionType={PROFESSIONS.PROJECT}
          professionColumns={columnsForImportConfig}
          onImported={() => {
            fetchCostItems({});
          }}
          additionalFormValues={[{ key: 'refId', value: selectedProjectId?.toString() || '' }]}
        />
      </BasicDialog>
    </>
  );
};
