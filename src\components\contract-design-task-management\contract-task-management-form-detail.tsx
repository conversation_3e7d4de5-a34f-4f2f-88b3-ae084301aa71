import { <PERSON><PERSON>ombob<PERSON>, <PERSON><PERSON>ield, FormLabel } from '@/components/ui/form';
import { ACTION_TYPES, enterLabel, QUERIES, selectLabel } from '@/constant';
import {
  Contract,
  ContractTaskManagement,
  ContractTaskManagementPlanningAndFinanceDetail,
  defaultContractTaskManagementPlanningAndFinanceDetail,
  defaultValuesContractTaskManagement,
} from '@/types';
import { DateBox, TextArea } from 'devextreme-react';
import Button from 'devextreme-react/button';
import { SyntheticEvent, useCallback, useMemo } from 'react';
import { BasicDialog } from '@/components/basic-dialog';
import { InputNumber } from '@/components/ui/input';
import { getRandomNumber } from '@/lib/number';
import { useTranslation } from 'react-i18next';
import { UseFormGetValues, UseFormSetValue } from 'react-hook-form';
import { useAuth, useDataTable, useSendNotification } from '@/hooks';
import { createQueryByIdFn, Model } from '@/services';
import { modelToCamelCase } from '@/lib/utils';
import { PeriodFilter } from '@/components/period-filter-form';
import {
  ContractTaskManagementResponse,
  getApproveTimeText,
  getNotifyContent,
  getOpinionTimeText,
} from './utils';
import { useLocation } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
const ActionTypeCombobox = ({
  ns,
  isDisable,
  fieldName,
}: {
  isDisable: boolean;
  fieldName: string;
  ns: string;
}) => {
  const { t } = useTranslation([ns]);
  return (
    <>
      <FormLabel name={fieldName} htmlFor={fieldName} className="hidden w-[100px] md:block">
        {t('fields.actionType')}
      </FormLabel>
      <FormField
        label={`${t('fields.actionType')}`}
        id={fieldName}
        name={fieldName}
        className="min-w-0 flex-1 md:w-[250px]"
      >
        <FormCombobox
          options={ACTION_TYPES}
          queryKey={['ACTION_TYPES']}
          placeholder={`${selectLabel} ${t('fields.actionType')}`}
          disabled={isDisable}
        />
      </FormField>
    </>
  );
};

type ContractTaskManagementFormDetailProps = {
  model: Model;
  item: ContractTaskManagementPlanningAndFinanceDetail;
  index: number;
  canSave: boolean;
  canApproveOrReject: boolean;
  isApprover: boolean;
  isSubmitted: boolean;
  isApprovedOrRejected: boolean;
  fieldsDepartmentDetails: ContractTaskManagementPlanningAndFinanceDetail[];
  getValues: UseFormGetValues<ContractTaskManagement>;
  setValue: UseFormSetValue<ContractTaskManagement>;
  handleSubmitPromise: (e?: SyntheticEvent<HTMLElement>) => Promise<ContractTaskManagement>;
  listName: keyof ContractTaskManagement;
};
export const ContractTaskManagementFormDetail = ({
  model,
  item,
  index,
  canSave,
  canApproveOrReject,
  isApprover,
  isSubmitted,
  isApprovedOrRejected,
  fieldsDepartmentDetails,
  getValues,
  setValue,
  handleSubmitPromise,
  listName,
}: ContractTaskManagementFormDetailProps) => {
  const { isEditDialogOpen: isEditDialogOpen, toggleEditDialog: toggleEditDialog } = useDataTable<
    ContractTaskManagement,
    PeriodFilter
  >({});
  const path = useLocation().pathname.replace(/\/(\d+|new)\/?$/, '');
  const rowTemplate = defaultContractTaskManagementPlanningAndFinanceDetail;
  const { ns, notifyNew } = useMemo(() => {
    const ns = modelToCamelCase(model);

    const notifyNew = ns + '.new';
    const notifyAddNew = ns + '.addNew';
    const notifyInfo = ns + '.info';
    const notifyContent = ns + '.content';

    return {
      ns,
      notifyNew,
      notifyAddNew,
      notifyInfo,
      notifyContent,
    };
  }, [model]);

  const { t } = useTranslation([ns]);
  const { sendNotify } = useSendNotification();
  const { user } = useAuth();
  const addNewRowProjectDepartment = () => {
    const newDepartment = {
      ...defaultValuesContractTaskManagement.contractTaskManagementProjectDepartments[0],
      id: -getRandomNumber(),
    };

    setValue('contractTaskManagementProjectDepartments', [
      ...getValues('contractTaskManagementProjectDepartments'),
      newDepartment,
    ]);
  };

  const contractId = getValues('contractId');

  const { data: contract } = useQuery({
    queryKey: [QUERIES.CONTRACT, contractId],
    queryFn: () => createQueryByIdFn<Contract>('contract')(contractId || 0),
    enabled: !!contractId,
  });

  const getContent = useCallback(
    (opinion: string) => {
      return getNotifyContent({
        projectName: getValues('projectName'),
        contractNumber: contract?.contractNumber,
        contractName: contract?.contractName,
        opinion: opinion,
      });
    },
    [getValues, contract?.contractNumber, contract?.contractName]
  );

  return (
    <>
      <div className="grid max-w-full grid-cols-1 gap-x-8 gap-y-4 lg:grid-cols-24" key={item.id}>
        <div className="col-span-1 lg:col-span-24">
          <div className="grid grid-cols-1 gap-x-8  lg:grid-cols-24">
            {/* Thày đổi lần thứ: */}
            <div className="col-span-16 flex items-center">
              <FormLabel htmlFor="revisedNumber" className="w-[1150px]">
                {index > 0 && (
                  <>
                    <br />
                    <br />
                    {`${getOpinionTimeText(item.opinionTime, index)}`}
                  </>
                )}
              </FormLabel>
            </div>
            <div hidden>
              <FormField
                id={`${listName}.${index}.revisedNumber`}
                name={`${listName}.${index}.revisedNumber`}
                className="min-w-0 flex-1 md:w-[500px]"
              >
                <InputNumber
                  placeholder={`${enterLabel} ${t(`fields.${listName}.revisedNumber`)}`}
                  value={index}
                />
              </FormField>
            </div>
          </div>
        </div>

        <div className="col-span-1 lg:col-span-24">
          <div className="grid grid-cols-1 gap-x-8 gap-y-4 lg:grid-cols-24">
            {/* Ngày nhận HS*/}
            <div className="col-span-1 flex items-center lg:col-span-8 2xl:col-span-6">
              <FormLabel htmlFor="receivedTime" className="hidden w-[100px] md:block">
                {t(`fields.${listName}.receivedTime`)}
              </FormLabel>
              <FormField
                id={`${listName}.${index}.receivedTime`}
                name={`${listName}.${index}.receivedTime`}
                className="min-w-0 flex-1 md:w-[250px]"
                type="date"
                label={`${t(`fields.${listName}.receivedTime`)}`}
              >
                <DateBox
                  placeholder={`${enterLabel} ${t(`fields.${listName}.receivedTime`)}`}
                  disabled={!canSave}
                  pickerType="calendar"
                  focusStateEnabled={false}
                />
              </FormField>
            </div>
            <div className="col-span-6 hidden items-center lg:flex 2xl:col-span-4"></div>

            {/* Ý kiến TP.TC-TH*/}
            <div className="col-span-1 hidden items-center lg:col-span-10 lg:flex">
              <FormLabel htmlFor="departmentHeadOpinion" className="hidden w-[100px] md:block">
                {t(`fields.${listName}.departmentHeadOpinion`)}
              </FormLabel>
              <FormField
                id={`${listName}.${index}.departmentHeadOpinion`}
                name={`${listName}.${index}.departmentHeadOpinion`}
                className="min-w-0 flex-1 md:w-[500px]"
                label={`${t(`fields.${listName}.departmentHeadOpinion`)}`}
              >
                <TextArea
                  autoResizeEnabled={true}
                  placeholder={`${enterLabel} ${t(`fields.${listName}.departmentHeadOpinion`)}`}
                  readOnly={!canApproveOrReject}
                />
              </FormField>
            </div>
          </div>
        </div>

        <div className="col-span-1 lg:col-span-24">
          <div className="grid grid-cols-1 gap-x-8 gap-y-4 lg:grid-cols-24">
            {/* Ngày hoàn thành */}
            <div className="col-span-1 flex items-center lg:col-span-8 2xl:col-span-6">
              <FormLabel
                htmlFor={`${listName}.completionTime`}
                className="hidden w-[100px] md:block"
              >
                {t(`fields.${listName}.completionTime`)}
              </FormLabel>
              <FormField
                label={`${t(`fields.${listName}.completionTime`)}`}
                id={`${listName}.${index}.completionTime`}
                name={`${listName}.${index}.completionTime`}
                className="min-w-0 flex-1 md:w-[250px]"
                type="date"
              >
                <DateBox
                  placeholder={`${enterLabel} ${t(`fields.${listName}.completionTime`)}`}
                  disabled={!canSave}
                  pickerType="calendar"
                  focusStateEnabled={false}
                />
              </FormField>
            </div>
            {/* Ý kiến TP.TC-TH*/}
            <div className="col-span-1 flex items-center lg:col-span-10 lg:hidden">
              <FormLabel htmlFor="departmentHeadOpinion" className="hidden w-[100px] md:block">
                {t(`fields.${listName}.departmentHeadOpinion`)}
              </FormLabel>
              <FormField
                id={`${listName}.${index}.departmentHeadOpinion`}
                name={`${listName}.${index}.departmentHeadOpinion`}
                className="min-w-0 flex-1 md:w-[500px]"
                label={`${t(`fields.${listName}.departmentHeadOpinion`)}`}
              >
                <TextArea
                  autoResizeEnabled={true}
                  placeholder={`${enterLabel} ${t(`fields.${listName}.departmentHeadOpinion`)}`}
                  readOnly={!canApproveOrReject}
                />
              </FormField>
            </div>
            <div className="col-span-4 hidden items-center lg:flex"></div>
            {/* đồng ý và từ chối*/}
            {isSubmitted && (
              <div className="col-span-1 flex flex-col items-end justify-start space-y-4 align-bottom lg:col-span-12 2xl:col-span-10">
                {isApprovedOrRejected && (
                  <FormLabel htmlFor="approveTime" className="flex items-end justify-end">
                    <>{`${getApproveTimeText(item.approveTime, item.userApproveName)}`}</>
                  </FormLabel>
                )}
                {canApproveOrReject && (
                  <div className="space-x-4">
                    <Button
                      id={`${listName}.${index}.isApprove`}
                      text="Đồng ý"
                      className="uppercase"
                      type="success"
                      icon="check"
                      onClick={e => {
                        // Lấy dữ liệu hiện tại từ form
                        const currentValues = getValues(
                          listName
                        ) as ContractTaskManagementPlanningAndFinanceDetail[];

                        // Cập nhật giá trị `isApprove` cho phần tử có index hiện tại và thêm phần tử mới vào mảng
                        const updatedValues = currentValues.map((item, idx) =>
                          idx === index
                            ? {
                                ...item,
                                isApprove: true,
                                userApproveId: user?.userId,
                                approveTime: new Date(),
                              }
                            : item
                        );

                        // Cập nhật lại giá trị vào form một lần duy nhất
                        setValue(listName, [...updatedValues]);

                        const currentItem = currentValues[index];
                        //Loại chỉnh sửa => thêm dòng mới cho QLDA
                        if (currentItem.actionType === 1) {
                          addNewRowProjectDepartment();
                        }

                        // Nhấn đồng ý gửi thông báo TP.QLDA + GĐ.QLDA
                        void handleSubmitPromise(
                          e.event?.currentTarget as unknown as SyntheticEvent<HTMLElement>
                        ).then(data => {
                          const newData = data as ContractTaskManagementResponse;
                          let departmentData: ContractTaskManagementPlanningAndFinanceDetail | null =
                            null;
                          if (listName === 'contractTaskManagementFinanceDepartments') {
                            departmentData =
                              newData.contractTaskManagementFinanceDepartments?.at(-1) || null;
                          } else if (listName === 'contractTaskManagementPlanningDepartments') {
                            departmentData =
                              newData.contractTaskManagementPlanningDepartments?.at(-1) || null;
                          }
                          if (departmentData?.actionType === 1) {
                            const receivers: number[] = [];
                            if (newData.departmentProjectHeadId) {
                              receivers.push(newData.departmentProjectHeadId);
                            }
                            if (newData.projectManagementDirectorId) {
                              receivers.push(newData.projectManagementDirectorId);
                            }
                            if (receivers.length > 0) {
                              sendNotify({
                                title: t(`${ns + '.rechecked'}`, {
                                  ns: 'sendNotification',
                                }),
                                content: getContent(departmentData.opinion || ''),
                                typeNotification: path,
                                refId: Number(newData.id) || null,
                                userIds: receivers,
                              });
                            }
                          }
                        });
                      }}
                    />
                    <Button
                      id={`${listName}.${index}.isNotApprove`}
                      text="Từ chối"
                      className="uppercase"
                      type="danger"
                      icon="close"
                      onClick={e => {
                        // Lấy dữ liệu hiện tại từ form
                        const currentValues = getValues(
                          listName
                        ) as ContractTaskManagementPlanningAndFinanceDetail[];

                        // Cập nhật giá trị `isApprove` cho phần tử có index hiện tại và thêm phần tử mới vào mảng
                        const updatedValues = currentValues.map((item, idx) =>
                          idx === index
                            ? {
                                ...item,
                                isApprove: false,
                                userApproveId: user?.userId,
                                approveTime: new Date(),
                              }
                            : item
                        );

                        // Thêm phần tử mới vào mảng sau khi cập nhật giá trị `isApprove`
                        const newDepartment = {
                          ...rowTemplate,
                          assignedUserId: updatedValues[index].assignedUserId,
                          receivedTime: updatedValues[index].receivedTime,
                          completionTime: updatedValues[index].completionTime,
                          opinion: updatedValues[index].opinion,
                          id: -getRandomNumber(),
                        };

                        // Cập nhật lại giá trị vào form một lần duy nhất
                        setValue(listName, [...updatedValues, newDepartment]);

                        //Nhấn từ chối gửi thông báo cho cán bộ tài chính tổng hợp
                        void handleSubmitPromise(
                          e.event?.currentTarget as unknown as SyntheticEvent<HTMLElement>
                        ).then(data => {
                          const newData = data as ContractTaskManagementResponse;
                          let departmentData: ContractTaskManagementPlanningAndFinanceDetail | null =
                            null;
                          if (listName === 'contractTaskManagementFinanceDepartments') {
                            departmentData =
                              newData.contractTaskManagementFinanceDepartments?.at(-1) || null;
                          } else if (listName === 'contractTaskManagementPlanningDepartments') {
                            departmentData =
                              newData.contractTaskManagementPlanningDepartments?.at(-1) || null;
                          }

                          if (departmentData?.assignedUserId) {
                            sendNotify({
                              title: t(`${ns + '.rechecked'}`, {
                                ns: 'sendNotification',
                              }),
                              content: getContent(departmentData.departmentHeadOpinion || ''),
                              typeNotification: path,
                              refId: Number(newData.id) || null,
                              userIds: [departmentData?.assignedUserId],
                            });
                          }
                        });
                      }}
                    />
                  </div>
                )}
              </div>
            )}
          </div>
        </div>

        <div className="col-span-1 lg:col-span-24">
          <div className="grid grid-cols-1 gap-x-8 lg:grid-cols-24">
            {/* Giao CB.P.TC-TH */}
            <div className="col-span-1 flex items-center lg:col-span-8 2xl:col-span-6">
              <FormLabel
                name="assignedUserId"
                htmlFor="assignedUserId"
                className="hidden w-[100px] md:block"
              >
                {t(`fields.${listName}.assignedUserId`)}
              </FormLabel>
              <FormField
                label={`${t(`fields.${listName}.assignedUserId`)}`}
                id={`${listName}.${index}.assignedUserId`}
                name={`${listName}.${index}.assignedUserId`}
                className="min-w-0 flex-1 md:w-[250px]"
              >
                <FormCombobox
                  model="user"
                  queryKey={[QUERIES.USERS]}
                  placeholder={`${selectLabel} ${t(`fields.${listName}.assignedUserId`)}`}
                  defaultText={fieldsDepartmentDetails[index]?.assignedUserName}
                  disabled
                />
              </FormField>
              <Button
                className="mt-5 !rounded-[5%] md:mt-1"
                stylingMode="contained"
                type="default"
                icon="edit"
                onClick={toggleEditDialog}
                text=""
                width={10}
                disabled={!isApprover || isSubmitted || isApprovedOrRejected}
              />
            </div>

            <BasicDialog
              className="w-auto"
              open={isEditDialogOpen}
              toggle={toggleEditDialog}
              title={t('page.form.edit')}
            >
              <div className="flex items-center lg:col-span-2">
                {/*  Giao CB.P.TC-TH */}
                <FormLabel name="assignedUserId" htmlFor="assignedUserId">
                  {t(`fields.${listName}.assignedUserId`)}
                </FormLabel>
                <FormField
                  id={`${listName}.${index}.assignedUserId`}
                  name={`${listName}.${index}.assignedUserId`}
                  className=" flex-1"
                >
                  <FormCombobox
                    model="user"
                    queryKey={[QUERIES.USERS]}
                    placeholder={`${selectLabel} ${t(`fields.${listName}.assignedUserId`)}`}
                    defaultText={fieldsDepartmentDetails[index]?.assignedUserName}
                    disabled={!isApprover || isApprovedOrRejected}
                  />
                </FormField>
              </div>
              <div className="mt-4 flex items-end justify-end lg:col-span-2">
                {/* Xác nhận */}
                <Button
                  id={`${listName}.${index}.isApprove`}
                  text="Xác nhận"
                  className="uppercase"
                  type="default"
                  icon="save"
                  onClick={e => {
                    //Gửi thông báo cho cán bộ tài chính tổng hợp được chọn
                    void handleSubmitPromise(
                      e.event?.currentTarget as unknown as SyntheticEvent<HTMLElement>
                    )
                      .then(data => {
                        const newData = data as ContractTaskManagementResponse;

                        let departmentData: ContractTaskManagementPlanningAndFinanceDetail | null =
                          null;
                        if (listName === 'contractTaskManagementFinanceDepartments') {
                          departmentData =
                            newData.contractTaskManagementFinanceDepartments?.at(-1) || null;
                        } else if (listName === 'contractTaskManagementPlanningDepartments') {
                          departmentData =
                            newData.contractTaskManagementPlanningDepartments?.at(-1) || null;
                        }

                        const receiver = departmentData?.assignedUserId || null;
                        const opinion = departmentData?.opinion || null;

                        if (receiver) {
                          sendNotify({
                            title: t(notifyNew, {
                              ns: 'sendNotification',
                            }),
                            content: getContent(opinion || ''),
                            typeNotification: path,
                            refId: Number(newData.id) || null,
                            userIds: [receiver],
                          });
                        }
                      })
                      .catch(error => console.error('error:', error));

                    //đóng dialog
                    toggleEditDialog();
                  }}
                />
              </div>
            </BasicDialog>
            <div className="col-span-4 hidden items-center lg:flex"></div>
          </div>
        </div>

        {/* Ý kiến*/}
        <div className="col-span-1 lg:col-span-24">
          <div className="grid grid-cols-1 gap-x-8 lg:grid-cols-24">
            <div className="col-span-1 flex items-center lg:col-span-14 2xl:col-span-10">
              <FormLabel htmlFor="opinion" className="hidden w-[100px] md:block">
                {t(`fields.${listName}.opinion`)}
              </FormLabel>
              <FormField
                id={`${listName}.${index}.opinion`}
                name={`${listName}.${index}.opinion`}
                className="min-w-0 flex-1 md:w-[500px]"
                label={`${t(`fields.${listName}.opinion`)}`}
              >
                <TextArea
                  autoResizeEnabled={true}
                  placeholder={`${enterLabel} ${t(`fields.${listName}.opinion`)}`}
                  readOnly={!canSave}
                />
              </FormField>
            </div>
          </div>
        </div>
        {/* Loại ý kiến */}
        <div className="col-span-1 lg:col-span-24">
          <div className="grid grid-cols-1 gap-x-8 lg:grid-cols-24">
            <div className="col-span-1 flex items-center  lg:col-span-8 2xl:col-span-6">
              <ActionTypeCombobox
                isDisable={!canSave}
                fieldName={`${listName}.${index}.actionType`}
                ns={ns}
              />
            </div>
          </div>
        </div>

        {canSave && (
          <div className="col-span-1 lg:col-span-24">
            <div className="grid grid-cols-1 gap-x-8 lg:grid-cols-24">
              {/* Lưu và lưu tạm */}
              <div className="col-span-1 flex items-center justify-end space-x-4 lg:col-span-10">
                <Button
                  id={`${listName}.${index}.isApprove`}
                  text="Lưu tạm"
                  className="uppercase"
                  type="default"
                  icon="save"
                  stylingMode="outlined"
                  onClick={e => {
                    void handleSubmitPromise(
                      e.event?.currentTarget as unknown as SyntheticEvent<HTMLElement>
                    );
                  }}
                />
                <Button
                  id={`${listName}.${index}.isNotApprove`}
                  text="Lưu lại"
                  className="uppercase"
                  type="success"
                  icon="save"
                  disabled={!canSave}
                  onClick={e => {
                    // Lấy dữ liệu hiện tại từ form
                    const currentValues = getValues(
                      listName
                    ) as ContractTaskManagementPlanningAndFinanceDetail[];

                    // Cập nhật giá trị `isSaveTmp` cho phần tử có index hiện tại
                    const updatedValues = currentValues.map((item, idx) =>
                      idx === index
                        ? {
                            ...item,
                            isSaveTmp: false,
                            revisedNumber: index,
                          }
                        : item
                    );

                    // Cập nhật lại giá trị vào form
                    setValue(listName, updatedValues);

                    //Gửi thông báo cho trưởng phòng tài chính tổng hợp
                    void handleSubmitPromise(
                      e.event?.currentTarget as unknown as SyntheticEvent<HTMLElement>
                    ).then(data => {
                      const newData = data as ContractTaskManagementResponse;

                      let receiver: number | null = null;
                      let opinion: string | null = null;
                      if (listName === 'contractTaskManagementFinanceDepartments') {
                        receiver = newData.departmentFinanceHeadId;
                        opinion =
                          newData.contractTaskManagementFinanceDepartments?.at(-1)?.opinion || '';
                      } else if (listName === 'contractTaskManagementPlanningDepartments') {
                        receiver = newData.departmentPlanningHeadId;
                        opinion =
                          newData.contractTaskManagementPlanningDepartments?.at(-1)?.opinion || '';
                      }
                      if (receiver) {
                        sendNotify({
                          title: t(`${ns + '.needApprove'}`, {
                            ns: 'sendNotification',
                          }),
                          content: getContent(opinion || ''),
                          typeNotification: path,
                          refId: Number(newData.id) || null,
                          userIds: [receiver],
                        });
                      }
                    });
                  }}
                />
              </div>
            </div>
          </div>
        )}
      </div>
    </>
  );
};
