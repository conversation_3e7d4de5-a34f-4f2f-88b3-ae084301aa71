/* eslint-disable @typescript-eslint/no-unsafe-member-access */
import { ProjectDeploymentStatusReport } from '@/types';
import Chart, {
  ArgumentAxis,
  ValueAxis,
  CommonSeriesSettings,
  Series,
  Legend,
  Title,
} from 'devextreme-react/chart';
import { RowLabel } from '.';

export const Row2Table = ({ items }: { items: ProjectDeploymentStatusReport[] }) => {
  return (
    <Chart dataSource={items} className="bg-white">
      <CommonSeriesSettings argumentField="budgetFundName" type="bar" />

      <Series valueField="totalProjectsOneStep" name="Dự án 1 bước">
        <RowLabel />
      </Series>
      <Series valueField="totalProjectsTwoSteps" name="Dự án 2 bước">
        <RowLabel />
      </Series>

      <ArgumentAxis
        title={{ text: 'Nguồn ngân sách', font: { weight: 'bold' } }}
        label={{ rotationAngle: 90, overlappingBehavior: 'none', font: { weight: 'bold' } }}
      />
      <ValueAxis
        title={{ text: 'Số lượng dự án', font: { weight: 'bold' } }}
        label={{ rotationAngle: 90, overlappingBehavior: 'none', font: { weight: 'bold' } }}
      />

      <Legend verticalAlignment="bottom" horizontalAlignment="center" font={{ weight: 800 }} />
      <Title text="Tình trạng triển khai theo nguồn ngân sách" />
    </Chart>
  );
};
