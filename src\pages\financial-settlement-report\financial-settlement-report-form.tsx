import { zod<PERSON><PERSON>olver } from '@hookform/resolvers/zod';
import { SyntheticEvent, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useParams } from 'react-router-dom';

import axiosInstance, { request } from '@/axios-instance';
import { DeleteConfirmDialog } from '@/components/confirm-dialog';
import { PageLayout } from '@/components/page-layout';
import { PeriodFilter } from '@/components/period-filter-form';
import { Form, FormCombobox, FormField, FormLabel } from '@/components/ui/form';
import { InputNumber } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  enterLabel,
  MUTATE,
  PATHS,
  PERMISSIONS,
  PROFESSIONS,
  QUERIES,
  selectLabel,
} from '@/constant';
import {
  useAuth,
  useDataTable,
  useEntity,
  useF<PERSON><PERSON><PERSON><PERSON>,
  useFormOperation,
  usePermission,
} from '@/hooks';
import { useFormNavigate } from '@/hooks/use-form-navigate';
import { toDateType, toLocaleDate } from '@/lib/date';
import { createMutationSuccessFn } from '@/lib/i18nUtils';
import { getRandomNumber } from '@/lib/number';
import { getSumsFromArray, getValidId, getValidItems } from '@/lib/utils';
import {
  createFinancialSettlementReport,
  FinancialSettlementReportBudgetFundEditableTable,
} from '@/pages/financial-settlement-report';
import {
  createDeleteMutateFn,
  createPostMutateFn,
  createPutMutateFn,
  createQueryByIdFn,
} from '@/services';
import {
  Contractor,
  CostItem,
  defaultValuesFinancialSettlementReport,
  FinancialSettlementReport,
  financialSettlementReportSchema,
  GetFinancialSettlementReportDto,
} from '@/types';
import { useMutation } from '@tanstack/react-query';
import { DateBox, TextArea, TextBox } from 'devextreme-react';
import Button from 'devextreme-react/button';
import { FinancialSettlementReportAssetGroupEditableTable } from './financial-settlement-report-asset-group-editable-table';
import { FinancialSettlementReportDataReconciliationEditableTable } from './financial-settlement-report-data-reconciliation-editable-table';
import { FinancialSettlementReportDocumentListEditableTable } from './financial-settlement-report-document-list-editable-table';
import { FinancialSettlementReportInvestmentCostEditableTable } from './financial-settlement-report-investment-cost-editable-table';
import { FinancialSettlementReportProjectDebtStatusStatisticsEditableTable } from './financial-settlement-report-project-debt-status-statistics-edittable-table';
import { FinancialSettlementReportProposedSettlementInvestmentCostEditableTable } from './financial-settlement-report-proposed-settlement-investment-cost-editable-table';
import { RecordEditableTable } from '@/components/records-attachment';
import { RowSelectionState } from '@tanstack/react-table';
import { removeAccents } from '@/lib/text';
import { snakeCase } from 'lodash';

const onFinancialSettlementReportMutationSuccess = createMutationSuccessFn(
  'financialSettlementReport'
);

export const FinancialSettlementReportForm = () => {
  const { id: editId } = useParams();

  const { t } = useTranslation('financialSettlementReport');
  const exportFileName = snakeCase(removeAccents(t('model')));

  const role = usePermission(PERMISSIONS.FINANCIAL_SETTLEMENT_REPORT);
  const { user } = useAuth();

  const [rowSelection, setRowSelection] = useState<RowSelectionState>({});
  const { goBackToList, goToUpdate, goToNew } = useFormNavigate(PATHS.FINANCIAL_SETTLEMENT_REPORT);

  const { list: contractors } = useEntity<Contractor>({
    queryKey: [QUERIES.CONTRACTOR],
    model: 'contractor',
  });
  const { list: costItems } = useEntity<CostItem>({
    queryKey: [QUERIES.BUDGET_FUND],
    model: 'cost-item',
  });

  const defaultValues = useMemo(
    () => ({
      ...defaultValuesFinancialSettlementReport,
      userCreatedId: user?.userId,
    }),
    [user?.userId]
  );

  const { handleSubmit, loading, methods, data } = useFormHandler<FinancialSettlementReport>({
    queryKey: [MUTATE.FINANCIAL_SETTLEMENT_REPORT, editId],
    mutateKey: [MUTATE.FINANCIAL_SETTLEMENT_REPORT],
    queryId: Number(editId) || 0,
    invalidateKey: [QUERIES.FINANCIAL_SETTLEMENT_REPORT],
    readFn: createQueryByIdFn<FinancialSettlementReport>('financial-settlement-report'),
    createFn: createPostMutateFn<FinancialSettlementReport>('financial-settlement-report'),
    updateFn: createPutMutateFn<FinancialSettlementReport>('financial-settlement-report'),
    formatPayloadFn: data => ({
      ...data,
      financialSettlementReportTime: toLocaleDate(data.financialSettlementReportTime!)!,
      usageStartDate: toLocaleDate(data.usageStartDate!),
      financialSettlementReportBudgetFunds: getValidItems(
        data.financialSettlementReportBudgetFunds,
        item => !!item.budgetFundId
      ),
      financialSettlementReportDataReconciliationTables: getValidItems(
        data.financialSettlementReportDataReconciliationTables,
        item => !!item.budgetFundId
      ),
      financialSettlementReportInvestmentCosts: getValidItems(
        data.financialSettlementReportInvestmentCosts,
        item => !!item.costItemTypeId
      ),
      financialSettlementReportProposedSettlementInvestmentCosts: getValidItems(
        data.financialSettlementReportProposedSettlementInvestmentCosts,
        item => !!item.costItemId
      ),
      financialSettlementReportProjectDebtStatusStatistics: getValidItems(
        data.financialSettlementReportProjectDebtStatusStatistics,
        item => !!item.costItemId
      ),
      financialSettlementReportDocumentLists: data.financialSettlementReportDocumentLists
        .filter(item => item.content)
        .map(item => ({
          ...item,
          dateCreate: toLocaleDate(item.dateCreate!),
        })),
      itemsRecordManagement: data.itemsRecordManagement
        .filter(item => item.content)
        .map(itemRecord => ({
          ...itemRecord,
          id: getValidId(itemRecord.id),
          dateCreate: toLocaleDate(itemRecord.dateCreate!),
          itemFile: itemRecord.itemFile
            .filter(file => file.fileName)
            .map(file => ({ ...file, id: getValidId(file.id) })),
        })),
    }),
    formatResponseFn: data => ({
      ...data,
      financialSettlementReportTime: toDateType(data.financialSettlementReportTime!)!,
      usageStartDate: toDateType(data.usageStartDate!),
      financialSettlementReportDocumentLists: data.financialSettlementReportDocumentLists.map(
        item => ({
          ...item,
          dateCreate: toDateType(item.dateCreate!),
        })
      ),
      itemsRecordManagement: data.itemsRecordManagement.map(item => ({
        ...item,
        dateCreate: toDateType(item.dateCreate!),
      })),
    }),
    onCreateSuccess: data => {
      onFinancialSettlementReportMutationSuccess(data);
      goToUpdate(data);
    },
    onUpdateSuccess: onFinancialSettlementReportMutationSuccess,
    formOptions: {
      resolver: zodResolver(financialSettlementReportSchema),
      defaultValues,
    },
  });

  const {
    isDeleting,
    deleteTarget,
    selectTargetToDelete,
    toggleConfirmDeleteDialog,
    isConfirmDeleteDialogOpen,
  } = useDataTable<FinancialSettlementReport, PeriodFilter>({
    deleteFn: createDeleteMutateFn('financial-settlement-report'),
    deleteKey: [MUTATE.FINANCIAL_SETTLEMENT_REPORT],
  });

  const { reset, onTimeChange } = useFormOperation<FinancialSettlementReport>({
    model: 'financial-settlement-report',
    fieldTime: 'financialSettlementReportTime',
    createCodeKey: [QUERIES.FINANCIAL_SETTLEMENT_REPORT_CODE],
    formMethods: methods,
  });

  const onCreateNew = () => {
    goToNew();
    methods.reset(defaultValues);
    reset();
  };

  const onDelete = () => {
    selectTargetToDelete(methods.getValues());
  };
  const { mutate: mutateDetails } = useMutation({
    mutationKey: [MUTATE.FINANCIAL_SETTLEMENT_REPORT_GET_REPORT],
    mutationFn: () => {
      const projectId = methods.getValues('projectId');
      return request<GetFinancialSettlementReportDto>(
        axiosInstance.post(`/${'financial-settlement-report/get-report'}`, {
          pageIndex: -1,
          pageSize: -1,
          objParam: { projectId: projectId },
        })
      );
    },
    onSuccess: data => {
      const [total] = getSumsFromArray(data?.rptInvestmentCostTypeDtos, [
        'approvedFinalProjectBudget',
      ]);
      methods.setValue('price', total);
      methods.setValue('totalAmount', total);
      methods.setValue('financialSettlementReportAssetGroups', [
        {
          id: -getRandomNumber(),
          financialSettlementReportId: 0,
          groupName: 'Tài sản dài hạn (tài sản cố định) ',
          totalAmount: total,
        },
        {
          id: -getRandomNumber(),
          financialSettlementReportId: 0,
          groupName: 'Tài sản ngắn hạn',
          totalAmount: 0,
        },
      ]);

      methods.setValue(
        'financialSettlementReportBudgetFunds',
        data?.rptFinancialSettlementReportByBudgetFundDtos.map(item => ({
          id: -getRandomNumber(),
          financialSettlementReportId: 0,
          budgetFundId: item.budgetFundId,
          budgetFundParentId: item.budgetFundParentId,
          approvedFinalProjectBudget: item.approvedFinalProjectBudget,
          allocatedPlannedCapital: item.allocatedPlannedCapital,
          disbursedFunds: item.disbursedFunds,
        }))
      );

      methods.setValue(
        'financialSettlementReportDataReconciliationTables',
        data?.rptDataReconciliationTableDtos.map(item => ({
          id: -getRandomNumber(),
          ordinalNumber: item.ordinalNumber,
          financialSettlementReportId: 0,
          budgetFundId: item.budgetFundId,
          poPlannedCapital: item.poPlannedCapital,
          poDisbursedTotal: item.poDisbursedTotal,
          poDisbursedCompletedWorkload: item.poDisbursedCompletedWorkload,
          poDisbursedAdvance: item.poDisbursedAdvance,
          amountPlannedCapital: item.poPlannedCapital,
          amountAllocatedTotal: item.poDisbursedTotal,
          amountAllocatedCompletedWorkload: item.poDisbursedCompletedWorkload,
          amountAllocatedAdvance: item.poDisbursedAdvance,
          difference: 0,
          notes: '',
          budgetYear: item.budgetYear,
          budgetFundName: item.budgetFundName!,
        }))
      );

      methods.setValue(
        'financialSettlementReportDocumentLists',
        data?.rptDocumentListOfProjectDtos.map(item => ({
          id: -getRandomNumber(),
          financialSettlementReportId: 0,
          groupDocId: item.groupDocId,
          typeDocId: item.typeDocId,
          noDoc: item.noDoc,
          dateCreate: toLocaleDate(item.dateCreate!),
          content: item.content,
          agencyId: item.agencyId,
          typeDocParentId: item.typeDocParentId!,
          typeDocName: item.typeDocName!,
          typeDocParentName: item.typeDocParentName!,
          groupDocName: item.groupDocName!,
          notes: item.notes!,
        }))
      );

      methods.setValue(
        'financialSettlementReportInvestmentCosts',
        data.rptInvestmentCostTypeDtos.map(item => ({
          id: -getRandomNumber(),
          financialSettlementReportId: 0,
          costItemTypeId: item.costItemTypeId,
          approvedFinalProjectBudget: item.approvedFinalProjectBudget,
          proposedSettlementValue: item.proposedSettlementValue,
          proposedLiquidationAmount:
            Number(item.proposedSettlementValue!) - Number(item.approvedFinalProjectBudget!),
        }))
      );

      methods.setValue(
        'financialSettlementReportProposedSettlementInvestmentCosts',
        data?.proposedSettlementInvestmentCostDetailDtos.map(item => ({
          id: -getRandomNumber(),
          financialSettlementReportId: 0,
          costItemId: item.costItemId,
          costItemTypeId: item.costItemTypeId,
          costItemTypeName: item.costItemTypeName,
          approvedFinalProjectBudget: item.approvedFinalProjectBudget,
          approvedFinalEstimate: item.approvedFinalEstimate,
          proposedSettlementValue: item.proposedSettlementValue,
          increaseDecreaseReason: '',
        }))
      );

      methods.setValue(
        'financialSettlementReportProjectDebtStatusStatistics',
        data?.rptProjectDebtStatusStatisticsDtos.map(item => ({
          id: -getRandomNumber(),
          financialSettlementReportId: 0,
          contractorId: item.contractorId,
          costItemId: item.costItemId,
          proposedSettlementValue: item.proposedSettlementValue,
          disbursedCapital: item.disbursedCapital,
          payableAmount: item.payableAmount,
          receivableAmount: item.receivableAmount,
          note: '',
        }))
      );
    },
  });
  const handleGetData = () => {
    mutateDetails();
  };

  return (
    <>
      <Form {...methods}>
        <form autoComplete="off">
          <PageLayout
            onSaveChange={e => {
              handleSubmit(e.event?.currentTarget as unknown as SyntheticEvent<HTMLElement>);
            }}
            header={editId !== 'new' ? t('page.form.edit') : t('page.form.addNew')}
            canSaveChange={!isNaN(Number(editId)) ? role?.isUpdate : role?.isCreate}
            isSaving={loading}
            onCancel={goBackToList}
            onDelete={editId !== 'new' ? onDelete : undefined}
            customElementLeft={
              <>
                <Button
                  text={t('content.createNew', { ns: 'common' })}
                  className="uppercase"
                  stylingMode="outlined"
                  type="default"
                  icon="plus"
                  onClick={onCreateNew}
                />
              </>
            }
            customElementRight={
              <>
                <Button
                  type="default"
                  stylingMode="contained"
                  icon="print"
                  text={t('Xuất Excel')}
                  disabled={!role?.isPrint || !data}
                  onClick={() => {
                    const formValues = { ...methods.getValues() };

                    formValues.financialSettlementReportProjectDebtStatusStatistics.forEach(
                      value => {
                        const contractor = contractors.find(
                          match => match.id === value.contractorId
                        );
                        const costItem = costItems.find(match => match.id === value.costItemId);
                        value.contractorName = contractor?.name;
                        value.costItemName = costItem?.name;
                      }
                    );

                    // eslint-disable-next-line @typescript-eslint/no-unsafe-call
                    void createFinancialSettlementReport(`${exportFileName}.xlsx`, formValues);
                  }}
                />
              </>
            }
          >
            <div className="grid  max-w-screen-2xl grid-cols-1 gap-x-8 gap-y-4 lg:grid-cols-24">
              {/* Cột 1 */}
              <div className="col-span-1 flex flex-col gap-x-8 gap-y-4 lg:col-span-16 2xl:col-span-12">
                <div className="flex items-center">
                  <FormLabel
                    name="projectId"
                    htmlFor="projectId"
                    className="hidden w-[100px] md:block"
                  >
                    {t('fields.projectId')}
                  </FormLabel>
                  <FormField
                    label={t('fields.projectId')}
                    id="projectId"
                    name="projectId"
                    className="min-w-0 flex-1"
                  >
                    <FormCombobox
                      placeholder={`${selectLabel} ${t('fields.projectId')}`}
                      model="project"
                      queryKey={[QUERIES.PROJECT]}
                    />
                  </FormField>
                </div>

                {/* Chủ đầu tư */}
                <div className="flex items-center">
                  <FormLabel
                    name="projectOwnerId"
                    htmlFor="projectOwnerId"
                    className="hidden w-[100px] md:block"
                  >
                    {t('fields.projectOwnerId')}
                  </FormLabel>
                  <FormField
                    id="projectOwnerId"
                    name="projectOwnerId"
                    className="min-w-0 flex-1"
                    label={t('fields.projectOwnerId')}
                  >
                    <FormCombobox
                      placeholder={`${selectLabel} ${t('fields.projectOwnerId')}`}
                      model="project-owner"
                      queryKey={[QUERIES.PROJECT_OWNER]}
                    />
                  </FormField>
                </div>
                {/* Đơn vị tiếp nhận sử dụng */}
                <div className="flex items-center">
                  <FormLabel
                    name="receivingUnit"
                    htmlFor="receivingUnit"
                    className="hidden w-[100px] md:block"
                  >
                    {t('fields.receivingUnit')}
                  </FormLabel>
                  <FormField
                    id="receivingUnit"
                    name="receivingUnit"
                    className="flex-1 md:w-[250px]"
                    label={t('fields.receivingUnit')}
                  >
                    <TextBox placeholder={`${enterLabel} ${t('fields.receivingUnit')}`} />
                  </FormField>
                </div>
                {/* show ra khi màn hình từ lg đến < 2xl */}
                <div className="hidden items-center gap-x-8  lg:flex 2xl:hidden">
                  {/* Giá đơn vị */}
                  <div className="flex flex-1 items-center">
                    <FormLabel name="price" htmlFor="price" className="hidden w-[100px] md:block">
                      {t('fields.price')}
                    </FormLabel>
                    <FormField
                      id="price"
                      name="price"
                      className="flex-1 "
                      type="number"
                      label={t('fields.price')}
                    >
                      <InputNumber isMoney placeholder={`${enterLabel} ${t('fields.price')}`} />
                    </FormField>
                  </div>
                  {/* Tổng nguyên giá */}
                  <div className="flex flex-1 items-center">
                    <FormLabel
                      name="totalAmount"
                      htmlFor="totalAmount"
                      className="hidden w-[100px] md:block"
                    >
                      {t('fields.totalAmount')}
                    </FormLabel>
                    <FormField
                      id="totalAmount"
                      name="totalAmount"
                      className="flex-1 "
                      type="number"
                      label={t('fields.totalAmount')}
                    >
                      <InputNumber
                        isMoney
                        placeholder={`${enterLabel} ${t('fields.totalAmount')}`}
                      />
                    </FormField>
                  </div>
                </div>
              </div>
              {/* Cột 2 */}
              {/* Ẩn đi khi màn hình từ lg đến < 2xl */}
              <div className="col-span-1 flex flex-col gap-x-8 gap-y-4 lg:hidden 2xl:col-span-6 2xl:flex">
                {/* Giá đơn vị */}
                <div className="flex items-center">
                  <FormLabel name="price" htmlFor="price" className="hidden w-[100px] md:block">
                    {t('fields.price')}
                  </FormLabel>
                  <FormField
                    id="price"
                    name="price"
                    className="flex-1 md:w-[250px]"
                    type="number"
                    label={t('fields.price')}
                  >
                    <InputNumber placeholder={`${enterLabel} ${t('fields.price')}`} />
                  </FormField>
                </div>

                {/* Tổng nguyên giá */}
                <div className="flex items-center">
                  <FormLabel
                    name="totalAmount"
                    htmlFor="totalAmount"
                    className="hidden w-[100px] md:block"
                  >
                    {t('fields.totalAmount')}
                  </FormLabel>
                  <FormField
                    id="totalAmount"
                    name="totalAmount"
                    className="flex-1 md:w-[250px]"
                    type="number"
                    label={t('fields.totalAmount')}
                  >
                    <InputNumber placeholder={`${enterLabel} ${t('fields.totalAmount')}`} />
                  </FormField>
                </div>
                {/* Ngày sử dụng */}
                <div className="flex items-center">
                  <FormLabel
                    name="usageStartDate"
                    htmlFor="usageStartDate"
                    className="hidden w-[100px] md:block"
                  >
                    {t('fields.usageStartDate')}
                  </FormLabel>
                  <FormField
                    id="usageStartDate"
                    name="usageStartDate"
                    className="flex-1 md:w-[250px]"
                    type="date"
                    label={t('fields.usageStartDate')}
                  >
                    <DateBox
                      placeholder={`${selectLabel} ${t('fields.usageStartDate')}`}
                      pickerType="calendar"
                      focusStateEnabled={false}
                    />
                  </FormField>
                </div>
              </div>
              {/* Cột 3 */}
              <div className="col-span-1 flex flex-col gap-x-8 gap-y-4 lg:col-span-8 2xl:col-span-6">
                {/* Ngày lập */}
                <div className="flex items-center">
                  <FormLabel
                    name="financialSettlementReportTime"
                    htmlFor="financialSettlementReportTime"
                    className="hidden w-[100px] md:block"
                  >
                    {t('fields.financialSettlementReportTime')}
                  </FormLabel>
                  <FormField
                    id="financialSettlementReportTime"
                    name="financialSettlementReportTime"
                    className="flex-1 md:w-[250px]"
                    type="date"
                    label={t('fields.financialSettlementReportTime')}
                    onChange={e => {
                      onTimeChange(e.target.value);
                    }}
                  >
                    <DateBox
                      placeholder={`${selectLabel} ${t('fields.financialSettlementReportTime')}`}
                      pickerType="calendar"
                      focusStateEnabled={false}
                    />
                  </FormField>
                </div>
                {/* Mã phiếu */}
                <div className="flex items-center">
                  <FormLabel name="code" htmlFor="code" className="hidden w-[100px] md:block">
                    {t('fields.code')}
                  </FormLabel>
                  <FormField
                    id="code"
                    name="code"
                    className="flex-1 md:w-[250px]"
                    label={t('fields.code')}
                  >
                    <TextBox placeholder={`${enterLabel} ${t('fields.code')}`} disabled />
                  </FormField>
                </div>

                {/* Ngày sử dụng */}
                {/* show ra khi màn hình từ lg đến < 2xl */}
                <div className="hidden items-center lg:flex 2xl:hidden">
                  <FormLabel
                    name="usageStartDate"
                    htmlFor="usageStartDate"
                    className="hidden w-[100px] md:block"
                  >
                    {t('fields.usageStartDate')}
                  </FormLabel>
                  <FormField
                    id="usageStartDate"
                    name="usageStartDate"
                    className="flex-1 md:w-[250px]"
                    type="date"
                    label={t('fields.usageStartDate')}
                  >
                    <DateBox
                      placeholder={`${selectLabel} ${t('fields.usageStartDate')}`}
                      pickerType="calendar"
                      focusStateEnabled={false}
                    />
                  </FormField>
                </div>
                <div className="flex items-center">
                  <Button
                    text={t('action.getData', { ns: 'common' })}
                    className="w-full lg:w-fit"
                    stylingMode="contained"
                    type="default"
                    icon="search"
                    onClick={() => void handleGetData()}
                    //
                  />
                </div>
              </div>
            </div>
            <div className="mt-8">
              <Tabs defaultValue="budgetFund">
                <div className="w-full">
                  <TabsList>
                    <TabsTrigger value="budgetFund">{t('page.form.tabs.budgetFund')}</TabsTrigger>
                    <TabsTrigger value="documentList">
                      {t('page.form.tabs.documentList')}
                    </TabsTrigger>

                    <TabsTrigger value="dataReconciliation">
                      {t('page.form.tabs.dataReconciliation')}
                    </TabsTrigger>

                    <TabsTrigger value="proposedSettlementInvestmentCost">
                      {t('page.form.tabs.proposedSettlementInvestmentCost')}
                    </TabsTrigger>

                    <TabsTrigger value="projectDebtStatusStatistics">
                      {t('page.form.tabs.projectDebtStatusStatistics')}
                    </TabsTrigger>
                    <TabsTrigger value="attachment">{t('page.form.tabs.attachment')}</TabsTrigger>
                  </TabsList>
                </div>
                <TabsContent value="budgetFund">
                  <Tabs defaultValue="overview">
                    <div className="w-full">
                      <TabsList>
                        <TabsTrigger value="overview">{t('page.form.tabs.overview')}</TabsTrigger>
                        <TabsTrigger value="budgetFund1">
                          {t('page.form.tabs.budgetFund')}
                        </TabsTrigger>
                        <TabsTrigger value="investmentCost">
                          {t('page.form.tabs.investmentCost')}
                        </TabsTrigger>
                        <TabsTrigger value="groupName">{t('page.form.tabs.groupName')}</TabsTrigger>
                      </TabsList>
                      <TabsContent value="overview">
                        <div className="xl2:max-w-screen-lg grid max-w-full grid-cols-1  gap-x-8 gap-y-4 pb-10 lg:grid-cols-24">
                          <div className="col-span-1 flex flex-col gap-x-4 gap-y-4 lg:col-span-8 2xl:col-span-6">
                            <div className="flex items-center">
                              <FormLabel
                                name="damageCosts"
                                htmlFor="damageCosts"
                                className="hidden w-[100px] md:block"
                              >
                                {t('fields.damageCosts')}
                              </FormLabel>
                              <FormField
                                id="damageCosts"
                                name="damageCosts"
                                className="flex-1 md:w-[250px]"
                                label={t('fields.damageCosts')}
                              >
                                <InputNumber
                                  placeholder={`${enterLabel} ${t('fields.damageCosts')}`}
                                />
                              </FormField>
                            </div>
                          </div>
                          <div className="col-span-1 flex flex-col gap-x-4 gap-y-4 lg:col-span-8 2xl:col-span-6">
                            <div className="flex items-center">
                              <FormLabel
                                name="nonCapitalizableCosts"
                                htmlFor="nonCapitalizableCosts"
                                className="hidden w-[100px] md:block"
                              >
                                {t('fields.nonCapitalizableCosts')}
                              </FormLabel>
                              <FormField
                                id="nonCapitalizableCosts"
                                name="nonCapitalizableCosts"
                                className="flex-1 md:w-[250px]"
                                label={t('fields.nonCapitalizableCosts')}
                              >
                                <InputNumber
                                  placeholder={`${enterLabel} ${t('fields.nonCapitalizableCosts')}`}
                                />
                              </FormField>
                            </div>
                          </div>
                          <div className="col-span-1 flex flex-col gap-x-8 gap-y-4 lg:col-span-24">
                            <div className="flex items-center">
                              <FormLabel
                                name="projectProgressStatus"
                                htmlFor="projectProgressStatus"
                                className="hidden w-[100px] md:block"
                              >
                                {t('fields.projectProgressStatus')}
                              </FormLabel>
                              <FormField
                                id="projectProgressStatus"
                                name="projectProgressStatus"
                                className="flex-1 md:w-[250px]"
                                label={t('fields.projectProgressStatus')}
                              >
                                <TextArea
                                  placeholder={`${enterLabel} ${t('fields.projectProgressStatus')}`}
                                  autoResizeEnabled
                                />
                              </FormField>
                            </div>
                          </div>
                          <div className="col-span-1 flex flex-col gap-x-8 gap-y-4 lg:col-span-24">
                            <div className="flex items-center">
                              <FormLabel
                                name="projectImplementationReviewAndEvaluation"
                                htmlFor="projectImplementationReviewAndEvaluation"
                                className="hidden w-[100px] md:block"
                              >
                                {t('fields.projectImplementationReviewAndEvaluation')}
                              </FormLabel>
                              <FormField
                                id="projectImplementationReviewAndEvaluation"
                                name="projectImplementationReviewAndEvaluation"
                                className="flex-1 md:w-[250px]"
                                label={t('fields.projectImplementationReviewAndEvaluation')}
                              >
                                <TextArea
                                  placeholder={`${enterLabel} ${t('fields.projectImplementationReviewAndEvaluation')}`}
                                  autoResizeEnabled
                                />
                              </FormField>
                            </div>
                          </div>
                          <div className="col-span-1 flex flex-col gap-x-8 gap-y-4 lg:col-span-24">
                            <div className="flex items-center">
                              <FormLabel
                                name="proposal"
                                htmlFor="proposal"
                                className="hidden w-[100px] md:block"
                              >
                                {t('fields.proposal')}
                              </FormLabel>
                              <FormField
                                id="proposal"
                                name="proposal"
                                className="flex-1 md:w-[250px]"
                                label={t('fields.proposal')}
                              >
                                <TextArea
                                  placeholder={`${enterLabel} ${t('fields.proposal')}`}
                                  autoResizeEnabled
                                />
                              </FormField>
                            </div>
                          </div>
                        </div>
                      </TabsContent>
                      <TabsContent value="budgetFund1">
                        <FinancialSettlementReportBudgetFundEditableTable />
                      </TabsContent>
                      <TabsContent value="investmentCost">
                        <FinancialSettlementReportInvestmentCostEditableTable role={role} />
                      </TabsContent>
                      <TabsContent value="groupName">
                        <FinancialSettlementReportAssetGroupEditableTable role={role} />
                      </TabsContent>
                    </div>
                  </Tabs>
                </TabsContent>
                <TabsContent value="documentList">
                  <FinancialSettlementReportDocumentListEditableTable />
                </TabsContent>
                <TabsContent value="dataReconciliation">
                  <FinancialSettlementReportDataReconciliationEditableTable role={role} />
                </TabsContent>
                <TabsContent value="proposedSettlementInvestmentCost">
                  <FinancialSettlementReportProposedSettlementInvestmentCostEditableTable
                    role={role}
                  />
                </TabsContent>
                <TabsContent value="projectDebtStatusStatistics">
                  <FinancialSettlementReportProjectDebtStatusStatisticsEditableTable role={role} />
                </TabsContent>
                <TabsContent value="attachment" className="mt-4">
                  <RecordEditableTable
                    role={role}
                    rowSelection={rowSelection}
                    setRowSelection={setRowSelection}
                    folder="financial-settlement-report"
                    profession={PROFESSIONS.FINANCIAL_SETTLEMENT_REPORT}
                  />
                </TabsContent>
              </Tabs>
            </div>
            {/* <div style={{ display: "none" }}>{printoutElement}</div> */}
          </PageLayout>
        </form>
      </Form>
      <DeleteConfirmDialog
        model="financial-settlement-report"
        name={methods.getValues('code')!}
        open={isConfirmDeleteDialogOpen}
        toggle={toggleConfirmDeleteDialog}
        isDeleting={isDeleting}
        onConfirm={() => {
          deleteTarget();
          setTimeout(() => onCreateNew(), 0);
        }}
      />
    </>
  );
};
