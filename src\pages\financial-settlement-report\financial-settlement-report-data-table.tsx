/* eslint-disable @typescript-eslint/no-unsafe-member-access */

import { DeleteConfirmDialog } from '@/components/confirm-dialog';
import { customizeNumberCell, DevexDataGrid } from '@/components/devex-data-grid';
import { PageLayout } from '@/components/page-layout';
import { PeriodFilter, PeriodFilterForm } from '@/components/period-filter-form';
import { MUTATE, PATHS, PERMISSIONS, QUERIES, TABLES } from '@/constant';
import { useDataTable, useEntity, usePermission } from '@/hooks';
import { createExportingEvent } from '@/lib/file';
import { translationWithNamespace } from '@/lib/i18nUtils';
import { removeAccents } from '@/lib/text';
import { callbackWithTimeout, displayExpr } from '@/lib/utils';
import { createDeleteMutateFn, createQueryPaginationFn } from '@/services';
import { FinancialSettlementReport } from '@/types';
import { useQuery } from '@tanstack/react-query';
import { Button, Column, Editing, Export, Lookup } from 'devextreme-react/data-grid';
import { ColumnButtonClickEvent } from 'devextreme/ui/data_grid';
import { snakeCase } from 'lodash';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';

const t = translationWithNamespace('financialSettlementReport');
const path = PATHS.FINANCIAL_SETTLEMENT_REPORT;
const exportFileName = snakeCase(removeAccents(t('model')));

const onExporting = createExportingEvent(`${exportFileName}.xlsx`, 'Main');

export const FinancialSettlementReportDataTable = () => {
  const navigate = useNavigate();
  const { t } = useTranslation('financialSettlementReport');

  const role = usePermission(PERMISSIONS.FINANCIAL_SETTLEMENT_REPORT);

  const { list: projects } = useEntity({ queryKey: [QUERIES.PROJECT], model: 'project' });
  const { list: projectOwners } = useEntity({
    queryKey: [QUERIES.PROJECT_OWNER],
    model: 'project-owner',
  });
  const { list: budgetFunds } = useEntity({
    queryKey: [QUERIES.PROJECT_OWNER],
    model: 'budget-fund',
  });

  const getTargetAlias = (target: FinancialSettlementReport | undefined) => {
    if (!target) {
      return '';
    }
    return target.code!;
  };

  const {
    selectedTarget,

    isConfirmDeleteDialogOpen,
    toggleConfirmDeleteDialog,
    selectTargetToDelete,
    deleteTarget,
    isDeleting,

    queryListParams,
    queryListMethods,
    // Query
  } = useDataTable<FinancialSettlementReport, PeriodFilter>({
    queryRangeName: 'financialSettlementReportTime',
    getTargetAlias,
    deleteFn: createDeleteMutateFn<FinancialSettlementReport>('financial-settlement-report'),
    deleteKey: [MUTATE.DELETE_FINANCIAL_SETTLEMENT_REPORT],
    invalidateKey: [QUERIES.FINANCIAL_SETTLEMENT_REPORT],
  });

  const { data, refetch } = useQuery({
    queryKey: [QUERIES.FINANCIAL_SETTLEMENT_REPORT],
    queryFn: () => {
      return createQueryPaginationFn<FinancialSettlementReport>('financial-settlement-report')({
        pageIndex: 1,
        pageSize: -1,
        sortColumn: 'FinancialSettlementReportTime',
        sortOrder: 1,
        isPage: false,
        filterColumn: [],
        ...queryListParams,
      });
    },
  });

  const { items } = data || { items: [] };

  const onEditClick = (e: ColumnButtonClickEvent<FinancialSettlementReport>) => {
    if (e.row?.data) {
      navigate(`${path}/` + e.row.data?.id, { state: path });
    }
  };

  const onAddClick = () => {
    navigate(`${path}/new`, { state: path });
  };

  const onDeleteClick = (e: ColumnButtonClickEvent<FinancialSettlementReport>) => {
    if (e.row?.data) {
      selectTargetToDelete(e.row.data);
    }
  };

  const { isUpdate, isDelete } = role || {};

  return (
    <PageLayout header={t('page.header')}>
      <PeriodFilterForm
        defaultSearchValues={{
          range: [queryListParams.fromDate!, queryListParams.toDate!],
        }}
        onSearch={values => {
          const { range } = values;

          if (range) {
            const [from, to] = values.range;
            queryListMethods.addOrReplaceFilterDateColumn(
              'financialSettlementReportTime',
              from!,
              to!
            );
          }

          callbackWithTimeout(refetch);
        }}
      />
      <DevexDataGrid
        id={TABLES.FINANCIAL_SETTLEMENT_REPORT}
        dataSource={items}
        onAddNewClick={onAddClick}
        onRefresh={() => {
          callbackWithTimeout(refetch);
        }}
        onExporting={onExporting}
      >
        <Export enabled={true} />
        <Editing allowUpdating={isUpdate} allowDeleting={isDelete} useIcons />
        <Column type="buttons">
          <Button name="edit" onClick={onEditClick} />
          <Button name="delete" onClick={onDeleteClick} />
        </Column>
        <Column dataField="projectId" caption={t('fields.projectId')}>
          <Lookup dataSource={projects} displayExpr={displayExpr(['name'])} valueExpr={'id'} />
        </Column>
        <Column dataField="projectOwnerId" caption={t('fields.projectOwnerId')}>
          <Lookup dataSource={projectOwners} displayExpr={displayExpr(['name'])} valueExpr={'id'} />
        </Column>
        <Column
          dataField="financialSettlementReportTime"
          caption={t('fields.financialSettlementReportTime')}
          dataType="date"
          alignment="left"
        />
        <Column dataField="code" caption={t('fields.code')} alignment="left" />
        <Column
          dataField="totalAmount"
          caption={t('fields.totalAmount')}
          customizeText={customizeNumberCell()}
        />
        <Column
          dataField="usageStartDate"
          caption={t('fields.usageStartDate')}
          dataType="date"
          alignment="left"
        />
        <Column dataField="budgetFundId" caption={t('fields.budgetFundId')}>
          <Lookup dataSource={budgetFunds} displayExpr={displayExpr(['name'])} valueExpr={'id'} />
        </Column>
      </DevexDataGrid>
      <DeleteConfirmDialog
        isDeleting={isDeleting}
        open={isConfirmDeleteDialogOpen}
        toggle={toggleConfirmDeleteDialog}
        onConfirm={() => {
          deleteTarget();
        }}
        name={getTargetAlias(selectedTarget)}
        model="financialSettlementReport"
      />
    </PageLayout>
  );
};
