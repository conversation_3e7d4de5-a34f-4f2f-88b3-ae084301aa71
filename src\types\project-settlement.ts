import { requiredTextWithNamespace } from '@/lib/i18nUtils';
import { z } from 'zod';
import { recordAttachmentSchema } from './records-attachment';

const requireProjectSettlementText = requiredTextWithNamespace('projectSettlement');

export const projectSettlementSchema = z.object({
  branchId: z.number().nullable(),
  id: z.number(),
  code: z.string().nullable().optional(),
  projectId: z
    .number({
      required_error: requireProjectSettlementText('projectId', 'select'),
      invalid_type_error: requireProjectSettlementText('projectId', 'select'),
    })
    .min(1, requireProjectSettlementText('projectId', 'select')), //dự án
  userCreatedId: z.number().nullable(),
  userCreatedName: z.string().nullable().optional(),
  projectSettlementTime: z.coerce.date().nullable().optional(),
  approvalNumber: z.string().nullable().optional(),
  approvalDate: z.coerce.date().nullable().optional(),
  approvalContent: z.string().nullable().optional(),
  agencyId: z.number().nullable().optional(),
  note: z.string().nullable().optional(),
  ids: z.number().nullable(),
  sort: z.string().nullable().optional(),
  itemsRecordManagement: z.array(recordAttachmentSchema),
});

export type ProjectSettlement = z.infer<typeof projectSettlementSchema>;

export const defaultValuesProjectSettlement: ProjectSettlement = {
  branchId: null, // mã chi nhánh
  id: 0, // Khóa chính
  projectSettlementTime: new Date(), // Ngày lập
  userCreatedId: null, // Người lập
  code: '', // Mã phiếu
  projectId: 0, // Dự án
  note: '', // Ghi chú
  ids: null, // ids
  sort: '',
  approvalNumber: '', // Số quyết định
  approvalDate: new Date(), // Ngày quyết định
  approvalContent: '', // Nội dung quyết định
  itemsRecordManagement: [], // Hồ sơ đính kèm
};
