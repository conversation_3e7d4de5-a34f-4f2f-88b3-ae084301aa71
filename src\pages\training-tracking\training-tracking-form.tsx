import { zod<PERSON>esolver } from '@hookform/resolvers/zod';
import { SyntheticEvent, useMemo, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useParams } from 'react-router-dom';

import { PageLayout } from '@/components/page-layout';
import { Form, FormCombobox, FormField, FormLabel } from '@/components/ui/form';
import { enterLabel, MUTATE, PATHS, PERMISSIONS, QUERIES, selectLabel } from '@/constant';
import { useAuth, useEntity, useFormHandler, usePermission } from '@/hooks';
import { useFormNavigate } from '@/hooks/use-form-navigate';
import { formatDate, toDateType, toLocaleDate } from '@/lib/date';
import { createMutationSuccessFn } from '@/lib/i18nUtils';
import { createPostMutateFn, createPutMutateFn, createQueryByIdFn } from '@/services';

import { DateBox, TextBox } from 'devextreme-react';
import Button from 'devextreme-react/button';

import { TrainingTracking, defaultValuesTrainingTracking, trainingTrackingSchema } from '@/types';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { TrainingTrackingEditableTable } from './training-tracking-editable-table';
import { removeAccents } from '@/lib/text';
import { snakeCase } from 'lodash';
import { useReactToPrint } from 'react-to-print';
import { trainingTrackingPrintTemplate } from './training-tracking-print-template';
import { safeRenderString } from '@/lib/print';
import { groupTrainingTrackingDetails } from './helper/array-group';
const onContractMutationSuccess = createMutationSuccessFn('trainingTracking');

export const TrainingTrackingForm = () => {
  const { id: editId } = useParams();
  const printRef = useRef(null);
  const { t } = useTranslation(['trainingTracking']);
  const exportFileName = snakeCase(removeAccents(t('model')));
  const role = usePermission(PERMISSIONS.TRAINING_TRACKING);
  const { user } = useAuth();
  const { list: allMembers } = useEntity({ queryKey: [QUERIES.USERS], model: 'user' });
  const { list: trainingInstitutionList } = useEntity({ queryKey: [QUERIES.TRAINING_INSTITUTION], model: 'training-institution' });
  const { goBackToList, goToUpdate, goToNew } = useFormNavigate(PATHS.TRAINING_TRACKING);
  const defaultValues = useMemo(
    () => ({
      ...defaultValuesTrainingTracking,
      userCreatedId: user?.userId,
    }),
    [user?.userId]
  );

  const { handleSubmit, loading, methods } = useFormHandler<TrainingTracking>({
    queryKey: [MUTATE.TRAINING_TRACKING, editId],
    mutateKey: [MUTATE.TRAINING_TRACKING],
    queryId: Number(editId) || 0,
    invalidateKey: [QUERIES.TRAINING_TRACKING],
    readFn: createQueryByIdFn<TrainingTracking>('training-tracking'),
    createFn: createPostMutateFn<TrainingTracking>('training-tracking'),
    updateFn: createPutMutateFn<TrainingTracking>('training-tracking'),
    formatPayloadFn: data => ({
      ...data,
      trainingTrackingTime: toLocaleDate(data.trainingTrackingTime || null),
      year: toLocaleDate(data.year)!,
      trainingTrackingDetails: data.trainingTrackingDetails.map(i => ({
        ...i,
        memberIds: i.memberIdArray?.toString(),
        classStartDate: toLocaleDate(i.classStartDate)!,
      })),
    }),
    formatResponseFn: data => ({
      ...data,
      year: toDateType(data.year)!,
      trainingTrackingTime: toDateType(data.trainingTrackingTime || null),
      trainingTrackingDetails: data.trainingTrackingDetails?.map(i => ({
        ...i,
        memberIdArray: i.memberIds?.split(',').map(Number),
        classStartDate: toDateType(i.classStartDate)!,
      })),
    }),
    onCreateSuccess: data => {
      onContractMutationSuccess(data);
      goToUpdate(data);
    },
    onUpdateSuccess: onContractMutationSuccess,
    formOptions: {
      resolver: zodResolver(trainingTrackingSchema),
      defaultValues,
    },
  });
  const year = methods.watch('year');

  const onCreateNew = () => {
    goToNew();
    methods.reset(defaultValues);
  };

  const preprocessPrint = (id: string): { html: string; error: any } => {
    if (id === 'new') {
      return safeRenderString(trainingTrackingPrintTemplate, {
        context: {
          ...defaultValuesTrainingTracking,
          year: formatDate(toLocaleDate(year), 'yyyy'),
          trainingTrackingDetails: [],
        },
      });
    }

    // shallow copy form data
    const formData = {
      ...methods.getValues(),
    };

    const tableTemplate = safeRenderString(trainingTrackingPrintTemplate, {
      context: {
        ...formData,
        year: formatDate(toLocaleDate(year), 'yyyy'),

        trainingTrackingDetails: groupTrainingTrackingDetails(
          formData.trainingTrackingDetails,
          (allMembers || []) as any[],
          (trainingInstitutionList || []) as any[]
        ),

        notes: (formData?.note ?? '').split(/\r?\n/),
      },
    });

    if (tableTemplate.error) {
      console.error('Error rendering string:', tableTemplate.error);
      return { html: '', error: tableTemplate.error };
    }

    if (!tableTemplate.html) {
      console.error;
    }

    return tableTemplate;
  };

  // Khởi tạo mẫu in
  const [printoutElement, setPrintoutElement] = useState(
    <div ref={printRef} dangerouslySetInnerHTML={{ __html: '' }} />
  );

  const printFn = useReactToPrint({
    content: () => printRef.current,
    pageStyle: `
            @media print {
              body { -webkit-print-color-adjust: exact; }
              .dx-datagrid-headers { background-color: #f5f5f5 !important; }
              .dx-datagrid-rowsview { font-size: 12px !important; }
            }
          `,
    // removeAfterPrint: true,
    onBeforeGetContent: () => {
      const table = preprocessPrint(editId as string);
      // cập nhật lại mẫu in
      setPrintoutElement(<div ref={printRef} dangerouslySetInnerHTML={{ __html: table.html }} />);
      return Promise.resolve();
    },
  });
  return (
    <>
      <Form {...methods}>
        <form autoComplete="off">
          <PageLayout
            onSaveChange={e => {
              handleSubmit(e.event?.currentTarget as unknown as SyntheticEvent<HTMLElement>);
            }}
            header={editId !== 'new' ? t('page.form.edit') : t('page.form.addNew')}
            canSaveChange={!isNaN(Number(editId)) ? role?.isUpdate : role?.isCreate}
            isSaving={loading}
            onCancel={goBackToList}
            customElementLeft={
              <>
                <Button
                  text={t('content.createNew', { ns: 'common' })}
                  className="uppercase"
                  stylingMode="outlined"
                  type="default"
                  icon="plus"
                  onClick={onCreateNew}
                />
              </>
            }
            customElementRight={
              <>
                <Button
                  type="default"
                  stylingMode="contained"
                  icon="print"
                  text={t('content.printA4', { ns: 'common' })}
                  disabled={editId === 'new'}
                  onClick={() => {
                    if (printRef.current) {
                      printFn();
                    }
                  }}
                />
              </>
            }
            contentClassName="pb-8"
          >
            <div className="grid grid-cols-1 gap-x-8 gap-y-4 xl:max-w-screen-2xl xl:grid-cols-24">
              {/* cột 1*/}
              <div className="col-span-1 flex flex-col gap-x-8 gap-y-4 xl:col-span-16 2xl:col-span-12">
                <div className="flex items-center md:w-1/2">
                  <FormLabel htmlFor="year" className="hidden w-[70px] md:block">
                    {t('fields.year')}
                  </FormLabel>
                  <FormField
                    id="year"
                    name={'year'}
                    className="min-w-0 flex-1"
                    type="date"
                    label={t('fields.year')}
                  >
                    <DateBox
                      type="date"
                      pickerType="calendar"
                      calendarOptions={{
                        maxZoomLevel: 'decade',
                        minZoomLevel: 'decade',
                      }}
                      displayFormat={'year'}
                      focusStateEnabled={false}
                    />
                  </FormField>
                </div>
                <div className="flex items-center">
                  <FormLabel htmlFor="note" className="hidden w-[70px] md:block ">
                    {t('fields.note')}
                  </FormLabel>
                  <FormField
                    id="note"
                    name={'note'}
                    className="min-w-0 flex-1 md:w-[250px]"
                    label={t('fields.note')}
                  >
                    <TextBox placeholder={`${enterLabel} ${t('fields.note')}`} />
                  </FormField>
                </div>
              </div>
              {/* cột 2*/}
              <div className="col-span-1 flex flex-col gap-x-8 gap-y-4 xl:col-span-8 2xl:col-span-6">
                <div className="flex flex-col gap-x-8 gap-y-4">
                  {/* ngày lập */}
                  <div className="flex items-center">
                    <FormLabel htmlFor="trainingTrackingTime" className="hidden w-[70px] md:block ">
                      {t('fields.trainingTrackingTime')}
                    </FormLabel>
                    <FormField
                      id="trainingTrackingTime"
                      name={'trainingTrackingTime'}
                      className="min-w-0 flex-1 md:w-[250px]"
                      type="date"
                      label={t('fields.trainingTrackingTime')}
                    >
                      <DateBox
                        placeholder={`${enterLabel} ${t('fields.trainingTrackingTime')}`}
                        pickerType="calendar"
                        focusStateEnabled={false}
                      />
                    </FormField>
                  </div>

                  {/* người lập */}
                  <div className="flex items-center">
                    <FormLabel htmlFor="userCreatedId" className="hidden w-[70px] md:block ">
                      {t('fields.userCreatedId')}
                    </FormLabel>
                    <FormField
                      id="userCreatedId"
                      name={'userCreatedId'}
                      className="min-w-0 flex-1 md:w-[250px]"
                      label={t('fields.userCreatedId')}
                    >
                      <FormCombobox
                        model="user"
                        // defaultText={userCreatedName}
                        queryKey={[QUERIES.USERS]}
                        placeholder={`${selectLabel} ${t('fields.userCreatedId')}`}
                        showFields={['name']}
                      />
                    </FormField>
                  </div>
                </div>
              </div>
            </div>
            <div className="mt-8">
              <Tabs defaultValue="detail">
                <div className="w-full">
                  <TabsList>
                    <TabsTrigger value="detail">{t('page.form.tabs.detail')}</TabsTrigger>
                  </TabsList>
                </div>
                <TabsContent value="detail" className="mt-4">
                  <TrainingTrackingEditableTable
                    params={{
                      year: formatDate(toLocaleDate(year), 'yyyy'),

                      outputFileName: `${exportFileName}`,
                    }}
                    role={role}
                  />
                </TabsContent>
              </Tabs>
            </div>
          </PageLayout>
        </form>
      </Form>
      <div id="print-region" className="hidden">
        {printoutElement}
      </div>
    </>
  );
};
