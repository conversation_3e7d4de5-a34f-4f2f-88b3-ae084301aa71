import { Worksheet } from 'exceljs';

interface Column {
  caption?: string;
  dataField?: string;
  columns?: Column[];
}

export function createHeaderRowsFromColumns(columns: Column[], worksheet: Worksheet) {
  const maxDepth = getMaxDepth(columns);

  const rows: string[][] = Array.from({ length: maxDepth }, () => []);

  fillHeaderRows(columns, rows, 0);

  // Add header rows to worksheet
  rows.forEach(row => worksheet.addRow(row));

  // Merge cells
  mergeHeaderCells(columns, worksheet, 0, 1);

  // Style header
  for (let i = 1; i <= maxDepth; i++) {
    const row = worksheet.getRow(i);
    row.height = 30;
    row.eachCell(cell => {
      cell.font = { bold: true };
      cell.alignment = { vertical: 'middle', horizontal: 'center', wrapText: true };
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFDCE6F1' },
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
    });
  }
}

function getMaxDepth(columns: Column[], depth = 1): number {
  return Math.max(
    depth,
    ...columns.map(col => (col.columns ? getMaxDepth(col.columns, depth + 1) : depth))
  );
}

function fillHeaderRows(columns: Column[], rows: string[][], depth: number) {
  columns.forEach(col => {
    const title = getColumnCaption(col);
    rows[depth].push(title);

    const colSpan = getLeafCount(col);
    for (let i = 1; i < colSpan; i++) {
      rows[depth].push('');
    }

    if (col.columns) {
      fillHeaderRows(col.columns, rows, depth + 1);
    }
  });
}

function getLeafCount(column: Column): number {
  if (!column.columns) return 1;
  return column.columns.reduce((sum, child) => sum + getLeafCount(child), 0);
}

function getColumnCaption(column: Column): string {
  if (!column.caption && column.dataField) return column.dataField;
  if (typeof column.caption === 'string' && column.caption.startsWith("t('")) {
    return column.caption; // giữ nguyên t('...')
  }
  return column.caption || '';
}

function mergeHeaderCells(
  columns: Column[],
  worksheet: Worksheet,
  row: number,
  colStart: number
): number {
  let colIndex = colStart;

  columns.forEach(col => {
    const colSpan = getLeafCount(col);

    if (col.columns) {
      // Merge across top row if colspan > 1
      if (colSpan > 1) {
        worksheet.mergeCells(row + 1, colIndex, row + 1, colIndex + colSpan - 1);
      }
      // Merge vertically if needed
      mergeHeaderCells(col.columns, worksheet, row + 1, colIndex);
    } else {
      // Merge vertically down if column doesn't span all rows
      const maxDepth = worksheet.actualRowCount;
      if (maxDepth > row + 1) {
        worksheet.mergeCells(row + 1, colIndex, maxDepth, colIndex);
      }
    }

    colIndex += colSpan;
  });

  return colIndex;
}
