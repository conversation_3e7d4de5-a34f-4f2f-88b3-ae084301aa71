import { ErrorMessage } from '@/components/ui/error-message';
import { TABLES } from '@/constant';
import {
  DisbursementProgressSummary,
  DisbursementProgressSummaryDetail,
  defaultValuesDisbursementProgressSummary,
  IUserPermission,
} from '@/types';
import { useFormContext, useWatch } from 'react-hook-form';

import { CellContext, ColumnDef } from '@tanstack/react-table';

// import { useMediaQuery } from '@/hooks';
import { DataTable, EditableInputCell } from '@/components/data-table';
import { getRandomNumber } from '@/lib/number';
import { useMemo } from 'react';
import { useTWithDefaultParams } from './utils';

const [defaultRow] = defaultValuesDisbursementProgressSummary.disbursementProgressSummaryDetails;

type DisbursementProgressSummaryEditableTableProps = {
  role?: IUserPermission;
  calculateForm?: () => void;
};

export const DisbursementProgressSummaryDetailEditableTable = ({
  role,
  calculateForm,
}: DisbursementProgressSummaryEditableTableProps) => {
  // const isMobile = useMediaQuery('(max-width: 768px)');
  const {
    setValue,
    control,
    formState: { errors },
  } = useFormContext<DisbursementProgressSummary>();

  const [editableData, budgetYear] = useWatch({
    control,
    name: ['disbursementProgressSummaryDetails', 'budgetYear'],
  });
  const { t } = useTWithDefaultParams('disbursementProgressSummary', {
    year: budgetYear?.getFullYear() || 0,
  });

  const disbursementProgressSummaryEditableColumns: ColumnDef<DisbursementProgressSummaryDetail>[] =
    useMemo(() => {
      const groupColumn: ColumnDef<DisbursementProgressSummaryDetail>[] = [
        {
          id: '1',
          header: 'Bảng tóm tắt tiến độ giải ngân',
          columns: [
            {
              id: 'content', // Nội dung
              accessorKey: 'content',
              header: '',
              cell: props => <EditableInputCell {...props} disabled />,
            },

            {
              id: 'value', // Giá trị
              accessorKey: 'value',
              header: '',
              cell: props => <EditableInputCell {...props} type="number" disabled />,
            },

            {
              id: 'unit', // Đvt
              accessorKey: 'unit',
              header: '',
              cell: props => <EditableInputCell {...props} disabled />,
            },
          ],
        },
      ];

      const allowEdittingColumns = [
        'q1PlannedDisbursement',
        'q2PlannedDisbursement',
        'q3PlannedDisbursement',
        'q4PlannedDisbursement',
        'disbursementAccordingQuarter1',
        'disbursementAccordingQuarter2',
        'disbursementAccordingQuarter3',
        'disbursementAccordingQuarter4',
      ];

      const renderValue = (props: CellContext<DisbursementProgressSummaryDetail, unknown>) => {
        const { row } = props;
        if (allowEdittingColumns.includes(props.column.id)) {
          return <EditableInputCell {...props} type="number" />;
        }
        if (props.getValue() === null) {
          return <EditableInputCell {...props} type="text" disabled value={''} />;
        }
        if (row.original.unit === '%') {
          return (
            <EditableInputCell
              {...props}
              type="number"
              disabled
              value={`${(props.getValue() as number).toLocaleString('vi-VN', { maximumFractionDigits: 0 })}%`}
            />
          );
        }
        return <EditableInputCell {...props} type="number" disabled />;
      };
      const otherColumns: ColumnDef<DisbursementProgressSummaryDetail>[] = [
        {
          id: 'month01', // Dự kiến lũy kế giải ngân từ đầu năm đến hết tháng 1/2025
          accessorKey: 'month01',
          header: t('fields.disbursementProgressSummaryDetails.month01'),
        },

        {
          id: 'month01Finance', // Dự kiến lũy kế giải ngân từ đầu năm đến hết tháng 1/2025 UBND TP
          accessorKey: 'month01Finance',
          header: t('fields.disbursementProgressSummaryDetails.month01Finance'),
        },

        {
          id: 'month02', // Dự kiến lũy kế giải ngân từ đầu năm đến hết tháng 2/2025
          accessorKey: 'month02',
          header: t('fields.disbursementProgressSummaryDetails.month02'),
        },

        {
          id: 'month02Finance', // Dự kiến lũy kế giải ngân từ đầu năm đến hết tháng 2/2025 UBND TP
          accessorKey: 'month02Finance',
          header: t('fields.disbursementProgressSummaryDetails.month02Finance'),
        },

        {
          id: 'month03', // Dự kiến lũy kế giải ngân từ đầu năm đến hết tháng 3/2025
          accessorKey: 'month03',
          header: t('fields.disbursementProgressSummaryDetails.month03'),
        },

        {
          id: 'month03Finance', // Dự kiến lũy kế giải ngân từ đầu năm đến hết tháng 3/2025 UBND TP
          accessorKey: 'month03Finance',
          header: t('fields.disbursementProgressSummaryDetails.month03Finance'),
        },

        {
          id: 'q1PlannedDisbursement', // GIẢI NGÂN THEO KH SỐ 191/KH-QLDA Quý I/2025
          accessorKey: 'q1PlannedDisbursement',
          header: t('fields.disbursementProgressSummaryDetails.q1PlannedDisbursement'),
        },

        {
          id: 'disbursementAccordingQuarter1', // GIẢI NGÂN THEO UBND TP Quý I/2025
          accessorKey: 'disbursementAccordingQuarter1',
          header: t('fields.disbursementProgressSummaryDetails.disbursementAccordingQuarter1'),
        },

        {
          id: 'month04', // Dự kiến lũy kế giải ngân từ đầu năm đến hết tháng 4/2025
          accessorKey: 'month04',
          header: t('fields.disbursementProgressSummaryDetails.month04'),
        },

        {
          id: 'month04Finance', // Dự kiến lũy kế giải ngân từ đầu năm đến hết tháng 4/2025 UBND TP
          accessorKey: 'month04Finance',
          header: t('fields.disbursementProgressSummaryDetails.month04Finance'),
        },

        {
          id: 'month05', // Dự kiến lũy kế giải ngân từ đầu năm đến hết tháng 5/2025
          accessorKey: 'month05',
          header: t('fields.disbursementProgressSummaryDetails.month05'),
        },

        {
          id: 'month05Finance', // Dự kiến lũy kế giải ngân từ đầu năm đến hết tháng 5/2025 UBND TP
          accessorKey: 'month05Finance',
          header: t('fields.disbursementProgressSummaryDetails.month05Finance'),
        },

        {
          id: 'month06', // Dự kiến lũy kế giải ngân từ đầu năm đến hết tháng 6/2025
          accessorKey: 'month06',
          header: t('fields.disbursementProgressSummaryDetails.month06'),
        },

        {
          id: 'month06Finance', // Dự kiến lũy kế giải ngân từ đầu năm đến hết tháng 6/2025 UBND TP
          accessorKey: 'month06Finance',
          header: t('fields.disbursementProgressSummaryDetails.month06Finance'),
        },

        {
          id: 'q2PlannedDisbursement', // GIẢI NGÂN THEO KH SỐ 191/KH-QLDA Quý II/2025
          accessorKey: 'q2PlannedDisbursement',
          header: t('fields.disbursementProgressSummaryDetails.q2PlannedDisbursement'),
        },

        {
          id: 'disbursementAccordingQuarter2', // GIẢI NGÂN THEO UBND TP Quý II/2025
          accessorKey: 'disbursementAccordingQuarter2',
          header: t('fields.disbursementProgressSummaryDetails.disbursementAccordingQuarter2'),
        },

        {
          id: 'month07', // Dự kiến lũy kế giải ngân từ đầu năm đến hết tháng 7/2025
          accessorKey: 'month07',
          header: t('fields.disbursementProgressSummaryDetails.month07'),
        },

        {
          id: 'month07Finance', // Dự kiến lũy kế giải ngân từ đầu năm đến hết tháng 7/2025 UBND TP
          accessorKey: 'month07Finance',
          header: t('fields.disbursementProgressSummaryDetails.month07Finance'),
        },

        {
          id: 'month08', // Dự kiến lũy kế giải ngân từ đầu năm đến hết tháng 8/2025 UBND TP
          accessorKey: 'month08',
          header: t('fields.disbursementProgressSummaryDetails.month08'),
        },

        {
          id: 'month08Finance', // Dự kiến lũy kế giải ngân từ đầu năm đến hết tháng 8/2025 UBND TP
          accessorKey: 'month08Finance',
          header: t('fields.disbursementProgressSummaryDetails.month08Finance'),
        },

        {
          id: 'month09', // Dự kiến lũy kế giải ngân từ đầu năm đến hết tháng 9/2025
          accessorKey: 'month09',
          header: t('fields.disbursementProgressSummaryDetails.month09'),
        },

        {
          id: 'month09Finance', // Dự kiến lũy kế giải ngân từ đầu năm đến hết tháng 9/2025 UBND TP
          accessorKey: 'month09Finance',
          header: t('fields.disbursementProgressSummaryDetails.month09Finance'),
        },

        {
          id: 'q3PlannedDisbursement', // GIẢI NGÂN THEO KH SỐ 191/KH-QLDA Quý III/2025
          accessorKey: 'q3PlannedDisbursement',
          header: t('fields.disbursementProgressSummaryDetails.q3PlannedDisbursement'),
        },

        {
          id: 'disbursementAccordingQuarter3', // GIẢI NGÂN THEO UBND TP Quý III/2025
          accessorKey: 'disbursementAccordingQuarter3',
          header: t('fields.disbursementProgressSummaryDetails.disbursementAccordingQuarter3'),
        },

        {
          id: 'month10', // Dự kiến lũy kế giải ngân từ đầu năm đến hết tháng 10/2025
          accessorKey: 'month10',
          header: t('fields.disbursementProgressSummaryDetails.month10'),
        },

        {
          id: 'month10Finance', // Dự kiến lũy kế giải ngân từ đầu năm đến hết tháng 10/2025 UBND TP
          accessorKey: 'month10Finance',
          header: t('fields.disbursementProgressSummaryDetails.month10Finance'),
        },

        {
          id: 'month11', // Dự kiến lũy kế giải ngân từ đầu năm đến hết tháng 11/2025 UBND TP
          accessorKey: 'month11',
          header: t('fields.disbursementProgressSummaryDetails.month11'),
        },

        {
          id: 'month11Finance', // Dự kiến lũy kế giải ngân từ đầu năm đến hết tháng 11/2025 UBND TP
          accessorKey: 'month11Finance',
          header: t('fields.disbursementProgressSummaryDetails.month11Finance'),
        },

        {
          id: 'month12', // Dự kiến lũy kế giải ngân từ đầu năm đến hết tháng 12/2025 UBND TP
          accessorKey: 'month12',
          header: t('fields.disbursementProgressSummaryDetails.month12'),
        },

        {
          id: 'month12Finance', // Dự kiến lũy kế giải ngân từ đầu năm đến hết tháng 12/2025 UBND TP
          accessorKey: 'month12Finance',
          header: t('fields.disbursementProgressSummaryDetails.month12Finance'),
        },

        {
          id: 'q4PlannedDisbursement', // GIẢI NGÂN THEO KH SỐ 191/KH-QLDA Quý IV/2025
          accessorKey: 'q4PlannedDisbursement',
          header: t('fields.disbursementProgressSummaryDetails.q4PlannedDisbursement'),
        },

        {
          id: 'disbursementAccordingQuarter4', // GIẢI NGÂN THEO UBND TP Quý IV/2025
          accessorKey: 'disbursementAccordingQuarter4',
          header: t('fields.disbursementProgressSummaryDetails.disbursementAccordingQuarter4'),
        },
        // {
        //   id: 'removeRow',
        //   header: ' ',
        //   size: 10,
        //   cell: props => {
        //     return (
        //       <DataTableRowActions
        //         onDelete={() => {
        //           props.table.options.meta?.removeRowByIndex(props.row.index);
        //         }}
        //         canDelete={role?.isCreate || role?.isUpdate}
        //       />
        //     );
        //   },
        // },
      ];

      otherColumns.forEach(column => {
        if (!column.cell) {
          column.cell = renderValue;
        }
      });

      // const groupCount = groupColumn.length;
      // const columns = [...groupColumn];
      // otherColumns.forEach((item, index) => {

      //   columns.push({
      //     id: `${index + groupCount + 1}`,
      //     columns: [item],
      //   });
      // });

      const columns: ColumnDef<DisbursementProgressSummaryDetail>[] = [...groupColumn];
      otherColumns.forEach((item, index) => {
        const groupedColumn: ColumnDef<DisbursementProgressSummaryDetail> = {
          id: `${index + 1}`,
          columns: [item],
        };

        // Copy size properties from child column to parent group column
        if (item.size !== undefined) {
          groupedColumn.size = item.size;
        }
        if (item.maxSize !== undefined) {
          groupedColumn.maxSize = item.maxSize;
        }
        if (item.minSize !== undefined) {
          groupedColumn.minSize = item.minSize;
        }

        columns.push(groupedColumn);
      });
      return columns;
    }, [role?.isCreate, role?.isUpdate, t]);

  return (
    <div>
      <>
        <div className="col-span-1 flex justify-end xl:col-span-3 xl:justify-start">
          {/* <Button
              text={tcommon('action.getData')}
              className="w-fit"
              stylingMode="contained"
              type="default"
              icon="search"
              onClick={() => void handleGetData()}
              disabled={loadingData}
              //
            /> */}
        </div>
        <div className="col-span-1 xl:col-span-24">
          <DataTable
            tableId={TABLES.DISBURSEMENT_PROGRESS_SUMMARY_DETAIL}
            sortColumn="id"
            role={role}
            editableData={editableData}
            setEditableData={editedData => {
              setValue('disbursementProgressSummaryDetails', editedData);
              calculateForm?.();
            }}
            manualPagination
            initialState={{
              pagination: {
                pageSize: editableData.length,
              },
            }}
            onAddButtonClick={table => {
              const newRow = { ...defaultRow, id: -getRandomNumber() };
              table.options.meta?.addNewRow(newRow);
            }}
            syncQueryParams={false}
            columns={disbursementProgressSummaryEditableColumns}
            customToolbar={() => {
              return (
                <>
                  {errors.disbursementProgressSummaryDetails?.message && (
                    <ErrorMessage message={errors.disbursementProgressSummaryDetails?.message} />
                  )}
                </>
              );
            }}
            hideAutoNumberedColumn
          />
        </div>
      </>
    </div>
  );
};
