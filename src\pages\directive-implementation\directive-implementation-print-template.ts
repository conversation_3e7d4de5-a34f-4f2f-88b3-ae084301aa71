export const directiveImplementationTargetPrintTemplate = `{% for target in targetDetails %}
<p style="margin-top: 12pt; margin-bottom: 12pt; text-indent: 28.35pt; text-align: justify; font-size: 14pt;">
  <span style="font-family: 'Times New Roman'; font-weight: bold; letter-spacing: normal;">{{ target.ordinalNumber }}</span>
  <span style="font-family: 'Times New Roman'; letter-spacing: normal;">{{ target.name }}</span>
</p>
<p style="margin-top: 12pt; margin-bottom: 12pt; text-indent: 28.35pt; text-align: justify; font-size: 14pt;">
  <span style="font-family: 'Times New Roman'; font-weight: bold; letter-spacing: normal;">Tiến độ hiện nay:</span>
  <span style="font-family: 'Times New Roman'; letter-spacing: normal;">{{ target.progress }}</span>
</p>
{% endfor%}`;

export const directiveImplementationTaskPrintTemplate = `{% for task in taskDetails %}
<p style="margin-top: 12pt; margin-bottom: 12pt; text-indent: 28.35pt; text-align: justify; font-size: 14pt;">
  <span style="font-family: 'Times New Roman'; font-weight: bold; letter-spacing: normal;">{{ task.ordinalNumber }}</span>
  <span style="font-family: 'Times New Roman'; letter-spacing: normal;">{{ task.name }}</span>
</p>
<p style="margin-top: 12pt; margin-bottom: 12pt; text-indent: 28.35pt; text-align: justify; font-size: 14pt;">
  <span style="font-family: 'Times New Roman'; font-weight: bold; letter-spacing: normal;">Tiến độ hiện nay:</span>
  <span style="font-family: 'Times New Roman'; letter-spacing: normal;">{{ task.progress }}</span>
</p>
{% endfor%}`;
