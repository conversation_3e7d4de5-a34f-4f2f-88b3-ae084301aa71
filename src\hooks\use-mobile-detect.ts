import { useEffect, useState } from 'react';

// Hook để phát hiện thiết bị di động
const useMobileDetect = () => {
  const [isMobilePhone, setIsMobilePhone] = useState(false);

  useEffect(() => {
    const checkDevice = () => {
      // Kiểm tra user-agent
      const userAgent = navigator.userAgent || navigator.vendor || (window as any).opera;
      const userAgentStr = String(userAgent).toLowerCase();

      // Phát hiện thiết bị có phải là điện thoại không, chỉ dựa vào user-agent, không dựa vào kích thước màn hình
      const isMobile =
        /iphone|ipod|android.*mobile|windows.*phone|blackberry|iemobile|opera.*mini/i.test(
          userAgentStr
        );

      // Phát hiện tablet để loại trừ
      const isTablet = /ipad|tablet|android(?!.*mobile)|tab(?!-|let)/i.test(userAgentStr);

      // Tr<PERSON> về true nếu là điện thoại và không phải tablet
      return isMobile && !isTablet;
    };

    // Kiểm tra một lần duy nhất khi component mount
    // và lưu kết quả vào state, không cần kiểm tra lại khi resize
    setIsMobilePhone(checkDevice());
  }, []);

  return isMobilePhone;
};
export default useMobileDetect;
