import { requiredTextWithNamespace } from '@/lib/i18nUtils';
import { z } from 'zod';
import { ArrayElement } from './common';

const requireTrainingTrackingText = requiredTextWithNamespace('trainingTracking');

export const trainingTrackingSchema = z.object({
  id: z.number(), //id
  year: z.date(),
  note: z.string().nullable().optional(),
  trainingTrackingTime: z.date().nullable().optional(),

  userCreatedId: z
    .number({
      required_error: requireTrainingTrackingText('userCreatedId'),
      invalid_type_error: requireTrainingTrackingText('userCreatedId'),
    })
    .min(1, requireTrainingTrackingText('userCreatedId')),
  //các trường mặc định
  branchId: z.number().nullable().optional(),
  storeId: z.number().nullable().optional(),
  statusId: z.number().nullable().optional(),
  trainingTrackingDetails: z.array(
    z.object({
      id: z.number(),
      memberIds: z.string().nullable().optional(),
      memberIdArray: z.array(z.number()).nullable().optional(),
      trainingTrackingId: z.number().nullable().optional(),
      trainingInstitutionId: z.number().nullable().optional(),
      note: z.string().nullable().optional(),
      roomName: z
        .string({
          required_error: requireTrainingTrackingText('roomName'),
          invalid_type_error: requireTrainingTrackingText('roomName'),
        })
        .min(1, {
          message: requireTrainingTrackingText('trainingTrackingDetails.roomName'),
        }),
      quantityMember: z.number().nullable().optional(),
      classStartDate: z.date(),
    })
  ),
});

export type TrainingTracking = z.infer<typeof trainingTrackingSchema>;
export type TrainingTrackingDetail = ArrayElement<TrainingTracking['trainingTrackingDetails']>;

export const defaultValuesTrainingTracking: TrainingTracking = {
  id: 0, //id
  year: new Date(),
  note: '',
  trainingTrackingTime: new Date(),
  userCreatedId: 0,
  //các trường mặc định
  branchId: null,
  storeId: null,
  trainingTrackingDetails: [
    {
      id: 0,
      memberIds: '',
      memberIdArray: [],
      trainingInstitutionId: 0,
      trainingTrackingId: 0,
      note: '',
      roomName: '',
      quantityMember: 0,
      classStartDate: new Date(),
    },
  ],
};
