import { CellContext, ColumnDef, GroupColumnDef, Table } from '@tanstack/react-table';
import { useCallback, useEffect, useMemo, useRef } from 'react';

import { BUSINESS_TABLE_APPEARANCE, QUERIES } from '@/constant';
import { useEntity } from '@/hooks/use-entity';
import { translationWithNamespace } from '@/lib/i18nUtils';
import notification from '@/lib/notifications';
import { getRandomNumber } from '@/lib/number';
import { getDistinctRecords } from '@/lib/utils';
import { ConstructionTask, Contractor, DataTableProps, IUserPermission, Unit } from '@/types';

import {
  ConstructionTaskPropsByUnit,
  geConstructionTaskInfoByUnitLabel,
  getRowOnConstructionTaskChange,
  getUnitsByConstructionTask,
  updateRowOnUnitChange,
} from '@/components/business-construction-task-editable-data-table';
import {
  DataTableRowActions,
  EditableDatePickerCell,
  EditableDropdownCell,
  EditableInputCell,
} from '@/components/data-table';
import { ChevronDown, ChevronRight } from 'lucide-react';
import { Combobox } from '../combobox';
import { DataTable } from '../data-table';
import { Button } from '../ui/button';

const t = translationWithNamespace('businessTable');

const getEditableColumns = <TData extends DefaultDataProps>(
  rawColumns: GroupColumnDef<TData>[],
  getAdditionalFieldsCallback: (values: TData, column?: keyof TData) => Partial<TData>
): GroupColumnDef<TData>[] => {
  return rawColumns.map(column => {
    const child = column?.columns?.length
      ? getEditableColumns(
          (column?.columns as GroupColumnDef<TData>[]) ?? [],
          getAdditionalFieldsCallback
        )
      : [];
    if (!column.meta?.editable) {
      return {
        ...column,
        columns: child,
      };
    }

    return {
      ...column,
      cell: (props: CellContext<TData, unknown>) => {
        switch (column.meta?.editable?.type) {
          case 'number': {
            const {
              table,
              column: { id },
              row: { original },
            } = props;
            const { editable } = column.meta;

            return (
              <EditableInputCell
                {...props}
                type="number"
                // Truyền các props bổ sung từ meta
                hideDecimal={editable.hideDecimal}
                readOnly={editable.readOnly}
                isMoney={editable.isMoney}
                decimal={editable.decimal}
                onValueChange={value => {
                  const updatedFieldValue = { [id]: Number(value) };
                  const updatedFields = {
                    ...updatedFieldValue,
                    ...getAdditionalFieldsCallback({ ...original, ...updatedFieldValue }, id),
                  };
                  table.options.meta?.updateRowValues?.(updatedFields, props.row.index);
                }}
              />
            );
          }
          case 'text':
            return <EditableInputCell {...props} />;
          case 'date':
            return <EditableDatePickerCell {...props} />;
          case 'multiple': {
            const { editable } = column.meta;
            return (
              <EditableDropdownCell
                {...props}
                defaultText={String(props.row.original[props.column.id.replace('Id', 'Name')])}
                model={editable.model}
                queryKey={editable.queryKey}
                createQueryFn={editable.createQueryFn}
              />
            );
          }
          default:
            return;
        }
      },
      columns: child,
    };
  });
};

// export type BusinessWarehouse = Warehouse & {
//   warehouseId: number;
//   currentStockQuantity?: number;
//   rowId?: number;
// };

export type DefaultDataProps = {
  id: number;
  constructionTaskId: ConstructionTask['id'] | null;
  unitId: ConstructionTask['unitId'] | null;
  units?: ConstructionTaskPropsByUnit[];
  constructionTaskCode?: ConstructionTask['code'] | null;
  constructionTaskName?: ConstructionTask['name'] | null;
  contractorName?: Contractor['name'] | null;
  contractAppendixName?: Contractor['name'] | null;
  unitName?: Unit['name'] | null;
} & Record<string, unknown>;

interface BusinessEditableDataTableProps<TData, TValue> extends DataTableProps<TData, TValue> {
  columns: (GroupColumnDef<TData> & ColumnDef<TData>)[];
  defaultRow: TData;
  getCalculatedRowValues: (values: TData, columnName?: keyof TData) => Partial<TData>;
  pickupConstructionTaskFields?: (
    unit: ConstructionTaskPropsByUnit
  ) => Promise<ConstructionTaskPropsByUnit>;
  pickupUnitFields?: (unit: ConstructionTaskPropsByUnit) => Promise<ConstructionTaskPropsByUnit>;
  editableData: TData[];
  setEditableData: (newData: TData[]) => void;
  role?: IUserPermission;
  onConstructionTaskSelected?: (value: TData | undefined) => TData | void;
  isAddRow?: boolean;
  showSetting?: boolean;
  showPagination?: boolean;
  notAutoAppendRow?: boolean;
  constructionTaskColumnName?: string;
  showContractorColumn?: boolean;
  disableConstructionTaskColumn?: boolean;
  disableUnitColumn?: boolean;
  allowRemoveRow?: boolean;
}

const defaultPickupFieldsCallback = (value: ConstructionTaskPropsByUnit) => {
  return new Promise<typeof value>(resolve => {
    resolve(value);
  });
};

export const BusinessConstructionTaskEditableDataTable = <TData extends DefaultDataProps, TValue>({
  columns,
  defaultRow,
  getCalculatedRowValues,
  pickupConstructionTaskFields = defaultPickupFieldsCallback,
  pickupUnitFields = defaultPickupFieldsCallback,
  editableData,
  setEditableData,
  role,
  customToolbar,
  onConstructionTaskSelected,
  initialState,
  isAddRow = true,
  showSetting,
  showPagination,
  notAutoAppendRow,
  renderSubRow,
  constructionTaskColumnName = translationWithNamespace('businessTable')(
    'fields.constructionTaskId'
  ),
  showContractorColumn = false,
  disableConstructionTaskColumn = false,
  disableUnitColumn = false,
  allowRemoveRow = true,
  ...props
}: BusinessEditableDataTableProps<TData, TValue>) => {
  const { entities: constructionTaskHash } = useEntity<ConstructionTask>({
    queryKey: [QUERIES.CONSTRUCTION_TASK],
    model: 'construction-task',
  });

  useEntity({
    queryKey: [QUERIES.UNIT],
    model: 'unit',
  });

  useEffect(() => {
    const isRowInfoIncomplete = editableData.some(item => {
      if (item.unitId === null || item.unitId === undefined) {
        return;
      }
      return item.constructionTaskId && !item.units?.length;
    });

    const shouldAutoLoadData = editableData.length && isRowInfoIncomplete && constructionTaskHash;
    if (!shouldAutoLoadData) return;

    const getRowWithUnits = (row: TData) => {
      const emptyUnits = [
        {
          id: -1,
          name: '',
          constructionTaskId: row.constructionTaskId,
        },
      ];

      const units =
        !row.units?.length && row.constructionTaskId && constructionTaskHash
          ? getUnitsByConstructionTask(constructionTaskHash[row.constructionTaskId]) || emptyUnits
          : row.units;

      return { ...row, units };
    };

    const newEditableData = editableData.map(item => {
      const rowWithUnits = getRowWithUnits(item);
      return rowWithUnits;
    });

    setEditableData(newEditableData);
  }, [editableData, constructionTaskHash, setEditableData]);

  const calculatedRowsValuesFnRef = useRef(getCalculatedRowValues);

  // const onWarehouseSelectedFnRef = useRef(onWarehouseSelected);

  const onConstructionTaskSelectedFnRef = useRef(onConstructionTaskSelected);

  const pickupUnitFieldsFnRef = useRef(pickupUnitFields);
  const pickupConstructionTaskFieldsFnRef = useRef(pickupConstructionTaskFields);

  const setEditableDataFnRef = useRef(setEditableData);

  // Update ref for calculatedRowsValues when it changes
  calculatedRowsValuesFnRef.current = getCalculatedRowValues;

  useEffect(() => {
    calculatedRowsValuesFnRef.current = getCalculatedRowValues;
  }, [getCalculatedRowValues]);

  // // Update ref for onWarehouseSelected when it changes
  // useEffect(() => {
  //   onWarehouseSelectedFnRef.current = onWarehouseSelected;
  // }, [onWarehouseSelected]);

  // Update ref for onConstructionTaskSelected when it changes
  useEffect(() => {
    onConstructionTaskSelectedFnRef.current = onConstructionTaskSelected;
  }, [onConstructionTaskSelected]);

  // Update ref for pickupUnitFields when it changes
  useEffect(() => {
    pickupUnitFieldsFnRef.current = pickupUnitFields;
  }, [pickupUnitFields]);

  // Update ref for pickupConstructionTaskFields when it changes
  useEffect(() => {
    pickupConstructionTaskFieldsFnRef.current = pickupConstructionTaskFields;
  }, [pickupConstructionTaskFields]);

  // Update ref for setEditableData when it changes
  useEffect(() => {
    setEditableDataFnRef.current = setEditableData;
  }, [setEditableData]);

  const editableColumns = useMemo(() => {
    return getEditableColumns(columns, calculatedRowsValuesFnRef.current);
  }, [columns]);

  /**
   * Handles the selection of a construction task in the table.
   * Retrieves custom fields for the selected construction task, calculates additional values for the row,
   * and updates the cell with the selected construction task and additional fields.
   * Appends a default row after construction task selection.
   * @param cellProps - The props object containing the cell properties.
   * @param selectedConstructionTask - The selected construction task.
   */
  const handleConstructionTaskSelection = useCallback(
    async (cellProps: CellContext<TData, unknown>, selectedConstructionTask: ConstructionTask) => {
      const getAdditionalFields = async (
        selectedConstructionTask: ConstructionTask,
        cellProps: CellContext<TData, unknown>
      ) => {
        // Retrieve custom fields for the selected construction - task
        const constructionTaskInfoByUnitLabel =
          geConstructionTaskInfoByUnitLabel(selectedConstructionTask);
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const { id, ...customFields } =
          (await pickupConstructionTaskFieldsFnRef.current?.(constructionTaskInfoByUnitLabel)) ??
          {};

        const base = { ...cellProps.row.original, ...customFields };
        const additionalFields = { ...base, ...calculatedRowsValuesFnRef.current?.(base) };

        return additionalFields;
      };

      const getMutatedRowValues = async (
        mutateConstructionTask: ConstructionTask,
        cellProps: CellContext<TData, unknown>
      ) => {
        const mutatedConstructionTaskAdditionFields = await getAdditionalFields(
          mutateConstructionTask,
          cellProps
        );

        return getRowOnConstructionTaskChange<TData>(
          mutateConstructionTask,
          mutatedConstructionTaskAdditionFields
        );
      };

      const selectedConstructionTaskFullRowValues = await getMutatedRowValues(
        selectedConstructionTask,
        cellProps
      );

      const newEditableData = [...cellProps.table.options.data];
      const editingIndex = newEditableData.findIndex(
        item => item.id === selectedConstructionTaskFullRowValues.id
      );

      if (editingIndex !== -1) {
        newEditableData[editingIndex] = {
          ...selectedConstructionTaskFullRowValues,
          ...onConstructionTaskSelectedFnRef.current?.(selectedConstructionTaskFullRowValues),
        };
      }

      const isTheLastRowEmpty =
        newEditableData?.[newEditableData.length - 1]?.constructionTaskId !== 0;

      if (defaultRow && !notAutoAppendRow && isTheLastRowEmpty) {
        newEditableData.push({ ...defaultRow, id: -getRandomNumber() });
      }

      setEditableDataFnRef.current?.(newEditableData);
    },
    [defaultRow, notAutoAppendRow]
  );

  /**
   * Handles the selection of a unit in the table.
   * Retrieves custom fields for the selected unit, calculates additional values for the row,
   * and updates the cell with the selected unit and additional fields.
   * @param cellProps - The props object containing the cell properties.
   * @param selectedUnit - The selected unit.
   */
  const handleUnitSelection = useCallback(
    async (cellProps: CellContext<TData, unknown>, selectedUnit: ConstructionTaskPropsByUnit) => {
      // Retrieve custom fields for the selected unit
      const customFields = await pickupUnitFieldsFnRef.current?.(selectedUnit);

      // Calculate additional values for the row using custom fields
      const additionalFields = {
        ...customFields,
        ...calculatedRowsValuesFnRef.current?.({ ...cellProps.row.original, ...customFields }),
      };

      updateRowOnUnitChange(cellProps, selectedUnit, additionalFields);
    },
    []
  );

  const businessColumns: ColumnDef<TData>[] = useMemo(() => {
    const result: ColumnDef<TData>[] = [
      {
        id: 'constructionTaskId',
        accessorKey: 'constructionTaskId',
        header: constructionTaskColumnName,
        maxSize: 450,
        cell: props => {
          return (
            <EditableDropdownCell
              {...props}
              queryKey={[QUERIES.CONSTRUCTION_TASK]}
              model="construction-task"
              showFields={['name']}
              defaultText={props.row.original.constructionTaskName}
              onSelectItem={(selectedConstructionTask: ConstructionTask | null) => {
                if (disableConstructionTaskColumn) return;
                if (selectedConstructionTask) {
                  handleConstructionTaskSelection(props, selectedConstructionTask).catch(
                    (error: { message: string }) => {
                      notification.error(error.message);
                    }
                  );
                } else {
                  props.table.options.meta?.updateData(props.row.index, props.column.id, 0);
                }
              }}
              disabled={disableConstructionTaskColumn}
            />
          );
        },
      },
    ];

    if (renderSubRow) {
      result.unshift({
        id: 'toggleExpandableAll',
        header: props => {
          return (
            <Button
              variant={'ghost'}
              onClick={e => {
                e.stopPropagation();
                e.preventDefault();
                props.table.toggleAllRowsExpanded();
              }}
            >
              {props.table.getIsAllRowsExpanded() ? (
                <ChevronDown size={16} />
              ) : (
                <ChevronRight size={16} />
              )}
            </Button>
          );
        },
        cell: props => {
          return (
            <Button
              size="icon"
              variant="ghost"
              onClick={() => {
                props.row.toggleExpanded();
              }}
            >
              {props.row.getIsExpanded() ? <ChevronDown size={16} /> : <ChevronRight size={16} />}
            </Button>
          );
        },
        size: 20,
      });
    }

    if (showContractorColumn) {
      result.push({
        id: 'contractorId',
        accessorKey: 'contractorId',
        header: t('fields.contractorId'),
        size: 200,
        cell: props => {
          return (
            <EditableDropdownCell
              {...props}
              model="contractor"
              queryKey={[QUERIES.CONTRACTOR]}
              defaultText={props.row.original.contractorName}
            />
          );
        },
      });
    }

    result.push({
      id: 'unitId',
      accessorKey: 'unitId',
      header: t('fields.unitId'),
      size: 150,
      cell: props => {
        let units;
        const { original } = props.row;

        if (original.units) {
          units = original.units;
        } else if (original.constructionTaskId && constructionTaskHash) {
          units = getUnitsByConstructionTask(constructionTaskHash[original.constructionTaskId]);
        }

        if (units) {
          units = getDistinctRecords(units);
        }

        return (
          <Combobox<ConstructionTaskPropsByUnit>
            value={props.getValue<number>()}
            options={units || []}
            className="rounded-none border-none"
            placeholder=""
            defaultText={props.row.original.unitName}
            onSelectItem={(selectedUnit: ConstructionTaskPropsByUnit | null) => {
              if (selectedUnit) {
                handleUnitSelection(props, selectedUnit).catch((errors: { message: string }) => {
                  console.error('error:', errors.message);
                });
              }
            }}
            disabled={disableUnitColumn}
          />
        );
      },
    });

    result.push(...editableColumns);

    if (allowRemoveRow) {
      result.push({
        id: 'removeRow',
        header: '',
        size: 10,
        cell: props => {
          return (
            <DataTableRowActions
              onDelete={() => {
                props.table.options.meta?.removeRowByIndex(props.row.index);
              }}
              canDelete={role?.isCreate || role?.isUpdate}
            />
          );
        },
      });
    }

    return result;
  }, [
    allowRemoveRow,
    constructionTaskColumnName,
    constructionTaskHash,
    disableConstructionTaskColumn,
    disableUnitColumn,
    editableColumns,
    handleConstructionTaskSelection,
    handleUnitSelection,
    renderSubRow,
    role?.isCreate,
    role?.isUpdate,
    showContractorColumn,
  ]);

  const onAddButtonClick = (table: Table<TData>) => {
    if (!defaultRow) {
      return;
    }
    table.options.meta?.addNewRow({ ...defaultRow, id: -getRandomNumber() });
  };
  const onDeleteAllButtonClick = () => {
    setEditableData([]);
  };
  return (
    <DataTable
      role={role}
      columns={businessColumns}
      editableData={editableData}
      setEditableData={setEditableData}
      customToolbar={customToolbar}
      onAddButtonClick={isAddRow ? onAddButtonClick : undefined}
      onDeleteAllButtonClick={editableData.length ? onDeleteAllButtonClick : undefined}
      showSetting={showSetting}
      showPagination={showPagination}
      syncQueryParams={false}
      manualGrouping={false}
      renderSubRow={renderSubRow}
      initialState={{ ...BUSINESS_TABLE_APPEARANCE, ...initialState }}
      {...props}
    />
  );
};
