import { DeleteConfirmDialog } from '@/components/confirm-dialog';
import { customizeNumberCell, DevexDataGrid } from '@/components/devex-data-grid';
import { PageLayout } from '@/components/page-layout';
import { removeAccents } from '@/lib/text';
import { PeriodFilter, PeriodFilterForm } from '@/components/period-filter-form';
import { MUTATE, PATHS, PERMISSIONS, QUERIES, TABLES } from '@/constant';
import { useDataTable, useEntity, usePermission } from '@/hooks';
import { createExportingEvent } from '@/lib/file';
import { callbackWithTimeout, displayExpr } from '@/lib/utils';
import { createDeleteMutateFn, createQueryPaginationFn } from '@/services';
import { translationWithNamespace } from '@/lib/i18nUtils';
import { useQuery } from '@tanstack/react-query';
import { Button, Column, Editing, Export, Lookup } from 'devextreme-react/data-grid';
import { ColumnButtonClickEvent } from 'devextreme/ui/data_grid';
import { snakeCase } from 'lodash';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { AdjustedCapitalIncreasePlan } from '@/types/adjusted-capital-increase-plan';

const t = translationWithNamespace('adjustedCapitalIncreasePlan');
const path = PATHS.ADJUSTED_CAPITAL_INCREASE_PLAN;
const exportFileName = snakeCase(removeAccents(t('model')));

const onExporting = createExportingEvent(`${exportFileName}.xlsx`, 'Main');

export const AdjustedCapitalIncreasePlanDataTable = () => {
  const navigate = useNavigate();
  const { t } = useTranslation('adjustedCapitalIncreasePlan');

  const role = usePermission(PERMISSIONS.ADJUSTED_CAPITAL_INCREASE_PLAN);

  const { list: users } = useEntity({ queryKey: [QUERIES.USERS], model: 'user' });
  const { list: budgetFunds } = useEntity({
    queryKey: [QUERIES.BUDGET_FUND],
    model: 'budget-fund',
  });
  const { list: capitalIncreasePlans } = useEntity({
    queryKey: [QUERIES.CAPITAL_INCREASE_PLAN],
    model: 'capital-increase-plan',
  });

  const getTargetAlias = (target: AdjustedCapitalIncreasePlan | undefined) => {
    if (!target) {
      return '';
    }
    return target.code!;
  };

  const {
    selectedTarget,

    isConfirmDeleteDialogOpen,
    toggleConfirmDeleteDialog,
    selectTargetToDelete,
    deleteTarget,
    isDeleting,

    queryListParams,
    queryListMethods,
    // Query
  } = useDataTable<AdjustedCapitalIncreasePlan, PeriodFilter>({
    queryRangeName: 'adjustedCapitalIncreasePlanTime',
    getTargetAlias,
    deleteFn: createDeleteMutateFn<AdjustedCapitalIncreasePlan>('adjusted-capital-increase-plan'),
    deleteKey: [MUTATE.DELETE_ADJUSTED_CAPITAL_INCREASE_PLAN],
    invalidateKey: [QUERIES.ADJUSTED_CAPITAL_INCREASE_PLAN],
  });

  const { data, refetch } = useQuery({
    queryKey: [QUERIES.ADJUSTED_CAPITAL_INCREASE_PLAN],
    queryFn: () => {
      return createQueryPaginationFn<AdjustedCapitalIncreasePlan>('adjusted-capital-increase-plan')(
        {
          pageIndex: 1,
          pageSize: -1,
          sortColumn: 'AdjustedCapitalIncreasePlanTime',
          sortOrder: 1,
          isPage: false,
          filterColumn: [],
          ...queryListParams,
        }
      );
    },
  });

  const { items } = data || { items: [] };

  const onEditClick = (e: ColumnButtonClickEvent<AdjustedCapitalIncreasePlan>) => {
    if (e.row?.data) {
      navigate(`${path}/` + e.row.data?.id, { state: path });
    }
  };

  const onAddClick = () => {
    navigate(`${path}/new`, { state: path });
  };

  const onDeleteClick = (e: ColumnButtonClickEvent<AdjustedCapitalIncreasePlan>) => {
    if (e.row?.data) {
      selectTargetToDelete(e.row.data);
    }
  };

  const { isUpdate, isDelete } = role || {};

  return (
    <PageLayout header={t('page.header')}>
      <PeriodFilterForm
        defaultSearchValues={{
          range: [queryListParams.fromDate!, queryListParams.toDate!],
        }}
        onSearch={values => {
          const { range } = values;

          if (range) {
            const [from, to] = values.range;
            queryListMethods.addOrReplaceFilterDateColumn(
              'adjustedCapitalIncreasePlanTime',
              from!,
              to!
            );
          }

          callbackWithTimeout(refetch);
        }}
      />
      <DevexDataGrid
        id={TABLES.ADJUSTED_CAPITAL_INCREASE_PLAN}
        dataSource={items}
        onAddNewClick={onAddClick}
        onRefresh={() => {
          callbackWithTimeout(refetch);
        }}
        onExporting={onExporting}
      >
        <Export enabled={true} />
        <Editing allowUpdating={isUpdate} allowDeleting={isDelete} useIcons />

        {/* thao tác */}
        <Column type="buttons">
          <Button name="edit" onClick={onEditClick} />
          <Button name="delete" onClick={onDeleteClick} />
        </Column>

        {/* Mã phiếu */}
        <Column dataField="code" caption={t('fields.code')} alignment="left" width={150} />

        {/* Ngày lập */}
        <Column
          dataField="adjustedCapitalIncreasePlanTime"
          caption={t('fields.adjustedCapitalIncreasePlanTime')}
          dataType="date"
          alignment="left"
          width={100}
        />

        {/* Người lập */}
        <Column dataField="userCreatedId" caption={t('fields.userCreatedId')} width={150}>
          <Lookup dataSource={users} displayExpr={displayExpr(['name'])} valueExpr={'id'} />
        </Column>

        {/* Từ kế hoạch giao vốn */}
        <Column
          dataField="capitalIncreasePlanId"
          caption={t('fields.capitalIncreasePlanId')}
          width={150}
        >
          <Lookup
            dataSource={capitalIncreasePlans}
            displayExpr={displayExpr(['approvalNumber'])}
            valueExpr={'id'}
          />
        </Column>

        {/* Số quyết định */}
        <Column
          dataField="approvalNumber"
          caption={t('fields.approvalNumber')}
          alignment="left"
          width={150}
        />

        {/* Nguồn ngân sách */}
        <Column dataField="budgetFundId" caption={t('fields.budgetFundId')}>
          <Lookup dataSource={budgetFunds} displayExpr={displayExpr(['name'])} valueExpr={'id'} />
        </Column>

        {/* Ngày quyết định */}
        <Column
          dataField="approvalDate"
          caption={t('fields.approvalDate')}
          dataType="date"
          alignment="left"
          width={200}
        />

        {/* Năm ngân sách */}
        <Column
          dataField="budgetYear"
          caption={t('fields.budgetYear')}
          dataType="date"
          alignment="left"
          format="yyyy"
          width={100}
        />

        {/* Tổng tiền */}
        <Column
          dataField="totalAmount"
          caption={t('fields.totalAmount')}
          dataType="number"
          alignment="right"
          customizeText={customizeNumberCell()}
          width={200}
        />

        {/* Ghi chú */}
        <Column dataField="note" caption={t('fields.note')} alignment="left" width={150} />
      </DevexDataGrid>
      <DeleteConfirmDialog
        isDeleting={isDeleting}
        open={isConfirmDeleteDialogOpen}
        toggle={toggleConfirmDeleteDialog}
        onConfirm={() => {
          deleteTarget();
        }}
        name={getTargetAlias(selectedTarget)}
        model="adjustedCapitalIncreasePlan"
      />
    </PageLayout>
  );
};
