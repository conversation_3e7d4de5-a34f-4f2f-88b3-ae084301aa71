import { requiredTextWithNamespace } from '@/lib/i18nUtils';
import { z } from 'zod';

export const requireDisbursementProgressByFundingSource = requiredTextWithNamespace(
  'disbursementProgressByFundingSource'
);

// Tạo các trường weekXX và landWeekXX tự động
const weekFields: Record<string, z.ZodTypeAny> = {};
for (let i = 1; i <= 52; i++) {
  const weekNum = String(i).padStart(2, '0');
  weekFields[`week${weekNum}`] = z.string().nullable().optional();
  weekFields[`landWeek${weekNum}`] = z.string().nullable().optional();
}

const monthFields = z.object({
  month01: z.number().nullable().optional(),
  month02: z.number().nullable().optional(),
  month03: z.number().nullable().optional(),
  month04: z.number().nullable().optional(),
  month05: z.number().nullable().optional(),
  month06: z.number().nullable().optional(),
  month07: z.number().nullable().optional(),
  month08: z.number().nullable().optional(),
  month09: z.number().nullable().optional(),
  month10: z.number().nullable().optional(),
  month11: z.number().nullable().optional(),
  month12: z.number().nullable().optional(),

  month01Finance: z.number().nullable().optional(),
  month02Finance: z.number().nullable().optional(),
  month03Finance: z.number().nullable().optional(),
  month04Finance: z.number().nullable().optional(),
  month05Finance: z.number().nullable().optional(),
  month06Finance: z.number().nullable().optional(),
  month07Finance: z.number().nullable().optional(),
  month08Finance: z.number().nullable().optional(),
  month09Finance: z.number().nullable().optional(),
  month10Finance: z.number().nullable().optional(),
  month11Finance: z.number().nullable().optional(),
  month12Finance: z.number().nullable().optional(),

  disbursementRate01: z.number().nullable().optional(),
  disbursementRate02: z.number().nullable().optional(),
  disbursementRate03: z.number().nullable().optional(),

  disbursementRate01Finance: z.number().nullable().optional(),
  disbursementRate02Finance: z.number().nullable().optional(),
  disbursementRate03Finance: z.number().nullable().optional(),
});

export const disbursementProgressByFundingSourceDetailSchema = z.object({
  id: z.number(),
  ordinalNumber: z.number().nullable().optional(),
  projectId: z
    .number({
      required_error: requireDisbursementProgressByFundingSource(
        'disbursementProgressByFundingSourceDetailFields.projectId',
        'select'
      ),
      invalid_type_error: requireDisbursementProgressByFundingSource(
        'disbursementProgressByFundingSourceDetailFields.projectId',
        'select'
      ),
    })
    .min(
      1,
      requireDisbursementProgressByFundingSource(
        'disbursementProgressByFundingSourceDetailFields.projectId',
        'select'
      )
    ),
  projectName: z.string().nullable().optional(),
  disbursementProgressByFundingSourceId: z.number().nullable().optional(),
  totalInvestment: z.number().nullable().optional(),
  totalFundingRequirement: z.number().nullable().optional(),
  totalFundingRequirementCompensation: z.number().nullable().optional(),
  totalFundingRequirementConstructionConsulting: z.number().nullable().optional(),
  cumulativeDisbursementUntil: z.number().nullable().optional(),
  cumulativeDisbursementUntilCompensation: z.number().nullable().optional(),
  cumulativeDisbursementUntilConstructionConsulting: z.number().nullable().optional(),
  mediumTermCapitalPlan: z.number().nullable().optional(),
  allocatedCapitalPlan: z.number().nullable().optional(),
  annualImplementationPlanFinance: z.number().nullable().optional(),
  cumulativeDisbursementYearToDate: z.number().nullable().optional(),
  compensationDisbursementYearToDate: z.number().nullable().optional(),
  constructionConsultingDisbursementYearToDate: z.number().nullable().optional(),
  disbursementRateYearToDate: z.number().nullable().optional(),

  ...monthFields.shape,

  plannedCumulativeFullYear: z.number().nullable().optional(),
  plannedCumulativeFullYearFinance: z.number().nullable().optional(),
  disbursementRateFullYear: z.number().nullable().optional(),
  disbursementRateFullYearFinance: z.number().nullable().optional(),
  constructionPeriod: z.string().nullable().optional(),

  ...weekFields,

  issuesAndRecommendations: z.string().nullable().optional(),
  deploymentPhaseId: z.number().nullable().optional(),
  implementationStepsType: z.number().nullable().optional(),
  completionAcceptanceDate: z.coerce.date().nullable().optional(),
  locationMap: z.string().nullable().optional(),
  planningInformation: z.string().nullable().optional(),
  projectManagementDirectorId: z.number().nullable().optional(),
  accountantId: z.number().nullable().optional(),
  departmentInChargeId: z.number().nullable().optional(),
});

export const disbursementProgressByFundingSourceSchema = z.object({
  storeId: z.number(),
  branchId: z.number().nullable(),
  id: z.number(),
  fiscalYear: z.date().nullable(),
  budgetFundId: z.number().nullable(),
  note: z.string().nullable(),

  disbursementProgressByFundingSourceDetails: z.array(
    disbursementProgressByFundingSourceDetailSchema
  ),
});

export type DisbursementProgressByFundingSource = z.infer<
  typeof disbursementProgressByFundingSourceSchema
>;
export type DisbursementProgressByFundingSourceDetail = z.infer<
  typeof disbursementProgressByFundingSourceDetailSchema
>;

// Tạo giá trị mặc định cho các trường week camelCase
const defaultWeekFields: Record<string, string> = {};
for (let i = 1; i <= 52; i++) {
  const weekNum = String(i).padStart(2, '0');
  defaultWeekFields[`week${weekNum}`] = '';
  defaultWeekFields[`landWeek${weekNum}`] = '';
}

// Tạo giá trị mặc định riêng cho Detail
export const defaultValuesDisbursementProgressByFundingSourceDetail: DisbursementProgressByFundingSourceDetail =
  {
    // Giả sử id và projectId mặc định là 0 hoặc null tùy logic
    id: 0,
    projectId: 0,

    disbursementProgressByFundingSourceId: null,
    totalInvestment: null,
    totalFundingRequirement: null,
    totalFundingRequirementCompensation: null,
    totalFundingRequirementConstructionConsulting: null,
    cumulativeDisbursementUntil: null,
    cumulativeDisbursementUntilCompensation: null,
    cumulativeDisbursementUntilConstructionConsulting: null,
    mediumTermCapitalPlan: null,
    allocatedCapitalPlan: null,
    annualImplementationPlanFinance: null,
    cumulativeDisbursementYearToDate: null,
    compensationDisbursementYearToDate: null,
    constructionConsultingDisbursementYearToDate: null,
    disbursementRateYearToDate: null,

    plannedCumulativeFullYear: null,
    plannedCumulativeFullYearFinance: null,
    disbursementRateFullYear: null,
    disbursementRateFullYearFinance: null,
    constructionPeriod: null,

    issuesAndRecommendations: null,
    deploymentPhaseId: null,
    implementationStepsType: null,
    completionAcceptanceDate: null,
    locationMap: null,
    planningInformation: null,
    projectManagementDirectorId: null,
    accountantId: null,
    departmentInChargeId: null,

    ...defaultWeekFields, // Thêm các trường week mặc định
  };

export const defaultValuesDisbursementProgressByFundingSource: DisbursementProgressByFundingSource =
  {
    branchId: null,
    storeId: 0,
    id: 0,
    fiscalYear: new Date(),
    budgetFundId: null,
    note: '',
    disbursementProgressByFundingSourceDetails: [
      defaultValuesDisbursementProgressByFundingSourceDetail,
    ],
  };
