import axiosInstance, { request } from '@/axios-instance';
import { PageLayout } from '@/components/page-layout';
import { TickerCard } from '@/components/ticker-card/TickerCard';
import { realNumberDecimalFormat } from '@/lib/number';
import { useQuery } from '@tanstack/react-query';
import { useState } from 'react';
import { FolderKanban, Wallet, Banknote } from 'lucide-react';
import {
  ProjectDeploymentStatusReport,
  SavingRateContractorSelectionPlanReport,
  Notification,
} from '@/types';
import { Row2Table } from './row-2-table';
import { Row3LeftTable } from './row-3-left-table';
import { Row3RightTable } from './row-3-right-table';

type DashBoardRow1 = {
  projectCount: number;
  totalPlannedDisbursementAmount: number;
  totalDisbursement: number;
};

export const Dashboard = () => {
  const [year] = useState<Date>(new Date());
  const { data: row1 } = useQuery({
    queryKey: ['/dashboard/get-row-1'],
    queryFn: () => {
      return request<DashBoardRow1>(
        axiosInstance.get(`/dashboard/get-row-1/${year.toISOString().slice(0, 10)}`)
      );
    },
  });

  const { data: row2 } = useQuery({
    queryKey: ['/dashboard/get-row-2'],
    queryFn: () => {
      return request<ProjectDeploymentStatusReport[]>(
        axiosInstance.get(`/dashboard/get-row-2/${year.toISOString().slice(0, 10)}`)
      );
    },
  });
  const row2Items = row2 || [];

  const { data: row3Left } = useQuery({
    queryKey: ['/dashboard/get-left-row-3'],
    queryFn: () => {
      return request<SavingRateContractorSelectionPlanReport[]>(
        axiosInstance.get(`/dashboard/get-left-row-3/${year.toISOString().slice(0, 10)}`)
      );
    },
  });
  const row3LeftItems = row3Left || [];

  const { data: row3Right } = useQuery({
    queryKey: ['/dashboard/get-right-row-3'],
    queryFn: () => {
      return request<Notification[]>(axiosInstance.get(`/dashboard/get-right-row-3`));
    },
  });
  const row3RightItems = row3Right || [];

  return (
    <PageLayout contentClassName="flex flex-col gap-x-8 gap-y-4 ">
      <div className="grid max-w-full grid-cols-1 gap-x-8 gap-y-4 lg:max-w-screen-2xl lg:grid-cols-24">
        <div className="col-span-1 flex flex-col gap-x-8 gap-y-4 lg:col-span-8">
          <TickerCard
            title={`SL dự án theo nguồn ngân sách năm ${year.getFullYear()}`}
            icon={<FolderKanban className="h-10 w-10" />}
            tone="blue"
            value={row1?.projectCount ?? 0}
            formatValue={value => realNumberDecimalFormat(value.toString())}
          />
        </div>
        <div className="col-span-1 flex flex-col gap-x-8 gap-y-4 lg:col-span-8">
          <TickerCard
            title="Tổng tiền kế hoạch giải ngân của năm"
            icon={<Wallet className="h-10 w-10" />}
            tone="pink"
            value={row1?.totalPlannedDisbursementAmount ?? 0}
            formatValue={value => realNumberDecimalFormat(value.toString())}
          />
        </div>
        <div className="col-span-1 flex flex-col gap-x-8 gap-y-4 lg:col-span-8">
          <TickerCard
            title="Tổng tiền đã giải ngân của năm"
            icon={<Banknote className="h-10 w-10" />}
            tone="yellow"
            value={row1?.totalDisbursement ?? 0}
            formatValue={value => realNumberDecimalFormat(value.toString())}
          />
        </div>
      </div>
      <div className="grid max-w-full grid-cols-1 gap-x-8 gap-y-4 lg:max-w-screen-2xl lg:grid-cols-24">
        <div className="col-span-1 flex flex-col gap-x-8 gap-y-4 lg:col-span-24">
          <Row2Table items={row2Items} />
        </div>
      </div>
      <div className="grid max-w-full grid-cols-1 gap-x-8 gap-y-4 lg:max-w-screen-2xl xl:grid-cols-24">
        <div className="col-span-1 flex flex-col gap-x-8 gap-y-4 xl:col-span-12">
          <Row3LeftTable items={row3LeftItems} />
        </div>
        <div className="col-span-1 flex flex-col gap-x-8 gap-y-4 xl:col-span-12">
          <Row3RightTable items={row3RightItems} />
        </div>
      </div>
    </PageLayout>
  );
};
