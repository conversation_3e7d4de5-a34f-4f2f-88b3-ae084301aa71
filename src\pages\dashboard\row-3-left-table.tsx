import { SavingRateContractorSelectionPlanReport } from '@/types';
import Chart, {
  ArgumentAxis,
  ValueAxis,
  CommonSeriesSettings,
  Series,
  Legend,
  Title,
} from 'devextreme-react/chart';
import { RowLabel } from '.';
import { formatterMillion } from './methods';

export const Row3LeftTable = ({ items }: { items: SavingRateContractorSelectionPlanReport[] }) => {
  return (
    <Chart dataSource={items} className="bg-white">
      <CommonSeriesSettings argumentField="biddingSectorName" type="bar" />

      <Series
        valueField="singlePackageValueVnd"
        name="Hình thức Chỉ định thầu - Giá gói thầu"
        type="bar"
      >
        <RowLabel formatMillion />
      </Series>
      <Series
        valueField="singleContractValueVnd"
        name="Hình thức Chỉ định thầu - Gi<PERSON> hợp đồng"
        type="bar"
      >
        <RowLabel formatMillion />
      </Series>
      <Series
        valueField="biddingPackageValueVnd"
        name="<PERSON><PERSON><PERSON> thức đấu thầu rộng rãi, qua mạng - <PERSON>i<PERSON> gói thầu"
        type="bar"
      >
        <RowLabel formatMillion />
      </Series>
      <Series
        valueField="biddingContractValueVnd"
        name="Hình thức đấu thầu rộng rãi, qua mạng - Giá hợp đồng"
        type="bar"
      >
        <RowLabel formatMillion />
      </Series>

      <ArgumentAxis
        title={{ text: 'Lĩnh vực đấu thầu', font: { weight: 'bold' } }}
        label={{ rotationAngle: 90, overlappingBehavior: 'none', font: { weight: 'bold' } }}
      />
      <ValueAxis
        title={{ text: 'Số tiền (triệu VNĐ)', font: { weight: 'bold' } }}
        valueType="numeric"
        // logarithmBase={10}
        type="continuous"
        label={{
          customizeText: info => formatterMillion(info),
          font: { weight: 'bold' },
        }}
      />
      <Legend verticalAlignment="bottom" horizontalAlignment="center" font={{ weight: 800 }} />
      <Title text="Công tác LCNT" />
    </Chart>
  );
};
