import { customizeNumberCell } from '@/components/devex-data-grid';

export const formatter = ({ value }: { value: string | number | Date; valueText: string }) => {
  return customizeNumberCell()({ value: `${(value as number).toFixed(2)}` });
};
export const formatterMillion = ({
  value,
}: {
  value: string | number | Date;
  valueText: string;
}) => {
  const valueInMillions = (value as number) / 1_000_000;
  return customizeNumberCell()({ value: `${valueInMillions.toFixed(2)}` });
};
