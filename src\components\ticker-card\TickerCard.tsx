import { clsx } from 'clsx';
import { ReactNode } from 'react';

const colorMap = {
  green: 'text-green-600 bg-green-100 border-b-green-500',
  red: 'text-red-600 bg-red-100 border-b-red-500',
  yellow: 'text-yellow-600 bg-yellow-100 border-b-yellow-500',
  blue: 'text-blue-600 bg-blue-100 border-b-blue-500',
  pink: 'text-pink-600 bg-pink-100 border-b-pink-500',
};
export const TickerCard = ({
  title,
  icon,
  tone,
  value,
  percentage,
  formatValue = v => `${v}`,
}: {
  title: string;
  icon: string | ReactNode;
  tone?: keyof typeof colorMap;
  value: number;
  percentage?: number;
  formatValue?: (value: number) => string;
}) => {
  const isPositive = percentage ?? 0 > 0;

  const status = tone || (isPositive ? 'blue' : 'red');
  const colorClass = colorMap[status];
  const renderIcon = () => {
    if (typeof icon === 'string') {
      return <i className={`dx-icon dx-icon-${icon}`} />;
    }
    return icon; // React component
  };

  return (
    <div
      className={clsx('flex items-center gap-3 rounded-lg border-b-4 p-4 shadow-sm', colorClass)}
    >
      <div
        className={clsx(
          'flex h-12 w-12 items-center justify-center rounded-full text-xl',
          colorClass
        )}
      >
        {renderIcon()}
      </div>

      <div className="flex-1">
        <div className="mb-1 h-8 text-xs font-bold text-gray-700 xl:h-8">{title.toUpperCase()}</div>
        <div className="text-2xl font-semibold text-gray-900">{formatValue(value)}</div>
      </div>

      <div
        className={clsx(
          'flex items-center rounded-full px-2 py-1 text-xs font-semibold',
          colorClass
        )}
      >
        <i
          className={`dx-icon ${isPositive ? 'dx-icon-spinup' : 'dx-icon-spindown'} mr-1 text-base`}
        />
        {percentage && <span>{`${Math.abs(percentage)}%`}</span>}
      </div>
    </div>
  );
};
