import { DevexDataGrid } from '@/components/devex-data-grid';
import { Column } from 'devextreme-react/data-grid';
import { Notification } from '@/types';
import { PageLayout } from '@/components/page-layout';

export const Row3RightTable = ({ items }: { items: Notification[] }) => {
  return (
    <PageLayout header="Thông báo" contentClassName="bg-white">
      <DevexDataGrid
        id={'DASHBOARD/NOTIFICATION'}
        dataSource={items}
        editing={{ allowAdding: false, allowUpdating: false, allowDeleting: false, useIcons: true }}
        hideSerialNumber
        pager={{ visible: false }}
        paging={{ enabled: false }}
        wordWrapEnabled
        filterRow={{ visible: false }}
        toolbar={{ visible: false }}
      >
        <Column dataField="title" caption="Tiêu đề" width={200} />
        <Column dataField="content" caption="Nội dung" />
      </DevexDataGrid>
    </PageLayout>
  );
};
