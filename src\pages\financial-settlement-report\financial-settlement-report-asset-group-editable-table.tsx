import { ErrorMessage } from '@/components/ui/error-message';
import { TABLES } from '@/constant';
import {
  defaultValuesFinancialSettlementReport,
  FinancialSettlementReport,
  FinancialSettlementReportAssetGroup,
  IUserPermission,
} from '@/types';
import { useFormContext, useWatch } from 'react-hook-form';

import { ColumnDef } from '@tanstack/react-table';

import { DataTable, EditableInputCell } from '@/components/data-table';
import { translationWithNamespace } from '@/lib/i18nUtils';
import { getRandomNumber } from '@/lib/number';
import { useMemo } from 'react';

const [defaultRow] = defaultValuesFinancialSettlementReport.financialSettlementReportAssetGroups;

type FinancialSettlementReportAssetGroupEditableTableProps = {
  role?: IUserPermission;
  calculateForm?: () => void;
};

const t = translationWithNamespace('financialSettlementReport');

export const FinancialSettlementReportAssetGroupEditableTable = ({
  role,
  calculateForm,
}: FinancialSettlementReportAssetGroupEditableTableProps) => {
  const {
    setValue,
    control,
    formState: { errors },
  } = useFormContext<FinancialSettlementReport>();

  const [editableData] = useWatch({
    control,
    name: ['financialSettlementReportAssetGroups'],
  });

  const financialSettlementReportAssetGroupEditableColumns: ColumnDef<FinancialSettlementReportAssetGroup>[] =
    useMemo(
      () => [
        {
          id: 'groupName', //
          accessorKey: 'groupName',
          header: t('fields.financialSettlementReportAssetGroups.groupName'),
          cell: props => <EditableInputCell {...props} />,
        },

        {
          id: 'totalAmount', //
          accessorKey: 'totalAmount',
          header: t('fields.financialSettlementReportAssetGroups.totalAmount'),
          cell: props => <EditableInputCell isMoney {...props} type="number" />,
        },
      ],
      []
    );

  return (
    <div>
      <DataTable
        tableId={TABLES.FINANCIAL_SETTLEMENT_REPORT_ASSET_GROUP}
        sortColumn="id"
        role={role}
        editableData={editableData}
        setEditableData={editedData => {
          setValue('financialSettlementReportAssetGroups', editedData);
          calculateForm?.();
        }}
        onAddButtonClick={table => {
          const newRow = { ...defaultRow, id: -getRandomNumber() };
          table.options.meta?.addNewRow(newRow);
        }}
        syncQueryParams={false}
        columns={financialSettlementReportAssetGroupEditableColumns}
        customToolbar={() => {
          return (
            <>
              {errors.financialSettlementReportAssetGroups?.message && (
                <ErrorMessage message={errors.financialSettlementReportAssetGroups?.message} />
              )}
            </>
          );
        }}
      />
    </div>
  );
};
