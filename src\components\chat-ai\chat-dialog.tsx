import { useState, useEffect } from 'react';
import { Button } from 'devextreme-react/button';
import {
  HumanMessage,
  SystemMessage,
  BaseMessage,
  AIMessage,
  mapChatMessagesToStoredMessages,
} from '@langchain/core/messages';
import { LocalAuthUser } from '@/types';
import { LOCAL_STORE_KEYS } from '@/constant';
import { getLocalStorage } from '@/lib/localStorage';

interface ChatDialogProps {
  onClose: () => void;
}

const STORAGE_KEY = 'chat-ai-messages';
const MAX_MESSAGES = 50;

// Helper functions for localStorage

export default function ChatDialog({ onClose }: ChatDialogProps) {
  const user = getLocalStorage(LOCAL_STORE_KEYS.USER) as LocalAuthUser;

  const getDefaultMessages = (): BaseMessage[] => [
    new SystemMessage(
      'Bạn là một trợ lý AI thông minh với CHỨC NĂNG CHÍNH:\n\n' +
        '**CHỨC NĂNG: Chuyên gia postgresql & Database**\n' +
        '- Luôn đặt tên trường và tên bảng trong dấu ngoặc kép (" ")\n' +
        '- Đảm bảo cú pháp SQL đúng chuẩn và dễ đọc\n' +
        '- **Format dữ liệu đẹp**: Khi trả về dữ liệu luôn format về dạng Markdown.\n\n' +
        '**QUY TẮC HOẠT ĐỘNG**:\n' +
        '- Bạn luôn phải trả lời bằng tiếng Việt.' +
        `Đây là thông tin user đang đăng nhập userId=${user?.userId} và userName=${user?.userName} và userRole=${user?.role}`
    ),
  ];
  const saveMessagesToStorage = (messages: BaseMessage[]) => {
    try {
      // Chỉ lưu 50 tin nhắn gần nhất (bao gồm cả SystemMessage)
      const limitedMessages = messages.slice(-MAX_MESSAGES);
      const serializedMessages = limitedMessages.map(msg => ({
        type: msg.getType(),
        content: msg.content,
      }));
      localStorage.setItem(STORAGE_KEY, JSON.stringify(serializedMessages));
    } catch (error) {
      console.error('Error saving messages to localStorage:', error);
    }
  };

  const loadMessagesFromStorage = (): BaseMessage[] => {
    try {
      const stored = localStorage.getItem(STORAGE_KEY);
      if (!stored) {
        return getDefaultMessages();
      }

      const serializedMessages = JSON.parse(stored);
      const messages = (
        serializedMessages as Array<{
          type: string;
          content: string | undefined;
          [key: string]: unknown;
        }>
      ).map(
        (msg: {
          type: string;
          content: string | undefined;
          [key: string]: unknown;
        }): BaseMessage => {
          const content = msg.content || '';
          switch (msg.type) {
            case 'system':
              return new SystemMessage(content);
            case 'human':
              return new HumanMessage(content);
            case 'ai':
              return new AIMessage(content);
            default:
              return new SystemMessage(content);
          }
        }
      );

      // Đảm bảo luôn có SystemMessage đầu tiên
      if (messages.length === 0 || !(messages[0] instanceof SystemMessage)) {
        return getDefaultMessages();
      }

      return messages;
    } catch (error) {
      console.error('Error loading messages from localStorage:', error);
      return getDefaultMessages();
    }
  };
  const [inputMessage, setInputMessage] = useState('');
  const [messages, setMessages] = useState<BaseMessage[]>(loadMessagesFromStorage());
  const [isLoading, setIsLoading] = useState(false);

  // Save messages to localStorage whenever messages change
  useEffect(() => {
    saveMessagesToStorage(messages);
  }, [messages]);

  const clearChatHistory = () => {
    const defaultMessages = getDefaultMessages();
    setMessages(defaultMessages);
    localStorage.removeItem(STORAGE_KEY);
  };

  async function sendMessage() {
    setIsLoading(true); // set to true
    const messageHistory = [...messages, new HumanMessage(inputMessage)];
    console.log(
      'mapChatMessagesToStoredMessages(messageHistory)',
      mapChatMessagesToStoredMessages(messageHistory)
    );
    try {
      //Gọi API NestJS thay vì server action Next.js
      const response = await fetch('https://api-qlda-hocmon-dev.phanmemviet.net.vn/ai-agent/message', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          messages: mapChatMessagesToStoredMessages(messageHistory),
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      if (data.response) {
        messageHistory.push(new AIMessage(data.response as string));
      }
    } catch (error) {
      console.error('Error calling NestJS API:', error);
      messageHistory.push(new AIMessage('Xin lỗi, đã có lỗi xảy ra khi xử lý yêu cầu của bạn.'));
    }

    setMessages(messageHistory);
    setInputMessage('');
    setIsLoading(false); // set to false
  }

  return (
    <div className="flex h-full flex-col">
      <header className="flex-shrink-0 border-b bg-white p-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <span className="ml-2 font-semibold">Trợ lý AI</span>
            <span className="ml-2 text-xs text-gray-500">({messages.length - 1} tin nhắn)</span>
          </div>
          <div className="flex items-center space-x-2">
            <Button
              icon="trash"
              stylingMode="text"
              onClick={clearChatHistory}
              className="text-gray-500 hover:text-red-600"
              hint="Xóa lịch sử chat"
            />
            <Button
              icon="close"
              stylingMode="text"
              onClick={onClose}
              className="text-gray-500 hover:text-gray-700"
            />
          </div>
        </div>
      </header>
      <div className="flex-1 overflow-y-auto p-1 pb-32">
        {messages.length > 0 &&
          messages.map((message, index) => {
            if (message instanceof HumanMessage) {
              return (
                <div
                  key={message.getType() + index}
                  className="col-start-1 col-end-8 rounded-lg p-3"
                >
                  <div className="flex flex-row items-center">
                    <div className="flex h-8 w-8 flex-shrink-0 items-center justify-center rounded-full bg-orange-400 text-sm text-white">
                      Me
                    </div>
                    <div className="relative ml-3 rounded-xl bg-white px-4 py-2 text-sm shadow">
                      <div>{message.content as string}</div>
                    </div>
                  </div>
                </div>
              );
            }

            if (message instanceof AIMessage) {
              return (
                <div
                  key={message.getType() + index}
                  className="col-start-6 col-end-13 rounded-lg p-3"
                >
                  <div className="flex flex-row-reverse items-center justify-start">
                    <div className="flex h-8 w-8 flex-shrink-0 items-center justify-center rounded-full bg-green-400 text-sm">
                      AI
                    </div>
                    <div
                      className="relative mr-3 rounded-xl bg-info-100 text-sm shadow"
                      style={{ minWidth: '300px', maxWidth: '100%' }}
                    >
                      <div className="whitespace-pre-wrap">
                        {message.content &&
                        (message.content as string).includes('|') &&
                        (message.content as string).includes('---') ? (
                          <div>
                            {/* Hiển thị text trước bảng */}
                            <div className="mb-3 px-4 py-2">
                              {(message.content as string)
                                .split('\n')
                                .filter(
                                  line =>
                                    !line.includes('|') && !line.includes('---') && line.trim()
                                )
                                .join('\n')}
                            </div>
                            {/* Hiển thị bảng */}
                            <div className="w-full overflow-x-auto" style={{ maxWidth: '100%' }}>
                              <table
                                className="mt-2 border-collapse border border-gray-300 bg-white"
                                style={{ width: 'max-content' }}
                              >
                                <thead>
                                  <tr className="bg-gray-50">
                                    {(() => {
                                      const lines = (message.content as string).split('\n');
                                      const headerLine = lines.find((line, index) => {
                                        const nextLine = lines[index + 1];
                                        return (
                                          line.includes('|') &&
                                          !line.includes('---') &&
                                          nextLine &&
                                          nextLine.includes('---')
                                        );
                                      });
                                      if (!headerLine) return null;
                                      const headerCells = headerLine.split('|');
                                      // Loại bỏ cell đầu và cuối nếu rỗng (do split với |)
                                      const cleanHeaderCells = headerCells.slice(
                                        headerCells[0].trim() === '' ? 1 : 0,
                                        headerCells[headerCells.length - 1].trim() === ''
                                          ? -1
                                          : headerCells.length
                                      );
                                      return cleanHeaderCells.map((header, idx) => (
                                        <th
                                          key={idx}
                                          className="border border-gray-300 px-2 py-1 text-left font-semibold"
                                        >
                                          {header.trim()}
                                        </th>
                                      ));
                                    })()}
                                  </tr>
                                </thead>
                                <tbody>
                                  {(() => {
                                    const lines = (message.content as string).split('\n');
                                    const headerIndex = lines.findIndex((line, index) => {
                                      const nextLine = lines[index + 1];
                                      return (
                                        line.includes('|') &&
                                        !line.includes('---') &&
                                        nextLine &&
                                        nextLine.includes('---')
                                      );
                                    });

                                    return lines
                                      .filter((line, index) => {
                                        return (
                                          line.includes('|') &&
                                          !line.includes('---') &&
                                          index > headerIndex && // Loại trừ header
                                          line.trim() !== ''
                                        );
                                      })
                                      .map((row, rowIdx) => (
                                        <tr key={rowIdx} className="hover:bg-info-100">
                                          {(() => {
                                            const cells = row.split('|');
                                            // Loại bỏ cell đầu và cuối nếu rỗng (do split với |)
                                            const cleanCells = cells.slice(
                                              cells[0].trim() === '' ? 1 : 0,
                                              cells[cells.length - 1].trim() === ''
                                                ? -1
                                                : cells.length
                                            );
                                            return cleanCells.map((cell, cellIdx) => (
                                              <td
                                                key={cellIdx}
                                                className="border border-gray-300 px-2 py-1"
                                              >
                                                {cell.trim()}
                                              </td>
                                            ));
                                          })()}
                                        </tr>
                                      ));
                                  })()}
                                </tbody>
                              </table>
                            </div>
                          </div>
                        ) : (
                          <div className="px-4 py-2">{message.content as string}</div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              );
            }
          })}
      </div>
      <div className="absolute bottom-0 left-0 right-0 rounded-tl-lg rounded-tr-lg bg-gray-50 py-2  ">
        <div className="flex  w-full flex-row items-center rounded-tl-lg  rounded-tr-lg bg-gray-50 px-2">
          <div className="ml-1 flex-grow">
            <div className="relative w-full">
              <textarea
                disabled={isLoading}
                value={inputMessage}
                onChange={e => setInputMessage(e.target.value)}
                className="flex max-h-16 min-h-10 w-full resize-none rounded-md border p-2 focus:border-gray-300 focus:outline-none"
                rows={1}
                onInput={e => {
                  const textarea = e.target as HTMLTextAreaElement;
                  textarea.style.height = 'auto';
                  textarea.style.height = `${textarea.scrollHeight}px`;
                }}
              />
            </div>
          </div>
          <div className="ml-4">
            <button
              onClick={() => {
                if (inputMessage) sendMessage().catch(console.error);
              }}
              className="flex flex-shrink-0 items-center justify-center rounded-xl bg-info-550 px-4 py-2 text-white hover:bg-info-400"
            >
              <span>{isLoading ? 'Chờ...' : 'Gửi'}</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
