import { requiredTextWithNamespace } from '@/lib/i18nUtils';
import { z } from 'zod';
const requireText = requiredTextWithNamespace('boardOfDirector');
// phong chuyen mon
// storeId
// branchId
// id
// code
// name
// note
// is_active

export const boardOfDirectorSchema = z.object({
  id: z.number(),
  storeId: z.number().nullable(),
  branchId: z.number().nullable(),
  code: z
    .string({
      required_error: requireText('code'),
      invalid_type_error: requireText('code'),
    })
    .min(1, { message: requireText('code') }),
  name: z
    .string({
      required_error: requireText('name'),
      invalid_type_error: requireText('name'),
    })
    .min(1, { message: requireText('name') }),
  departmentHeadId: z
    .number({
      required_error: requireText('departmentHeadId'),
      invalid_type_error: requireText('departmentHeadId'),
    })
    .min(1, { message: requireText('departmentHeadId') }),
  departmentHeadName: z.string().nullable().optional(),
  note: z.string().optional().nullable(),
  isActive: z.boolean().default(true),

  departmentDeputyId: z.number().nullable().optional(),
  departmentDeputyTwoId: z.number().nullable().optional(),
  departmentDeputyThreeId: z.number().nullable().optional(),
  departmentDeputyName: z.string().nullable().optional(),
  departmentDeputyTwoName: z.string().nullable().optional(),
  departmentDeputyThreeName: z.string().nullable().optional(),
  consultingContractOfficerId: z.number().nullable().optional(),
  consultingContractOfficerName: z.string().nullable().optional(),
  departmentType: z.number().nullable().optional(),
});

export type BoardOfDirector = z.infer<typeof boardOfDirectorSchema>;
