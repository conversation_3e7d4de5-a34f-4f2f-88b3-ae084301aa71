import { ErrorMessage } from '@/components/ui/error-message';
import { IMPLEMENTATION_STEPS_TYPES, MUTATE, QUERIES, TABLES } from '@/constant';
import {
  DisbursementProgressByFundingSource,
  IUserPermission,
  DisbursementProgressByFundingSourceDetail,
  defaultValuesDisbursementProgressByFundingSourceDetail,
} from '@/types';
import { useFormContext, useWatch } from 'react-hook-form';
import { useCallback, useMemo } from 'react';

import { CellContext, ColumnDef } from '@tanstack/react-table';

import {
  DataTable,
  DataTableRowActions,
  EditableDatePickerCell,
  EditableDropdownCell,
  EditableInputCell,
  EditableTextArea,
} from '@/components/data-table';
import { translationWithNamespace } from '@/lib/i18nUtils';
import { getRandomNumber } from '@/lib/number';
import dayjs from 'dayjs';
import weekOfYear from 'dayjs/plugin/weekOfYear';
import localizedFormat from 'dayjs/plugin/localizedFormat';
import 'dayjs/locale/vi';
import { Button } from 'devextreme-react';
import { useMutation } from '@tanstack/react-query';
import axiosInstance, { request } from '@/axios-instance';
import { useParams } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { toRoman } from '@/lib/numberToText';

dayjs.extend(weekOfYear);
dayjs.extend(localizedFormat);
dayjs.locale('vi');

const defaultRow = defaultValuesDisbursementProgressByFundingSourceDetail;

type DisbursementProgressByFundingSourceEditableTableProps = {
  role?: IUserPermission;
  calculateForm?: () => void;
};

const tcommon = translationWithNamespace('common');

export const DisbursementProgressByFundingSourceEditableTable = ({
  role,
  calculateForm,
}: DisbursementProgressByFundingSourceEditableTableProps) => {
  const { id: editId } = useParams();
  const {
    setValue,
    control,
    formState: { errors },
    watch,
    getValues,
  } = useFormContext<DisbursementProgressByFundingSource>();

  const fiscalYearValue = watch('fiscalYear');
  const { t: translator } = useTranslation('disbursementProgressByFundingSource');
  const t = useCallback(
    (fieldName: string, options?: Record<string, string | number>) => {
      const thisYear = fiscalYearValue?.getFullYear() || new Date().getFullYear();
      const year = thisYear;
      const nextYear = thisYear + 1;
      const previousYear = thisYear - 1;

      return translator(fieldName, {
        year,
        previousYear,
        nextYear,
        ...options,
        interpolation: { escapeValue: false },
      });
    },
    [fiscalYearValue, translator]
  );
  const [editableData] = useWatch({
    control,
    name: ['disbursementProgressByFundingSourceDetails'],
  });

  const createWeekColumns = useMemo(
    () =>
      (weekIndex: number, year: number): ColumnDef<DisbursementProgressByFundingSourceDetail>[] => {
        const weekNum = String(weekIndex).padStart(2, '0');
        const weekKey = `week${weekNum}`;
        const landKey = `landWeek${weekNum}`;

        const startDate = dayjs().year(year).week(weekIndex).day(1); // day(1) là thứ Hai
        const endDate = dayjs().year(year).week(weekIndex).day(6); // day(6) là thứ Bảy
        const displayStartDate =
          startDate.year() === year ? startDate : dayjs().year(year).startOf('year');
        const displayEndDate = endDate.year() === year ? endDate : dayjs().year(year).endOf('year');

        const fromDate = displayStartDate.format('DD/MM');
        const toDate = displayEndDate.format('DD/MM');
        const dateRange = `(${fromDate}-${toDate})`;

        return [
          {
            id: weekKey,
            accessorKey: weekKey,
            header: t('fields.disbursementProgressByFundingSourceDetails.weekHeader', {
              weekNum,
              dateRange,
              fromDate,
              toDate,
            }),
            cell: (props: CellContext<DisbursementProgressByFundingSourceDetail, unknown>) => (
              <EditableTextArea {...props} type="number" disabled />
            ),
            size: 400,
          },
          {
            id: landKey,
            accessorKey: landKey,
            header: t('fields.disbursementProgressByFundingSourceDetails.landWeekHeader', {
              weekNum,
              dateRange,
              fromDate,
              toDate,
            }),
            cell: (props: CellContext<DisbursementProgressByFundingSourceDetail, unknown>) => (
              <EditableTextArea {...props} disabled />
            ),
            size: 400,
          },
        ];
      },
    [t]
  );
  const { allWeekColumns, initialColumnVisibility } = useMemo(() => {
    const calculationYear =
      fiscalYearValue instanceof Date ? dayjs(fiscalYearValue).year() : dayjs().year();

    const allWeekColumns = Array.from({ length: 52 }, (_, i) =>
      createWeekColumns(i + 1, calculationYear)
    ).flat();

    const currentWeek = dayjs().week();
    let startWeek: number;
    let endWeek: number;

    if (currentWeek === 1) {
      startWeek = 1;
      endWeek = 3;
    } else if (currentWeek === 52) {
      startWeek = 50;
      endWeek = 52;
    } else {
      startWeek = currentWeek - 1;
      endWeek = currentWeek + 1;
    }

    const getWeekNumberFromId = (id: string | undefined): number | null => {
      if (!id) return null;
      const match = id.match(/(?:week|landWeek)(\d{2})$/);
      return match ? parseInt(match[1], 10) : null;
    };

    const visibilityState: Record<string, boolean> = {};
    allWeekColumns.forEach(col => {
      if (col.id) {
        const weekNum = getWeekNumberFromId(col.id);
        const isVisibleByDefault = weekNum !== null && weekNum >= startWeek && weekNum <= endWeek;
        visibilityState[col.id] = isVisibleByDefault;
      }
    });
    visibilityState['removeRow'] = true;

    return {
      allWeekColumns: [...allWeekColumns],
      initialColumnVisibility: visibilityState,
    };
  }, [fiscalYearValue, createWeekColumns]);

  const allMonthColumns = useMemo(() => {
    const columns: ColumnDef<DisbursementProgressByFundingSourceDetail>[] = [];
    for (let month = 1; month <= 12; month++) {
      const strMonth = String(month).padStart(2, '0');
      const keyMonth = `month${strMonth}`;
      const keyMonthFinance = `month${strMonth}Finance`;

      columns.push({
        id: keyMonth,
        accessorKey: keyMonth,
        header: t('fields.disbursementProgressByFundingSourceDetails.month', {
          month: month,
        }),
        cell: (props: CellContext<DisbursementProgressByFundingSourceDetail, unknown>) => (
          <EditableInputCell {...props} type="number" />
        ),
        size: 250,
      });
      columns.push({
        id: keyMonthFinance,
        accessorKey: keyMonthFinance,
        header: t('fields.disbursementProgressByFundingSourceDetails.monthFinance', {
          month: month,
        }),
        cell: (props: CellContext<DisbursementProgressByFundingSourceDetail, unknown>) => (
          <EditableInputCell {...props} type="number" />
        ),
        size: 250,
      });

      if (month % 3 == 0 && month < 12) {
        const quater = month / 3;
        const quaterFormat = String(quater).padStart(2, '0');
        const strQuater = toRoman(quater);
        const keyQuater = `disbursementRate${quaterFormat}`;
        const keyQuaterFinance = `disbursementRate${quaterFormat}Finance`;

        columns.push({
          id: keyQuater,
          accessorKey: keyQuater,
          header: t('fields.disbursementProgressByFundingSourceDetails.quater', {
            quater: strQuater,
          }),
          cell: (props: CellContext<DisbursementProgressByFundingSourceDetail, unknown>) => (
            <EditableInputCell {...props} type="number" />
          ),
          size: 250,
        });

        columns.push({
          id: keyQuaterFinance,
          accessorKey: keyQuaterFinance,
          header: t('fields.disbursementProgressByFundingSourceDetails.quaterFinance', {
            quater: strQuater,
          }),
          cell: (props: CellContext<DisbursementProgressByFundingSourceDetail, unknown>) => (
            <EditableInputCell {...props} type="number" />
          ),
          size: 250,
        });
      }
    }
    return columns;
  }, [t]);

  const tableColumns = [
    {
      id: 'projectId',
      accessorKey: 'projectId',
      header: t('fields.disbursementProgressByFundingSourceDetails.projectId'),
      cell: (props: CellContext<DisbursementProgressByFundingSourceDetail, unknown>) => (
        <EditableDropdownCell
          {...props}
          model="project"
          queryKey={[QUERIES.PROJECT]}
          defaultText={props.row.original.projectName}
          disabled
        />
      ),
      size: 250,
    },
    {
      id: 'totalInvestment',
      accessorKey: 'totalInvestment',
      header: t('fields.disbursementProgressByFundingSourceDetails.totalInvestment'),
      cell: (props: CellContext<DisbursementProgressByFundingSourceDetail, unknown>) => (
        <EditableInputCell {...props} type="number" disabled />
      ),
      size: 250,
    },
    {
      id: 'totalFundingRequirementSummary',
      header: t('fields.disbursementProgressByFundingSourceDetails.totalFundingRequirementSummary'),
      columns: [
        {
          id: 'totalFundingRequirement',
          accessorKey: 'totalFundingRequirement',
          header: t('fields.disbursementProgressByFundingSourceDetails.totalFundingRequirement'),
          cell: (props: CellContext<DisbursementProgressByFundingSourceDetail, unknown>) => (
            <EditableInputCell {...props} type="number" disabled />
          ),
          size: 250,
        },
        {
          id: 'totalFundingRequirementCompensation',
          accessorKey: 'totalFundingRequirementCompensation',
          header: t(
            'fields.disbursementProgressByFundingSourceDetails.totalFundingRequirementCompensation'
          ),
          cell: (props: CellContext<DisbursementProgressByFundingSourceDetail, unknown>) => (
            <EditableInputCell {...props} type="number" disabled />
          ),
          size: 250,
        },
        {
          id: 'totalFundingRequirementConstructionConsulting',
          accessorKey: 'totalFundingRequirementConstructionConsulting',
          header: t(
            'fields.disbursementProgressByFundingSourceDetails.totalFundingRequirementConstructionConsulting'
          ),
          cell: (props: CellContext<DisbursementProgressByFundingSourceDetail, unknown>) => (
            <EditableInputCell {...props} type="number" disabled />
          ),
          size: 250,
        },
      ],
    },
    {
      id: 'cumulativeDisbursementUntilSummary',
      header: t(
        'fields.disbursementProgressByFundingSourceDetails.cumulativeDisbursementUntilSummary'
      ),
      columns: [
        {
          id: 'cumulativeDisbursementUntil',
          accessorKey: 'cumulativeDisbursementUntil',
          header: t(
            'fields.disbursementProgressByFundingSourceDetails.cumulativeDisbursementUntil'
          ),
          cell: (props: CellContext<DisbursementProgressByFundingSourceDetail, unknown>) => (
            <EditableInputCell {...props} type="number" disabled />
          ),
          size: 250,
        },
        {
          id: 'cumulativeDisbursementUntilCompensation',
          accessorKey: 'cumulativeDisbursementUntilCompensation',
          header: t(
            'fields.disbursementProgressByFundingSourceDetails.cumulativeDisbursementUntilCompensation'
          ),
          cell: (props: CellContext<DisbursementProgressByFundingSourceDetail, unknown>) => (
            <EditableInputCell {...props} type="number" disabled />
          ),
          size: 250,
        },
        {
          id: 'cumulativeDisbursementUntilConstructionConsulting',
          accessorKey: 'cumulativeDisbursementUntilConstructionConsulting',
          header: t(
            'fields.disbursementProgressByFundingSourceDetails.cumulativeDisbursementUntilConstructionConsulting'
          ),
          cell: (props: CellContext<DisbursementProgressByFundingSourceDetail, unknown>) => (
            <EditableInputCell {...props} type="number" disabled />
          ),
          size: 250,
        },
      ],
    },

    {
      id: 'mediumTermCapitalPlan',
      accessorKey: 'mediumTermCapitalPlan',
      header: t('fields.disbursementProgressByFundingSourceDetails.mediumTermCapitalPlan'),
      cell: (props: CellContext<DisbursementProgressByFundingSourceDetail, unknown>) => (
        <EditableInputCell {...props} type="number" />
      ),
      size: 250,
    },
    {
      id: 'allocatedCapitalPlan',
      accessorKey: 'allocatedCapitalPlan',
      header: t('fields.disbursementProgressByFundingSourceDetails.allocatedCapitalPlan'),
      cell: (props: CellContext<DisbursementProgressByFundingSourceDetail, unknown>) => (
        <EditableInputCell {...props} type="number" disabled />
      ),
      size: 250,
    },
    {
      id: 'annualImplementationPlanFinance',
      accessorKey: 'annualImplementationPlanFinance',
      header: t(
        'fields.disbursementProgressByFundingSourceDetails.annualImplementationPlanFinance'
      ),
      cell: (props: CellContext<DisbursementProgressByFundingSourceDetail, unknown>) => (
        <EditableInputCell {...props} type="number" />
      ),
      size: 250,
    },
    {
      id: 'cumulativeDisbursementYearToDateSummary',
      header: t(
        'fields.disbursementProgressByFundingSourceDetails.cumulativeDisbursementYearToDateSummary'
      ),
      columns: [
        {
          id: 'cumulativeDisbursementYearToDate',
          accessorKey: 'cumulativeDisbursementYearToDate',
          header: t(
            'fields.disbursementProgressByFundingSourceDetails.cumulativeDisbursementYearToDate'
          ),
          cell: (props: CellContext<DisbursementProgressByFundingSourceDetail, unknown>) => (
            <EditableInputCell {...props} type="number" disabled />
          ),
          size: 250,
        },
        {
          id: 'compensationDisbursementYearToDate',
          accessorKey: 'compensationDisbursementYearToDate',
          header: t(
            'fields.disbursementProgressByFundingSourceDetails.compensationDisbursementYearToDate'
          ),
          cell: (props: CellContext<DisbursementProgressByFundingSourceDetail, unknown>) => (
            <EditableInputCell {...props} type="number" disabled />
          ),
          size: 250,
        },
        {
          id: 'constructionConsultingDisbursementYearToDate',
          accessorKey: 'constructionConsultingDisbursementYearToDate',
          header: t(
            'fields.disbursementProgressByFundingSourceDetails.constructionConsultingDisbursementYearToDate'
          ),
          cell: (props: CellContext<DisbursementProgressByFundingSourceDetail, unknown>) => (
            <EditableInputCell {...props} type="number" disabled />
          ),
          size: 250,
        },
      ],
    },
    {
      id: 'disbursementRateYearToDate',
      accessorKey: 'disbursementRateYearToDate',
      header: t('fields.disbursementProgressByFundingSourceDetails.disbursementRateYearToDate'),
      cell: (props: CellContext<DisbursementProgressByFundingSourceDetail, unknown>) => (
        <EditableInputCell {...props} type="number" disabled />
      ),
      size: 250,
    },
    //months
    ...allMonthColumns,
    {
      id: 'plannedCumulativeFullYear',
      accessorKey: 'plannedCumulativeFullYear',
      header: t('fields.disbursementProgressByFundingSourceDetails.plannedCumulativeFullYear'),
      cell: (props: CellContext<DisbursementProgressByFundingSourceDetail, unknown>) => (
        <EditableInputCell {...props} type="number" />
      ),
      size: 250,
    },
    {
      id: 'plannedCumulativeFullYearFinance',
      accessorKey: 'plannedCumulativeFullYearFinance',
      header: t(
        'fields.disbursementProgressByFundingSourceDetails.plannedCumulativeFullYearFinance'
      ),
      cell: (props: CellContext<DisbursementProgressByFundingSourceDetail, unknown>) => (
        <EditableInputCell {...props} type="number" />
      ),
      size: 250,
    },
    {
      id: 'disbursementRateFullYear',
      accessorKey: 'disbursementRateFullYear',
      header: t('fields.disbursementProgressByFundingSourceDetails.disbursementRateFullYear'),
      cell: (props: CellContext<DisbursementProgressByFundingSourceDetail, unknown>) => (
        <EditableInputCell {...props} type="number" />
      ),
      size: 250,
    },
    {
      id: 'disbursementRateFullYearFinance',
      accessorKey: 'disbursementRateFullYearFinance',
      header: t(
        'fields.disbursementProgressByFundingSourceDetails.disbursementRateFullYearFinance'
      ),
      cell: (props: CellContext<DisbursementProgressByFundingSourceDetail, unknown>) => (
        <EditableInputCell {...props} type="number" />
      ),
      size: 250,
    },
    {
      id: 'constructionPeriod',
      accessorKey: 'constructionPeriod',
      header: t('fields.disbursementProgressByFundingSourceDetails.constructionPeriod'),
      cell: (props: CellContext<DisbursementProgressByFundingSourceDetail, unknown>) => (
        <EditableInputCell {...props} type="text" />
      ),
      size: 250,
    },
    //weeks
    ...allWeekColumns,
    {
      id: 'issuesAndRecommendations',
      accessorKey: 'issuesAndRecommendations',
      header: () => {
        const headerText = t(
          'fields.disbursementProgressByFundingSourceDetails.issuesAndRecommendations'
        );
        const tooltipText = t('fields.tooltips.issuesAndRecommendationsTooltip');
        return <span title={tooltipText}>{headerText}</span>;
      },
      cell: (props: CellContext<DisbursementProgressByFundingSourceDetail, unknown>) => (
        <EditableTextArea {...props} />
      ),
      size: 400,
    },
    {
      id: 'deploymentPhaseId',
      accessorKey: 'deploymentPhaseId',
      header: t('fields.disbursementProgressByFundingSourceDetails.deploymentPhaseId'),
      cell: (props: CellContext<DisbursementProgressByFundingSourceDetail, unknown>) => (
        <EditableDropdownCell
          {...props}
          model="deployment-phase"
          queryKey={[QUERIES.DEPLOYMENT_PHASE]}
          disabled
        />
      ),
      size: 250,
    },
    {
      id: 'implementationStepsType',
      accessorKey: 'implementationStepsType',
      header: t('fields.disbursementProgressByFundingSourceDetails.implementationStepsType'),
      cell: (props: CellContext<DisbursementProgressByFundingSourceDetail, unknown>) => (
        <EditableDropdownCell
          {...props}
          options={IMPLEMENTATION_STEPS_TYPES}
          queryKey={['IMPLEMENTATION_STEPS_TYPES']}
          disabled
        />
      ),
      size: 250,
    },
    {
      id: 'completionAcceptanceDate',
      accessorKey: 'completionAcceptanceDate',
      header: t('fields.disbursementProgressByFundingSourceDetails.completionAcceptanceDate'),
      cell: (props: CellContext<DisbursementProgressByFundingSourceDetail, unknown>) => (
        <EditableDatePickerCell {...props} disabled />
      ),
      size: 200,
    },
    {
      id: 'locationMap',
      accessorKey: 'locationMap',
      header: t('fields.disbursementProgressByFundingSourceDetails.locationMap'),
      cell: (props: CellContext<DisbursementProgressByFundingSourceDetail, unknown>) => (
        <EditableInputCell {...props} disabled />
      ),
      size: 250,
    },
    {
      id: 'planningInformation',
      accessorKey: 'planningInformation',
      header: t('fields.disbursementProgressByFundingSourceDetails.planningInformation'),
      cell: (props: CellContext<DisbursementProgressByFundingSourceDetail, unknown>) => (
        <EditableInputCell {...props} disabled />
      ),
      size: 250,
    },
    {
      id: 'projectManagementDirectorId',
      accessorKey: 'projectManagementDirectorId',
      header: t('fields.disbursementProgressByFundingSourceDetails.projectManagementDirectorId'),
      cell: (props: CellContext<DisbursementProgressByFundingSourceDetail, unknown>) => (
        <EditableDropdownCell
          {...props}
          model="user"
          queryKey={[QUERIES.USERS]}
          defaultText={props.row.original.projectName}
          disabled
        />
      ),
      size: 250,
    },
    {
      id: 'accountantId',
      accessorKey: 'accountantId',
      header: t('fields.disbursementProgressByFundingSourceDetails.accountantId'),
      cell: (props: CellContext<DisbursementProgressByFundingSourceDetail, unknown>) => (
        <EditableDropdownCell
          {...props}
          model="user"
          queryKey={[QUERIES.USERS]}
          defaultText={props.row.original.projectName}
          disabled
        />
      ),
      size: 250,
    },
    {
      id: 'departmentInChargeId',
      accessorKey: 'departmentInChargeId',
      header: t('fields.disbursementProgressByFundingSourceDetails.departmentInChargeId'),
      cell: (props: CellContext<DisbursementProgressByFundingSourceDetail, unknown>) => (
        <EditableDropdownCell
          {...props}
          model="department"
          queryKey={[QUERIES.DEPARTMENT]}
          defaultText={props.row.original.projectName}
          disabled
        />
      ),
      size: 250,
    },
    {
      id: 'removeRow',
      header: '',
      size: 10,
      cell: (props: CellContext<DisbursementProgressByFundingSourceDetail, unknown>) => {
        return (
          <DataTableRowActions
            onDelete={() => {
              props.table.options.meta?.removeRowByIndex(props.row.index);
            }}
            canDelete={role?.isCreate || role?.isUpdate}
          />
        );
      },
    },
  ];

  const { mutate: mutateDetails, isPending: loadingData } = useMutation({
    mutationKey: [MUTATE.DISBURSEMENT_PROGRESS_BY_FUNDING_SOURCE_GET_REPORT],
    mutationFn: ({
      year,
      budgetFundId,
      disbursementProgressByFundingSourceId,
    }: {
      year: Date;
      budgetFundId: number;
      disbursementProgressByFundingSourceId: number;
    }) => {
      return request<{ items: DisbursementProgressByFundingSourceDetail[] }>(
        axiosInstance.post('/disbursement-progress-by-funding-source/get-report', {
          filterColumn: [],
          pageIndex: 1,
          pageSize: -1,
          sortColumn: 'Id',
          sortOrder: 0,
          isPage: false,
          objParam: {
            year: year.toISOString(),
            budgetFundId,
            disbursementProgressByFundingSourceId,
          },
        })
      );
    },
    onSuccess: data => {
      if (isNaN(Number(editId))) {
        setValue(
          'disbursementProgressByFundingSourceDetails',
          data.items.map(item => ({ ...item, id: -getRandomNumber() }))
        );
      } else {
        const savedItems = getValues('disbursementProgressByFundingSourceDetails');
        const newData = data.items.map(getItem => {
          const savedItem = savedItems.find(item => item.projectId === getItem.projectId);
          if (savedItem) {
            return {
              ...getItem,
              month01: savedItem?.month01,
              month02: savedItem?.month02,
              month03: savedItem?.month03,
              month04: savedItem?.month04,
              month05: savedItem?.month05,
              month06: savedItem?.month06,
              month07: savedItem?.month07,
              month08: savedItem?.month08,
              month09: savedItem?.month09,
              month10: savedItem?.month10,
              month11: savedItem?.month11,
              month12: savedItem?.month12,
              month01Finance: savedItem?.month01Finance,
              month02Finance: savedItem?.month02Finance,
              month03Finance: savedItem?.month03Finance,
              month04Finance: savedItem?.month04Finance,
              month05Finance: savedItem?.month05Finance,
              month06Finance: savedItem?.month06Finance,
              month07Finance: savedItem?.month07Finance,
              month08Finance: savedItem?.month08Finance,
              month09Finance: savedItem?.month09Finance,
              month10Finance: savedItem?.month10Finance,
              month11Finance: savedItem?.month11Finance,
              month12Finance: savedItem?.month12Finance,
              disbursementRate01: savedItem?.disbursementRate01,
              disbursementRate02: savedItem?.disbursementRate02,
              disbursementRate03: savedItem?.disbursementRate03,
              disbursementRate01Finance: savedItem?.disbursementRate01Finance,
              disbursementRate02Finance: savedItem?.disbursementRate02Finance,
              disbursementRate03Finance: savedItem?.disbursementRate03Finance,
              plannedCumulativeFullYear: savedItem?.plannedCumulativeFullYear,
              plannedCumulativeFullYearFinance: savedItem?.plannedCumulativeFullYearFinance,
              disbursementRateFullYear: savedItem?.disbursementRateFullYear,
              disbursementRateFullYearFinance: savedItem?.disbursementRateFullYearFinance,
              constructionPeriod: savedItem?.constructionPeriod,
              issuesAndRecommendations: savedItem?.issuesAndRecommendations,
              mediumTermCapitalPlan: savedItem?.mediumTermCapitalPlan,
              annualImplementationPlanFinance: savedItem?.annualImplementationPlanFinance,
              id: savedItem.id > 0 ? savedItem.id : -getRandomNumber(),
            };
          }
          return getItem;
        });
        setValue('disbursementProgressByFundingSourceDetails', newData);
      }
    },
  });

  const handleGetData = () => {
    const [year, budgetFundId, disbursementProgressByFundingSourceId] = getValues([
      'fiscalYear',
      'budgetFundId',
      'id',
    ]);
    if (year && budgetFundId) {
      mutateDetails({
        year: year,
        budgetFundId: budgetFundId || 0,
        disbursementProgressByFundingSourceId,
      });
    }
  };

  return (
    <div>
      <div className="col-span-1 flex justify-end xl:col-span-3 xl:justify-start">
        <Button
          text={tcommon('action.getData')}
          className="w-fit"
          stylingMode="contained"
          type="default"
          icon="search"
          onClick={() => void handleGetData()}
          disabled={loadingData}
          //
        />
      </div>
      <DataTable
        tableId={TABLES.DISBURSEMENT_PROGRESS_BY_FUNDING_SOURCE}
        sortColumn="id"
        role={role}
        editableData={editableData}
        setEditableData={editedData => {
          setValue('disbursementProgressByFundingSourceDetails', editedData);
          calculateForm?.();
        }}
        onAddButtonClick={table => {
          const newRow = { ...defaultRow, id: -getRandomNumber() };
          table.options.meta?.addNewRow(newRow);
        }}
        syncQueryParams={false}
        columns={tableColumns}
        initialState={{
          columnVisibility: initialColumnVisibility,
        }}
        customToolbar={() => {
          let firstError;
          if (Array.isArray(errors.disbursementProgressByFundingSourceDetails)) {
            firstError = errors.disbursementProgressByFundingSourceDetails.find(
              detailError => detailError?.projectId?.message
            );
          }

          // Get the message from the found error, if any
          const errorMessage = firstError?.projectId?.message;

          return (
            <>
              {errorMessage && ( // Render only if an error message exists
                <ErrorMessage message={errorMessage} />
              )}
            </>
          );
        }}
      />
    </div>
  );
};
