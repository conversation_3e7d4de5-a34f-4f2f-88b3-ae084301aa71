import { QUERIES, TABLES } from '@/constant';
import { FinancialSettlementReport, FinancialSettlementReportBudgetFund } from '@/types';
import { useFormContext, useWatch } from 'react-hook-form';

import { customizeNumberCell } from '@/components/devex-data-grid';
import { DevexTreeList } from '@/components/devex-tree-list';
import { useEntity } from '@/hooks';
import { translationWithNamespace } from '@/lib/i18nUtils';
import { displayExpr } from '@/lib/utils';
import { GroupItem, Summary, TotalItem } from 'devextreme-react/data-grid';
import { Column, Lookup } from 'devextreme-react/tree-list';
import { useMemo, useRef } from 'react';

const calculateTotals = (
  data: FinancialSettlementReportBudgetFund[]
): FinancialSettlementReportBudgetFund[] => {
  const map: { [key: number]: FinancialSettlementReportBudgetFund } = {};
  const childrenMap: { [key: number]: FinancialSettlementReportBudgetFund[] } = {};

  const dataCopy = data.map(item => {
    const copy = { ...item };
    map[copy.budgetFundId || 0] = copy;

    const parentId = copy.budgetFundParentId!;

    if (parentId !== null) {
      if (!childrenMap[parentId]) {
        childrenMap[parentId] = [];
      }
      childrenMap[parentId].push(copy);
    }

    return copy;
  });

  const sumRecursive = (
    node: FinancialSettlementReportBudgetFund
  ): {
    approvedFinalProjectBudget: number;
    allocatedPlannedCapital: number;
    disbursedFunds: number;
  } => {
    const children = childrenMap[node.budgetFundId || 0] || [];

    const totalValue = {
      approvedFinalProjectBudget: node.approvedFinalProjectBudget || 0,
      allocatedPlannedCapital: node.allocatedPlannedCapital || 0,
      disbursedFunds: node.disbursedFunds || 0,
    };

    children.forEach(child => {
      const childTotal = sumRecursive(child);

      totalValue.approvedFinalProjectBudget =
        totalValue.approvedFinalProjectBudget + childTotal.approvedFinalProjectBudget;
      totalValue.allocatedPlannedCapital =
        totalValue.allocatedPlannedCapital + childTotal.allocatedPlannedCapital;
      totalValue.disbursedFunds = totalValue.disbursedFunds + childTotal.disbursedFunds;
    });

    node.approvedFinalProjectBudget = totalValue.approvedFinalProjectBudget;
    node.allocatedPlannedCapital = totalValue.allocatedPlannedCapital;
    node.disbursedFunds = totalValue.disbursedFunds;

    return totalValue;
  };

  dataCopy.forEach(item => {
    if (item.budgetFundParentId === null) {
      sumRecursive(item);
    }
  });

  return dataCopy;
};

// type FinancialSettlementReportBudgetFundEditableTableProps = {
//   role?: IUserPermission;
//   calculateForm?: () => void;
// };

const t = translationWithNamespace('financialSettlementReport');

export const FinancialSettlementReportBudgetFundEditableTable = () => {
  const { control } = useFormContext<FinancialSettlementReport>();

  const [editableData] = useWatch({
    control,
    name: ['financialSettlementReportBudgetFunds'],
  });

  const { list: budgetFunds } = useEntity({
    queryKey: [QUERIES.BUDGET_FUND],
    model: 'budget-fund',
  });

  const ref = useRef(null);
  const dataSourceWithSums = useMemo(() => calculateTotals(editableData), [editableData]);
  return (
    <div className="pb-10">
      <DevexTreeList
        ref={ref}
        id={TABLES.FINANCIAL_SETTLEMENT_REPORT_BUBGET_FUND_DETAIL}
        dataSource={dataSourceWithSums}
        // keyExpr="budgetFundId"
        // parentIdExpr={'budgetFundParentId'}
        hoverStateEnabled
        focusedRowEnabled={true}
        editing={{
          allowAdding: false,
          allowUpdating: rowData => {
            const data = rowData.row?.data as FinancialSettlementReportBudgetFund;
            return !data.child?.length;
          },
          allowDeleting: false,
          mode: 'cell',
        }}
        hideSerialNumber
        paging={{ enabled: false }}
        pager={{ visible: false }}
      >
        <Column
          dataField="budgetFundId"
          caption={t('fields.financialSettlementReportBudgetFunds.budgetFundId')}
          allowEditing={false}
        >
          <Lookup dataSource={budgetFunds} displayExpr={displayExpr(['name'])} valueExpr={'id'} />
        </Column>
        <Column
          dataField="approvedFinalProjectBudget"
          caption={t('fields.financialSettlementReportBudgetFunds.approvedFinalProjectBudget')}
          allowEditing={false}
          dataType="number"
          alignment="right"
          customizeText={customizeNumberCell()}
        />
        <Column
          dataField="allocatedPlannedCapital"
          caption={t('fields.financialSettlementReportBudgetFunds.allocatedPlannedCapital')}
          allowEditing={false}
          dataType="number"
          alignment="right"
          customizeText={customizeNumberCell()}
        />
        <Column
          dataField="disbursedFunds"
          caption={t('fields.financialSettlementReportBudgetFunds.disbursedFunds')}
          allowEditing={false}
          dataType="number"
          alignment="right"
          customizeText={customizeNumberCell()}
        />
        <Summary>
          {/* TotalItem: Hiển thị tổng cuối bảng */}
          <TotalItem
            column="approvedFinalProjectBudget"
            summaryType="sum"
            customizeText={customizeNumberCell()}
            displayFormat="{0}"
          />
          <GroupItem
            column="approvedFinalProjectBudget"
            summaryType="sum"
            customizeText={customizeNumberCell()}
            displayFormat="{0}"
            showInGroupFooter={true}
            alignByColumn={true}
          />

          <TotalItem
            column="allocatedPlannedCapital"
            summaryType="sum"
            customizeText={customizeNumberCell()}
            displayFormat="{0}"
          />
          <GroupItem
            column="allocatedPlannedCapital"
            summaryType="sum"
            customizeText={customizeNumberCell()}
            displayFormat="{0}"
            showInGroupFooter={true}
            alignByColumn={true}
          />

          <TotalItem
            column="disbursedFunds"
            summaryType="sum"
            customizeText={customizeNumberCell()}
            displayFormat="{0}"
          />
          <GroupItem
            column="disbursedFunds"
            summaryType="sum"
            customizeText={customizeNumberCell()}
            displayFormat="{0}"
            showInGroupFooter={true}
            alignByColumn={true}
          />
        </Summary>
      </DevexTreeList>
    </div>
  );
};
