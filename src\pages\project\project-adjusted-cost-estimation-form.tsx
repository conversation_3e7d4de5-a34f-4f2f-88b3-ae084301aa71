import { BasicDialog } from '@/components/basic-dialog';
import { DataTable, EditableDropdownCell, EditableInputCell } from '@/components/data-table';
import { ImportExcelConfigForm } from '@/components/import-excel-config-form';
import { PageLayout } from '@/components/page-layout';
import { FormField, FormLabel } from '@/components/ui/form';
import { enterLabel, PROFESSIONS, QUERIES, selectLabel, TABLES } from '@/constant';
import { useBoolean, useEntity } from '@/hooks';
import { getRandomNumber } from '@/lib/number';
import { displayExpr } from '@/lib/utils';
import {
  CostItem,
  CostItemType,
  defaultValuesProject,
  Project,
  ProjectDetail,
  ProjectDetailSummaries,
  ProjectTabChildrenProps,
} from '@/types';
import { ColumnDef } from '@tanstack/react-table';
import { Button, DataGrid, DateBox, TextBox } from 'devextreme-react';
import { Column, Lookup, Summary, TotalItem } from 'devextreme-react/data-grid';
import { SyntheticEvent, useEffect, useMemo } from 'react';
import { useFormContext, useWatch } from 'react-hook-form';
import { calculateTaxValues } from '.';
import { customizeNumberCell } from '@/components/devex-data-grid';

const [defaultRow] = defaultValuesProject.projectAdjustedCostEstimationDetails;

export const ProjectAdjustedCostEstimationForm = ({
  role,
  loading,
  onBackToList,
  onCreateNew,
  onSubmit,
  t,
}: ProjectTabChildrenProps) => {
  const { state: isImportFormOpen, toggle: toggleImportForm } = useBoolean(false);
  const { control, setValue } = useFormContext<Project>();
  const [editableData, editableSummaries, projectId] = useWatch({
    control,
    name: ['projectAdjustedCostEstimationDetails', 'projectAdjustedCostEstimationSummaries', 'id'],
  });

  const { fetch: fetchCostItems } = useEntity({
    queryKey: [QUERIES.COST_ITEM],
    model: 'cost-item',
  });
  const { list: costItemTypes } = useEntity<CostItemType>({
    queryKey: [QUERIES.COST_ITEM_TYPE],
    model: 'cost-item-type',
  });

  useEffect(() => {
    if (editableSummaries.length === 0 || editableSummaries.length !== costItemTypes.length) {
      const summary: ProjectDetailSummaries[] = costItemTypes
        .map(costItemType => {
          return {
            id: -getRandomNumber(),
            projectId, 
            costItemValue: editableSummaries.find(item=>item.costItemTypeId === costItemType.id)?.costItemValue || 0,
            costItemValueBefore: editableSummaries.find(item=>item.costItemTypeId === costItemType.id)?.costItemValueBefore || 0,
            costItemTypeId: costItemType.id,
            sort: costItemType.sort,
          };
        })
        .sort((a, b) => (a?.sort ?? 0) - (b?.sort ?? 0));
      setValue('projectAdjustedCostEstimationSummaries', summary);
    }
  }, [costItemTypes, editableSummaries, projectId, setValue]);

  const columns: ColumnDef<ProjectDetail>[] = useMemo(
    () => [
      {
        id: 'costItemId',
        accessorKey: 'costItemId',
        header: t('fields.projectDetail.costItemId'),
        cell: props => (
          <EditableDropdownCell<ProjectDetail, CostItem>
            {...props}
            disabled
            model="cost-item"
            queryKey={[QUERIES.COST_ITEM]}
            className="disabled:opacity-100"
            defaultText={props.row.original.costItemName}
          />
        ),
      },
      {
        id: 'symbol',
        accessorKey: 'symbol',
        header: t('fields.projectDetail.symbol'),
        cell: props => <EditableInputCell {...props} readOnly />,
      },
      {
        id: 'percentageRate',
        accessorKey: 'percentageRate',
        header: t('fields.projectDetail.percentageRate'),
        cell: props => <EditableInputCell {...props} readOnly type="number" hideDecimal />,
      },
      {
        id: 'calculationMethod',
        accessorKey: 'calculationMethod',
        header: t('fields.projectDetail.calculationMethod'),
        cell: props => <EditableInputCell {...props} readOnly />,
      },
      {
        id: 'preTaxValue',
        accessorKey: 'preTaxValue',
        header: t('fields.projectDetail.preTaxValue'),
        cell: props => <EditableInputCell {...props} type="number" readOnly isMoney />,
      },
      {
        id: 'vat',
        accessorKey: 'vat',
        header: t('fields.projectDetail.vat'),
        cell: props => <EditableInputCell {...props} type="number" readOnly hideDecimal />,
      },
      {
        id: 'vatTax',
        accessorKey: 'vatTax',
        header: t('fields.projectDetail.vatTax'),
        cell: props => <EditableInputCell {...props} type="number" readOnly isMoney />,
      },
      {
        id: 'postTaxValue',
        accessorKey: 'postTaxValue',
        header: t('fields.projectDetail.postTaxValue'),
        cell: props => <EditableInputCell {...props} type="number" readOnly isMoney />,
      },
      {
        id: 'postTaxValueBefore',
        accessorKey: 'postTaxValueBefore',
        header: t('fields.projectDetail.postTaxValueBefore'),
        cell: props => <EditableInputCell {...props} type="number" readOnly isMoney />,
      },
    ],
    [t]
  );

  const columnsForImportConfig = columns.map(column => {
    return {
      field: column.id,
      header: column.header as string,
    };
  });

  return (
    <PageLayout
      onSaveChange={e => {
        const target = e.event?.currentTarget as unknown as SyntheticEvent<HTMLElement>;
        onSubmit(target);
      }}
      canSaveChange={role?.isUpdate}
      isSaving={loading}
      onCancel={onBackToList}
      customElementLeft={
        <>
          <Button
            text={t('content.createNew', { ns: 'common' })}
            className="uppercase"
            stylingMode="outlined"
            type="default"
            icon="plus"
            onClick={onCreateNew}
          />
        </>
      }
      contentClassName="!h-[calc(100vh-220px)]"
    >
      <div className="grid grid-cols-1 gap-x-8 gap-y-4 xl:grid-cols-24">
        <div className="col-span-1 space-y-4 xl:col-span-8">
          <div className="flex w-full items-center md:w-2/3 xl:w-full">
            <FormLabel
              className="hidden w-[60px] md:block"
              name="adjustedCostEstimationDocumentCode"
              htmlFor="adjustedCostEstimationDocumentCode"
            >
              {t('fields.documentCode')}
            </FormLabel>
            <FormField
              className="min-w-0 flex-1"
              name="adjustedCostEstimationDocumentCode"
              label={t('fields.documentCode')}
            >
              <TextBox placeholder={`${enterLabel} ${t('fields.documentCode')}`} readOnly />
            </FormField>
          </div>
          <div className="flex w-full items-center sm:w-2/3">
            <FormLabel
              className="hidden w-[60px] md:block"
              name="adjustedCostEstimationSigningDate"
              htmlFor="adjustedCostEstimationSigningDate"
            >
              {t('fields.signingDate')}
            </FormLabel>
            <FormField
              className="min-w-0 flex-1"
              name="adjustedCostEstimationSigningDate"
              type="date"
              label={t('fields.signingDate')}
            >
              <DateBox
                placeholder={`${selectLabel} ${t('fields.signingDate')}`}
                pickerType="calendar"
                focusStateEnabled={false}
              />
            </FormField>
          </div>
          <div className="flex w-full items-center sm:w-2/3">
            <FormLabel
              className="hidden w-[60px] md:block"
              name="adjustedCostEstimationSignerId"
              htmlFor="adjustedCostEstimationSignerId"
            >
              {t('fields.signerId')}
            </FormLabel>
            <FormField
              className="min-w-0 flex-1"
              name="adjustedCostEstimationSignerId"
              label={t('fields.signerId')}
            >
              <TextBox placeholder={`${enterLabel} ${t('fields.signerId')}`} />
            </FormField>
          </div>
        </div>
        <div className="col-span-1 xl:col-span-16">
          <DataGrid
            dataSource={editableSummaries}
            columnAutoWidth
            allowColumnResizing
            allowColumnReordering
            showBorders
            showColumnLines
            showRowLines
            width={'auto'}
            id={TABLES.ADJUSTED_INVESTMENT_DETAIL_SUMMARY}
            className="column-header-wrap"
          >
            <Column
              dataType="number"
              caption="STT"
              width={50}
              cellRender={({ row }) => Number(row.rowIndex) + 1}
            />
            <Column
              dataField="costItemTypeId"
              caption={t('fields.projectDetailSummaries.costItemTypeId')}
            >
              <Lookup
                dataSource={costItemTypes}
                valueExpr={'id'}
                displayExpr={displayExpr(['name'])}
              />
            </Column>
            <Column
              dataField="costItemValue"
              caption={t('fields.projectDetailSummaries.costItemValue')}
              dataType="number"
              alignment="right"
              customizeText={customizeNumberCell(0)}
            />
            <Column
              dataField="costItemValueBefore"
              caption={t('fields.projectDetailSummaries.costItemValueBefore')}
              dataType="number"
              alignment="right"
              customizeText={customizeNumberCell(0)}
            />
            <Summary>
              <TotalItem
                column="costItemTypeId"
                summaryType="count"
                customizeText={() => t('fields.projectDetailSummaries.totalAmount')}
              />
              <TotalItem
                column="costItemValue"
                summaryType="sum"
                customizeText={customizeNumberCell(0)}
                displayFormat="{0}"
              />
              <TotalItem
                column="costItemValueBefore"
                summaryType="sum"
                customizeText={customizeNumberCell(0)}
                displayFormat="{0}"
              />
            </Summary>
          </DataGrid>
        </div>
        <div className="col-span-1 xl:col-span-24">
          <DataTable
            role={role}
            showPagination={false}
            editableData={editableData}
            tableId={TABLES.ADJUSTED_COST_ESTIMATION_DETAIL}
            syncQueryParams={false}
            initialState={{
              columnVisibility: {
                costItemId: true,
                symbol: true,
                percentageRate: true,
                calculationMethod: true,
                preTaxValue: true,
                vat: true,
                vatTax: true,
                postTaxValue: true,
              },
            }}
            setEditableData={editedData => {
              setValue('projectAdjustedCostEstimationDetails', editedData);
            }}
            columns={columns}
          />
        </div>
      </div>
      <BasicDialog
        open={isImportFormOpen}
        title="Import Excel"
        toggle={toggleImportForm}
        className="max-w-[100vw] md:max-w-[90vw]"
      >
        <ImportExcelConfigForm<ProjectDetail>
          onApply={data => {
            setValue(
              'projectAdjustedCostEstimationDetails',
              data.map(item => {
                return {
                  ...defaultRow,
                  ...item,
                  ...calculateTaxValues(item.preTaxValue || 0, item.vat || 8),
                  projectId,
                };
              })
            );
            toggleImportForm();
          }}
          importModel="project"
          onClose={toggleImportForm}
          professionType={PROFESSIONS.PROJECT}
          professionColumns={columnsForImportConfig}
          onImported={() => {
            fetchCostItems({});
          }}
          additionalFormValues={[{ key: 'refId', value: projectId?.toString() || '' }]}
        />
      </BasicDialog>
    </PageLayout>
  );
};
