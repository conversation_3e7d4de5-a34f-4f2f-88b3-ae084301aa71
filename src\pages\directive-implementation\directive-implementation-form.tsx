import { zod<PERSON><PERSON>olver } from '@hookform/resolvers/zod';
import { SyntheticEvent, useMemo, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useParams } from 'react-router-dom';

import { PageLayout } from '@/components/page-layout';
import { Form, FormCombobox, FormField, FormLabel } from '@/components/ui/form';
import {
  DIRECTIVE_IMPLEMENTATION_REPORT_TYPES,
  enterLabel,
  MUTATE,
  PATHS,
  PERMISSIONS,
  PROFESSIONS,
  QUERIES,
  selectLabel,
} from '@/constant';
import { useAuth, useEntity, useFormHandler, usePermission } from '@/hooks';
import { useFormNavigate } from '@/hooks/use-form-navigate';
import { createMutationSuccessFn } from '@/lib/i18nUtils';
import {
  createPostMutateFn,
  createPutMutateFn,
  createQueryByIdFn,
  createQueryReport,
} from '@/services';
import {
  DirectiveImplementation,
  directiveImplementationSchema,
  defaultValuesDirectiveImplementation,
  DocumentDecision,
} from '@/types';
import { DateBox, TextBox } from 'devextreme-react';
import Button from 'devextreme-react/button';
import JoditEditor from 'jodit-react';
import { formatDate, toDateType, toLocaleDate } from '@/lib/date';
import { exportToWord } from '@/lib/utils';
import { useReactToPrint } from 'react-to-print';
import { useQuery } from '@tanstack/react-query';
import { safeRenderString } from '@/lib/print';
import {
  directiveImplementationTargetPrintTemplate,
  directiveImplementationTaskPrintTemplate,
} from './directive-implementation-print-template';
import { removeAccents } from '@/lib/text';
import { snakeCase } from 'lodash';

const onDirectiveImplementationMutationSuccess = createMutationSuccessFn('directiveImplementation');

const config = {
  readonly: false,
  placeholder: '',
  cleanHTML: {
    removeEmptyElements: false, // Không xóa các thẻ rỗng
    fillEmptyParagraph: false, // Không tự chèn nội dung vào thẻ <p> rỗng
  },
};

export const DirectiveImplementationForm = () => {
  const { id: editId } = useParams();
  const { t } = useTranslation('directiveImplementation');
  const role = usePermission(PERMISSIONS.DIRECTIVE_IMPLEMENTATION);
  const { user } = useAuth();
  const ref = useRef<HTMLDivElement>(null);
  const [content, setContent] = useState('');

  const { list: documentDecisions } = useEntity<DocumentDecision>({
    queryKey: [QUERIES.DOCUMENT_DECISION],
    model: 'document-decision',
  });

  const documentDecisionFields = useMemo(() => {
    if (documentDecisions) {
      return documentDecisions.reduce<Record<string, string>>((acc, decision) => {
        acc[decision.code] = decision.content;
        return acc;
      }, {});
    }
    return {};
  }, [documentDecisions]);

  const { goBackToList, goToUpdate, goToNew } = useFormNavigate(PATHS.DIRECTIVE_IMPLEMENTATION);

  const defaultValues = useMemo(
    () => ({
      ...defaultValuesDirectiveImplementation,
      userCreatedId: user?.userId,
    }),
    [user?.userId]
  );

  const { handleSubmit, loading, methods, data } = useFormHandler<DirectiveImplementation>({
    queryKey: [MUTATE.DIRECTIVE_IMPLEMENTATION, editId],
    mutateKey: [MUTATE.DIRECTIVE_IMPLEMENTATION],
    queryId: Number(editId) || 0,
    invalidateKey: [QUERIES.DIRECTIVE_IMPLEMENTATION],
    readFn: createQueryByIdFn<DirectiveImplementation>('directive-implementation'),
    createFn: createPostMutateFn<DirectiveImplementation>('directive-implementation'),
    updateFn: createPutMutateFn<DirectiveImplementation>('directive-implementation'),
    formatPayloadFn: (data): DirectiveImplementation => ({
      ...data,
      directiveImplementationReportTime: toLocaleDate(data.directiveImplementationReportTime),
      fromDate: toLocaleDate(data?.fromDate)!,
      toDate: toLocaleDate(data?.toDate)!,
      code: data.code ? data.code : 'BCKQTHCD-' + formatDate(new Date(), 'yyyyMMddHHmm'),
      content: methods.getValues('content') ?? '',
    }),
    formatResponseFn: data => {
      methods.setValue('content', data.content);
      setContent(data.content ?? '');
      return {
        ...data,
        directiveImplementationReportTime: toDateType(data.directiveImplementationReportTime),
        fromDate: toDateType(data?.fromDate)!,
        toDate: toDateType(data?.toDate)!,
      };
    },
    onCreateSuccess: data => {
      onDirectiveImplementationMutationSuccess(data);
      goToUpdate(data);
    },
    onUpdateSuccess: onDirectiveImplementationMutationSuccess,
    formOptions: {
      resolver: zodResolver(directiveImplementationSchema),
      defaultValues,
    },
  });

  const onCreateNew = () => {
    goToNew();
    methods.reset(defaultValues);
    setContent('');
  };

  const { refetch: directiveImplementationReportDetails } = useQuery({
    queryKey: [QUERIES.DIRECTIVE_IMPLEMENTATION_REPORT],
    queryFn: () => {
      const fromDate = methods.getValues('fromDate');
      const toDate = methods.getValues('toDate');
      return createQueryReport('directive-implementation')({
        pageIndex: 1,
        pageSize: -1,
        sortColumn: 'Id',
        sortOrder: 1,
        isPage: false,
        filterColumn: [],
        objParam: {
          fromDate: toLocaleDate(fromDate),
          toDate: toLocaleDate(toDate),
        },
      });
    },
  });

  const printFn = useReactToPrint({ content: () => ref.current });

  const printHandler = () => {
    if (ref.current) {
      printFn();
    }
  };

  const getDataHandler = async () => {
    try {
      const result = await directiveImplementationReportDetails();
      const directiveImplementationReportTime =
        methods.getValues('directiveImplementationReportTime') ?? new Date();
      if (result.data?.items && result.data.items.length > 0) {
        const items = result.data.items;
        const targetDetails = safeRenderString(directiveImplementationTargetPrintTemplate, {
          targetDetails: items.filter(
            (item: any) => item.typeId === DIRECTIVE_IMPLEMENTATION_REPORT_TYPES.TARGET
          ),
        });
        const taskDetails = safeRenderString(directiveImplementationTaskPrintTemplate, {
          taskDetails: items.filter(
            (item: any) => item.typeId === DIRECTIVE_IMPLEMENTATION_REPORT_TYPES.TASK
          ),
        });

        const htmlTemplate = safeRenderString(content, {
          targetDetails: !targetDetails.error ? targetDetails.html : '',
          taskDetails: !taskDetails.error ? taskDetails.html : '',
          day: directiveImplementationReportTime.getDate(),
          month: (directiveImplementationReportTime.getMonth() + 1).toString().padStart(2, '0'),
          year: directiveImplementationReportTime.getFullYear(),
          ...documentDecisionFields,
        });

        if (!htmlTemplate.error) {
          setContent(htmlTemplate.html);
        }
      }
    } catch (error) {
      console.error('Error fetching data:', error);
    }
  };

  const printoutElement = <div ref={ref} dangerouslySetInnerHTML={{ __html: content }} />;

  return (
    <>
      <Form {...methods}>
        <form autoComplete="off">
          <PageLayout
            onSaveChange={e => {
              handleSubmit(e.event?.currentTarget as unknown as SyntheticEvent<HTMLElement>);
            }}
            header={editId !== 'new' ? t('page.form.edit') : t('page.form.addNew')}
            canSaveChange={!isNaN(Number(editId)) ? role?.isUpdate : role?.isCreate}
            isSaving={loading}
            onCancel={goBackToList}
            customElementLeft={
              <>
                <Button
                  text={t('content.createNew', { ns: 'common' })}
                  className="uppercase"
                  stylingMode="outlined"
                  type="default"
                  icon="plus"
                  onClick={onCreateNew}
                />
              </>
            }
            customElementRight={
              <>
                <Button
                  text={t('action.getData', { ns: 'common' })}
                  className="mb-2 w-fit md:mb-0"
                  stylingMode="contained"
                  type="default"
                  icon="search"
                  onClick={() => void getDataHandler()}
                />
                <Button
                  type="default"
                  stylingMode="contained"
                  icon="print"
                  text={t('page.buttons.exportToWord')}
                  disabled={!role?.isPrint || !data}
                  onClick={() => {
                    exportToWord(
                      content,
                      snakeCase(removeAccents(methods.getValues('name') ?? 'document'))
                    );
                  }}
                />
                <Button
                  type="default"
                  stylingMode="contained"
                  icon="print"
                  text={t('page.buttons.print')}
                  disabled={!role?.isPrint || !data}
                  onClick={() => {
                    printHandler();
                  }}
                />
              </>
            }
          >
            <div className="grid grid-cols-1 gap-4 gap-x-8 lg:grid-cols-2">
              {/* Cột 1 dòng 1 */}
              <div className="order-1 lg:order-1">
                {/* Tên báo cáo */}
                <div className="flex items-center">
                  <FormLabel name="name" htmlFor="name" className="hidden w-[100px] md:block">
                    {t('fields.name')}
                  </FormLabel>
                  <FormField
                    label={t('fields.name')}
                    id="name"
                    name="name"
                    className="flex-1 md:w-[250px]"
                  >
                    <TextBox placeholder={`${enterLabel} ${t('fields.name')}`} />
                  </FormField>
                </div>

                {/* Biếu mẫu */}
                <div className="flex items-center pt-4">
                  <FormLabel
                    name="formDocumentManagerId"
                    htmlFor="formDocumentManagerId"
                    className="hidden w-[100px] md:block"
                  >
                    {t('fields.formDocumentManagerId')}
                  </FormLabel>
                  <FormField
                    id="formDocumentManagerId"
                    name="formDocumentManagerId"
                    className="flex-1 md:w-[250px]"
                    label={t('fields.formDocumentManagerId')}
                  >
                    <FormCombobox
                      placeholder={`${selectLabel} ${t('fields.formDocumentManagerId')}`}
                      model="form-document-manager"
                      queryKey={[QUERIES.FORM_DOCUMENT_MANAGER]}
                      filter={(item: any) =>
                        item?.professionType === PROFESSIONS.DIRECTIVE_IMPLEMENTATION_REPORT
                      }
                      onSelectItem={item => {
                        methods.setValue(
                          'content',
                          (item?.htmlBody as string | null | undefined) || ''
                        );
                        setContent((item?.htmlBody as string) || '');
                      }}
                    />
                  </FormField>
                </div>

                {/* Từ ngày */}
                <div className="flex items-center pt-4 lg:w-2/3 2xl:w-1/2">
                  <FormLabel
                    name="fromDate"
                    htmlFor="fromDate"
                    className="hidden w-[100px] md:block"
                  >
                    {t('fields.fromDate')}
                  </FormLabel>
                  <FormField
                    id="fromDate"
                    name="fromDate"
                    className="flex-1 md:w-[250px]"
                    type="date"
                    label={t('fields.fromDate')}
                  >
                    <DateBox
                      placeholder={`${selectLabel} ${t('fields.fromDate')}`}
                      pickerType="calendar"
                      focusStateEnabled={false}
                    />
                  </FormField>
                </div>

                {/* Đến ngày */}
                <div className="flex items-center pt-4 lg:w-2/3 2xl:w-1/2">
                  <FormLabel name="toDate" htmlFor="toDate" className="hidden w-[100px] md:block">
                    {t('fields.toDate')}
                  </FormLabel>
                  <FormField
                    id="toDate"
                    name="toDate"
                    className="flex-1 md:w-[250px]"
                    type="date"
                    label={t('fields.toDate')}
                  >
                    <DateBox
                      placeholder={`${selectLabel} ${t('fields.toDate')}`}
                      pickerType="calendar"
                      focusStateEnabled={false}
                    />
                  </FormField>
                </div>
              </div>

              {/* Cột 2 dòng 1 */}
              <div className="order-2 lg:order-2">
                {/* Ngày lập */}
                <div className="flex items-center pt-4 md:pt-0 lg:w-2/3 2xl:w-1/2">
                  <FormLabel
                    htmlFor="directiveImplementationReportTime"
                    className="hidden w-[100px] md:block"
                  >
                    {t('fields.directiveImplementationReportTime')}
                  </FormLabel>
                  <FormField
                    id="directiveImplementationReportTime"
                    name="directiveImplementationReportTime"
                    className="flex-1 md:w-[250px]"
                    type="date"
                    label={t('fields.directiveImplementationReportTime')}
                  >
                    <DateBox
                      placeholder={`${selectLabel} ${t('fields.directiveImplementationReportTime')}`}
                      pickerType="calendar"
                      focusStateEnabled={false}
                    />
                  </FormField>
                </div>

                {/* Người lập */}
                <div className="flex items-center pt-4">
                  <FormLabel
                    name="userCreatedId"
                    htmlFor="userCreatedId"
                    className="hidden w-[100px] md:block"
                  >
                    {t('fields.userCreatedId')}
                  </FormLabel>
                  <FormField
                    id="userCreatedId"
                    name="userCreatedId"
                    className="flex-1 md:w-[250px]"
                    label={t('fields.userCreatedId')}
                  >
                    <FormCombobox
                      placeholder={`${selectLabel} ${t('fields.userCreatedId')}`}
                      model="user"
                      queryKey={[QUERIES.USERS]}
                      disabled
                    />
                  </FormField>
                </div>

                {/* Ghi chú */}
                <div className="flex items-center pt-4">
                  <FormLabel name="note" htmlFor="note" className="hidden w-[100px] md:block">
                    {t('fields.note')}
                  </FormLabel>
                  <FormField
                    id="note"
                    name="note"
                    className="flex-1 md:w-[250px]"
                    label={t('fields.note')}
                  >
                    <TextBox placeholder={`${enterLabel} ${t('fields.note')}`} />
                  </FormField>
                </div>
              </div>

              {/* Dòng 2 */}
              <div className="order-3 h-full pt-4 md:order-3 md:pt-0 lg:col-span-2">
                {/* Nội dung */}
                <JoditEditor
                  tabIndex={1}
                  value={content}
                  config={config}
                  onBlur={setContent}
                  className="directive-implementation-content w-full"
                />
              </div>
            </div>
            <div className="hidden">{printoutElement}</div>
          </PageLayout>
        </form>
      </Form>
    </>
  );
};
