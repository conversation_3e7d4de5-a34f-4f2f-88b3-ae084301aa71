import { requiredTextWithNamespace } from '@/lib/i18nUtils';
import { z } from 'zod';
import { ArrayElement } from './common';

export const requireDisbursementProgressSummaryText = requiredTextWithNamespace(
  'disbursementProgressSummary'
);

export const disbursementProgressSummarySchema = z.object({
  id: z.number(),
  budgetYear: z.date().nullable().optional(),
  disbursementProgressSummaryTime: z.date().nullable().optional(),
  userCreatedId: z.number().nullable(),
  note: z.string().nullable().optional(),
  disbursementProgressSummaryDetails: z.array(
    z.object({
      id: z.number(),
      disbursementProgressSummaryId: z.number().nullable(),
      rootId: z.number().nullable(),
      content: z.string().nullable().optional(),
      value: z.number().nullable(),
      unit: z.string().nullable().optional(),
      month01: z.number().nullable(),
      month01Finance: z.number().nullable(),
      month02: z.number().nullable(),
      month02Finance: z.number().nullable(),
      month03: z.number().nullable(),
      month03Finance: z.number().nullable(),
      q1PlannedDisbursement: z.number().nullable(),
      disbursementAccordingQuarter1: z.number().nullable(),
      month04: z.number().nullable(),
      month04Finance: z.number().nullable(),
      month05: z.number().nullable(),
      month05Finance: z.number().nullable(),
      month06: z.number().nullable(),
      month06Finance: z.number().nullable(),
      q2PlannedDisbursement: z.number().nullable(),
      disbursementAccordingQuarter2: z.number().nullable(),
      month07: z.number().nullable(),
      month07Finance: z.number().nullable(),
      month08: z.number().nullable(),
      month08Finance: z.number().nullable(),
      month09: z.number().nullable(),
      month09Finance: z.number().nullable(),
      q3PlannedDisbursement: z.number().nullable(),
      disbursementAccordingQuarter3: z.number().nullable(),
      month10: z.number().nullable(),
      month10Finance: z.number().nullable(),
      month11: z.number().nullable(),
      month11Finance: z.number().nullable(),
      month12: z.number().nullable(),
      month12Finance: z.number().nullable(),
      q4PlannedDisbursement: z.number().nullable(),
      disbursementAccordingQuarter4: z.number().nullable(),
    })
  ),
  disbursementProgressSummaryDetailByRooms: z.array(
    z.object({
      id: z.number(),
      disbursementProgressSummaryId: z.number().nullable(),
      roomId: z.number().nullable(),
      roomName: z.string().nullable().optional(),
      plannedCapitalValue: z.number().nullable(),
      content: z.string().nullable(),
      month01: z.number().nullable(),
      month02: z.number().nullable(),
      month03: z.number().nullable(),
      q1PlannedDisbursement: z.number().nullable(),
      month04: z.number().nullable(),
      month05: z.number().nullable(),
      month06: z.number().nullable(),
      q2PlannedDisbursement: z.number().nullable(),
      month07: z.number().nullable(),
      month08: z.number().nullable(),
      month09: z.number().nullable(),
      q3PlannedDisbursement: z.number().nullable(),
      month10: z.number().nullable(),
      month11: z.number().nullable(),
      month12: z.number().nullable(),
      q4PlannedDisbursement: z.number().nullable(),
    })
  ),
});

export type DisbursementProgressSummary = z.infer<typeof disbursementProgressSummarySchema>;
export type DisbursementProgressSummaryDetail = ArrayElement<
  DisbursementProgressSummary['disbursementProgressSummaryDetails']
>;
export type DisbursementProgressSummaryDetailByRoom = ArrayElement<
  DisbursementProgressSummary['disbursementProgressSummaryDetailByRooms']
>;

export const defaultValuesDisbursementProgressSummary: DisbursementProgressSummary = {
  id: 0, // Khóa chính
  budgetYear: new Date(), // Năm ngân sách
  disbursementProgressSummaryTime: new Date(), // Ngày lập
  userCreatedId: null, // Người lập
  note: '', // Ghi chú,
  disbursementProgressSummaryDetails: [
    {
      id: 0, // Khóa chính
      disbursementProgressSummaryId: 0, //
      rootId: 0, //
      content: '', // Nội dung
      value: 0, // Giá trị
      unit: '', // Đvt
      month01: 0, // Dự kiến lũy kế giải ngân từ đầu năm đến hết tháng 1/2025
      month01Finance: 0, // Dự kiến lũy kế giải ngân từ đầu năm đến hết tháng 1/2025 UBND TP
      month02: 0, // Dự kiến lũy kế giải ngân từ đầu năm đến hết tháng 2/2025
      month02Finance: 0, // Dự kiến lũy kế giải ngân từ đầu năm đến hết tháng 2/2025 UBND TP
      month03: 0, // Dự kiến lũy kế giải ngân từ đầu năm đến hết tháng 3/2025
      month03Finance: 0, // Dự kiến lũy kế giải ngân từ đầu năm đến hết tháng 3/2025 UBND TP
      q1PlannedDisbursement: 0, // GIẢI NGÂN THEO KH SỐ 191/KH-QLDA Quý I/2025
      disbursementAccordingQuarter1: 0, // GIẢI NGÂN THEO UBND TP Quý I/2025
      month04: 0, // Dự kiến lũy kế giải ngân từ đầu năm đến hết tháng 4/2025
      month04Finance: 0, // Dự kiến lũy kế giải ngân từ đầu năm đến hết tháng 4/2025 UBND TP
      month05: 0, // Dự kiến lũy kế giải ngân từ đầu năm đến hết tháng 5/2025
      month05Finance: 0, // Dự kiến lũy kế giải ngân từ đầu năm đến hết tháng 5/2025 UBND TP
      month06: 0, // Dự kiến lũy kế giải ngân từ đầu năm đến hết tháng 6/2025
      month06Finance: 0, // Dự kiến lũy kế giải ngân từ đầu năm đến hết tháng 6/2025 UBND TP
      q2PlannedDisbursement: 0, // GIẢI NGÂN THEO KH SỐ 191/KH-QLDA Quý II/2025
      disbursementAccordingQuarter2: 0, // GIẢI NGÂN THEO UBND TP Quý II/2025
      month07: 0, // Dự kiến lũy kế giải ngân từ đầu năm đến hết tháng 7/2025
      month07Finance: 0, // Dự kiến lũy kế giải ngân từ đầu năm đến hết tháng 7/2025 UBND TP
      month08: 0, // Dự kiến lũy kế giải ngân từ đầu năm đến hết tháng 8/2025 UBND TP
      month08Finance: 0, // Dự kiến lũy kế giải ngân từ đầu năm đến hết tháng 8/2025 UBND TP
      month09: 0, // Dự kiến lũy kế giải ngân từ đầu năm đến hết tháng 9/2025
      month09Finance: 0, // Dự kiến lũy kế giải ngân từ đầu năm đến hết tháng 9/2025 UBND TP
      q3PlannedDisbursement: 0, // GIẢI NGÂN THEO KH SỐ 191/KH-QLDA Quý III/2025
      disbursementAccordingQuarter3: 0, // GIẢI NGÂN THEO UBND TP Quý III/2025
      month10: 0, // Dự kiến lũy kế giải ngân từ đầu năm đến hết tháng 10/2025
      month10Finance: 0, // Dự kiến lũy kế giải ngân từ đầu năm đến hết tháng 10/2025 UBND TP
      month11: 0, // Dự kiến lũy kế giải ngân từ đầu năm đến hết tháng 11/2025 UBND TP
      month11Finance: 0, // Dự kiến lũy kế giải ngân từ đầu năm đến hết tháng 11/2025 UBND TP
      month12: 0, // Dự kiến lũy kế giải ngân từ đầu năm đến hết tháng 12/2025 UBND TP
      month12Finance: 0, // Dự kiến lũy kế giải ngân từ đầu năm đến hết tháng 12/2025 UBND TP
      q4PlannedDisbursement: 0, // GIẢI NGÂN THEO KH SỐ 191/KH-QLDA Quý IV/2025
      disbursementAccordingQuarter4: 0, // GIẢI NGÂN THEO UBND TP Quý IV/2025
    },
  ],
  disbursementProgressSummaryDetailByRooms: [
    {
      id: 0, // Khóa chính
      disbursementProgressSummaryId: 0, //
      roomId: 0, // Phòng
      plannedCapitalValue: 0, // Giá trị kế hoạch vốn
      content: '', // Nội dung
      month01: 0, // Dự kiến lũy kế giải ngân từ đầu năm đến hết tháng 1/2025
      month02: 0, // Dự kiến lũy kế giải ngân từ đầu năm đến hết tháng 2/2025
      month03: 0, // Dự kiến lũy kế giải ngân từ đầu năm đến hết tháng 3/2025
      q1PlannedDisbursement: 0, // GIẢI NGÂN THEO KH SỐ 191/KH-QLDA Quý I/2025
      month04: 0, // Dự kiến lũy kế giải ngân từ đầu năm đến hết tháng 4/2025
      month05: 0, // Dự kiến lũy kế giải ngân từ đầu năm đến hết tháng 5/2025
      month06: 0, // Dự kiến lũy kế giải ngân từ đầu năm đến hết tháng 6/2025
      q2PlannedDisbursement: 0, // GIẢI NGÂN THEO KH SỐ 191/KH-QLDA Quý II/2025
      month07: 0, // Dự kiến lũy kế giải ngân từ đầu năm đến hết tháng 7/2025
      month08: 0, // Dự kiến lũy kế giải ngân từ đầu năm đến hết tháng 8/2025 UBND TP
      month09: 0, // Dự kiến lũy kế giải ngân từ đầu năm đến hết tháng 9/2025
      q3PlannedDisbursement: 0, // GIẢI NGÂN THEO KH SỐ 191/KH-QLDA Quý III/2025
      month10: 0, // Dự kiến lũy kế giải ngân từ đầu năm đến hết tháng 10/2025
      month11: 0, // Dự kiến lũy kế giải ngân từ đầu năm đến hết tháng 11/2025 UBND TP
      month12: 0, // Dự kiến lũy kế giải ngân từ đầu năm đến hết tháng 12/2025 UBND TP
      q4PlannedDisbursement: 0, // GIẢI NGÂN THEO KH SỐ 191/KH-QLDA Quý IV/2025
    },
  ],
};
