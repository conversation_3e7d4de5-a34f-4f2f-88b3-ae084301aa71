import { ErrorMessage } from '@/components/ui/error-message';
import { QUERIES, TABLES } from '@/constant';
import {
  defaultValuesFinancialSettlementReport,
  FinancialSettlementReport,
  FinancialSettlementReportProjectDebtStatusStatistics,
  IUserPermission,
} from '@/types';
import { useFormContext, useWatch } from 'react-hook-form';

import { CellContext, ColumnDef } from '@tanstack/react-table';

import {
  DataTable,
  DataTableRowActions,
  EditableDropdownCell,
  EditableInputCell,
  NumberCell,
} from '@/components/data-table';
import { useBoolean } from '@/hooks';
import { translationWithNamespace } from '@/lib/i18nUtils';
import { getRandomNumber } from '@/lib/number';
import { useMemo, useState } from 'react';
import { FinancialSettlementReportProjectDebtStatusStatisticsReportDetails } from './financial-settlement-report-project-debt-status-statistics-report-details';

const [defaultRow] =
  defaultValuesFinancialSettlementReport.financialSettlementReportProjectDebtStatusStatistics;

type FinancialSettlementReportProjectDebtStatusStatisticsEditableTableProps = {
  role?: IUserPermission;
  calculateForm?: () => void;
};

const t = translationWithNamespace('financialSettlementReport');

export const FinancialSettlementReportProjectDebtStatusStatisticsEditableTable = ({
  role,
  calculateForm,
}: FinancialSettlementReportProjectDebtStatusStatisticsEditableTableProps) => {
  const {
    setValue,
    control,
    formState: { errors },
  } = useFormContext<FinancialSettlementReport>();

  const [editableData] = useWatch({
    control,
    name: ['financialSettlementReportProjectDebtStatusStatistics'],
  });
  const [contractorId, setContractorId] = useState(0);
  const [costItemId, setCostItemId] = useState(0);
  const { state: open, toggle } = useBoolean();

  const financialSettlementReportProjectDebtStatusStatisticsEditableColumns: ColumnDef<FinancialSettlementReportProjectDebtStatusStatistics>[] =
    useMemo(
      () => [
        {
          id: 'contractorId', // Tên cá nhân, đơn vị thực hiện
          accessorKey: 'contractorId',
          header: t('fields.financialSettlementReportProjectDebtStatusStatistics.contractorId'),
          cell: (
            props: CellContext<FinancialSettlementReportProjectDebtStatusStatistics, unknown>
          ) => (
            <EditableDropdownCell
              {...props}
              model="contractor"
              queryKey={[QUERIES.CONTRACTOR]}
              disabled={true}
            />
          ),
        },

        {
          id: 'costItemId', // Nội dung công việc, hợp đồng thực hiện
          accessorKey: 'costItemId',
          header: t('fields.financialSettlementReportProjectDebtStatusStatistics.costItemId'),
          cell: (
            props: CellContext<FinancialSettlementReportProjectDebtStatusStatistics, unknown>
          ) => (
            <EditableDropdownCell
              {...props}
              model="cost-item"
              queryKey={[QUERIES.COST_ITEM]}
              disabled={true}
            />
          ),
        },

        {
          id: 'proposedSettlementValue', // Giá trị đề nghị quyết toán
          accessorKey: 'proposedSettlementValue',
          header: t(
            'fields.financialSettlementReportProjectDebtStatusStatistics.proposedSettlementValue'
          ),
          cell: props => <EditableInputCell {...props} type="number" />,
          aggregatedCell: NumberCell,
        },

        {
          id: 'disbursedCapital', // Vốn đã giải ngân
          accessorKey: 'disbursedCapital',
          header: t('fields.financialSettlementReportProjectDebtStatusStatistics.disbursedCapital'),
          cell: props => <EditableInputCell {...props} type="number" />,
          aggregatedCell: NumberCell,
        },

        {
          id: 'payableAmount', // Phải trả
          accessorKey: 'payableAmount',
          header: t('fields.financialSettlementReportProjectDebtStatusStatistics.payableAmount'),
          cell: props => <EditableInputCell {...props} type="number" />,
          aggregatedCell: NumberCell,
        },

        {
          id: 'receivableAmount', // Phải thu
          accessorKey: 'receivableAmount',
          header: t('fields.financialSettlementReportProjectDebtStatusStatistics.receivableAmount'),
          cell: props => <EditableInputCell {...props} type="number" />,
          aggregatedCell: NumberCell,
        },

        {
          id: 'note', // Ghi chú
          accessorKey: 'note',
          header: t('fields.financialSettlementReportProjectDebtStatusStatistics.note'),
          cell: props => <EditableInputCell {...props} />,
        },
        {
          id: 'removeRow',
          header: '',
          size: 10,
          cell: props => {
            return (
              <DataTableRowActions
                onEdit={() => {
                  const { contractorId, costItemId } = props.row.original;
                  if (contractorId) setContractorId(contractorId);
                  if (costItemId) setCostItemId(costItemId);
                  toggle();
                }}
                canEdit={true}
              />
            );
          },
        },
      ],
      [toggle]
    );

  return (
    <div>
      <DataTable
        tableId={TABLES.FINANCIAL_SETTLEMENT_REPORT_PROJECT_DEBT_STATUS_STATISTICS}
        sortColumn="id"
        role={role}
        editableData={editableData}
        setEditableData={editedData => {
          setValue('financialSettlementReportProjectDebtStatusStatistics', editedData);
          calculateForm?.();
        }}
        onAddButtonClick={table => {
          const newRow = { ...defaultRow, id: -getRandomNumber() };
          table.options.meta?.addNewRow(newRow);
        }}
        syncQueryParams={false}
        columns={financialSettlementReportProjectDebtStatusStatisticsEditableColumns}
        customToolbar={() => {
          return (
            <>
              {errors.financialSettlementReportProjectDebtStatusStatistics?.message && (
                <ErrorMessage
                  message={errors.financialSettlementReportProjectDebtStatusStatistics?.message}
                />
              )}
            </>
          );
        }}
      />
      <FinancialSettlementReportProjectDebtStatusStatisticsReportDetails
        open={open}
        toggle={toggle}
        contractorId={contractorId}
        costItemId={costItemId}
      />
    </div>
  );
};
