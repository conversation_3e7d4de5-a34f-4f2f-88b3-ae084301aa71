import { useState, useLayoutEffect } from 'react';

interface ScreenSize {
  width: number;
  height: number;
}

const getScreenSize = (): ScreenSize => {
  return {
    width: window.innerWidth,
    height: window.innerHeight,
  };
};

export const useScreenSize = (): ScreenSize => {
  const [screenSize, setScreenSize] = useState<ScreenSize>(getScreenSize());

  useLayoutEffect(() => {
    const handleResize = () => {
      setScreenSize(getScreenSize());
    };

    window.addEventListener('resize', handleResize);
    window.addEventListener('orientationchange', handleResize);

    // Initial size set
    handleResize();

    return () => {
      window.removeEventListener('resize', handleResize);
      window.removeEventListener('orientationchange', handleResize);
    };
  }, []);

  return screenSize;
};
